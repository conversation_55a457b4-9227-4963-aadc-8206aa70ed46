package com.insurance.rocketmq.dlq;

import com.alibaba.fastjson.JSON;
import com.insurance.rocketmq.msg.OrderMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:订单下单成功消费者
 */
@Component
@Slf4j
@RocketMQMessageListener(
        topic = "%DLQ%insurance_order_success_gray_topic_consumer_group",
        consumerGroup = "dlq_insurance_order_success_topic_gray_consumer_group",
        consumeMode = ConsumeMode.CONCURRENTLY
)
public class DlqOrderSuccessConsumer implements RocketMQListener<OrderMsg> {
    @Override
    public void onMessage(OrderMsg orderMsg) {
        log.error("订单下单成功死信队列消息:{}", JSON.toJSONString(orderMsg));
    }
}
