package com.apps.service;

import com.apps.api.dto.VerifyUserDto;
import com.apps.api.dto.coupon.SaveCouponUserDto;
import com.apps.api.dto.partner.SavePartnerUserDto;
import com.apps.api.dto.partner.UpdatePartnerLockDto;
import com.apps.api.dto.system.ResetPasswordDto;
import com.apps.api.dto.system.SaveUserDto;
import com.apps.api.dto.system.UpdateUserInfoDto;
import com.apps.api.dto.system.UpdateUserPasswordDto;
import com.apps.api.dto.wechat.WxGetPhoneDto;
import com.apps.api.entity.SystemUserEntity;
import com.apps.api.entity.SystemUserPlatformLoginEntity;
import com.apps.api.vo.system.SystemUserDetailVo;
import com.apps.api.vo.system.SystemUserVo;
import com.apps.api.vo.system.UserPermissionVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemUserService extends IService<SystemUserEntity> {

    /**
     * 保存用户
     * @param saveUserDto
     * @return
     */
    Long saveUser(SaveUserDto saveUserDto);

    /**
     * 根据用户Id获取用户信息
     * @param userId
     * @return
     */
    SystemUserDetailVo getUser(Long userId);

    /**
     * 保存用户微信手机号
     * @param wxGetPhoneDto
     * @return
     */
    Boolean saveUserWxPhone(WxGetPhoneDto wxGetPhoneDto);

    /**
     * 校验登录用户状态
     * @param userId
     * @return
     */
    Boolean checkLoginUser(Long userId);

    /**
     * 根据用户Id获取用户信息
     * @param userId
     * @return
     */
    UserPermissionVo getUserCacheInfo(Boolean getBusinessData, SystemUserPlatformLoginEntity loginEntity, Long userId);

    /**
     * 获取用户权限
     * @param userId
     * @return
     */
    UserPermissionVo getUserPermission(Long userId);

    /**
     * 保存伙伴用户
     *
     * @param partnerUserDto
     * @return
     */
    Long savePartnerUser(SavePartnerUserDto partnerUserDto);

    /**
     * 修改用户账号状态
     * @param lockDto
     */
    void lockUser(UpdatePartnerLockDto lockDto);

    /**
     * 根据平台Id和角色Code获取用户Id列表
     * @param platformId
     * @param platformCode
     * @param roleCodes
     * @return
     */
    List<Long> getPlatformUserByRole(Long platformId, String platformCode, List<String> roleCodes);

    /**
     * 保存优惠券用户
     * @param couponUserDto
     * @return
     */
    Long saveCouponUser(SaveCouponUserDto couponUserDto);

    /**
     * 校验用户是否存在
     *
     * @param mobile
     * @param platformCode
     * @param platformId
     * @return
     */
    Integer verifyUserSmsCode(String mobile, String platformCode, Long platformId,String captcha);

    /**
     * 修改用户密码
     * @param userPasswordDto
     */
    void updateUserPassword(UpdateUserPasswordDto userPasswordDto);

    /**
     * 校验用户登录权限-登录前校验账号
     * @param verifyUserDto
     * @return
     */
    Boolean verifyUserBeforeLogin(VerifyUserDto verifyUserDto);


    /**
     * 修改用户信息
     * @param updateUserInfoDto
     * @return
     */
    SystemUserVo updateUser(UpdateUserInfoDto updateUserInfoDto);


    /**
     * 重置伙伴用户密码
     * @param resetPasswordDto
     */
    void resetPartnerUserPassword(ResetPasswordDto resetPasswordDto);
}
