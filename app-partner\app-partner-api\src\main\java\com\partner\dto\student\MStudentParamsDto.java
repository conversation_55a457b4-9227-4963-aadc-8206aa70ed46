package com.partner.dto.student;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "学生查询参数")
public class MStudentParamsDto {

    @Schema(description = "公司ID")
    private Long companyId;

   /* @Schema(description = "代理UUID")
    @NotBlank(message = "代理UUID不能为空")
    private String agentUUID;*/

    @Schema(description = "查询类型 0按人查询/1按申请项目 查询")
    @NotNull(message = "查询类型不能空")
    private int searchType;

    @Schema(description = "国家ID")
    private Long countryId;
    @Schema(description = "当前申请状态步骤StepId")
    private Long studentOfferItemStepId;
    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "年度")
    private int year;


    @Schema(description = "学生UUID-用于查详情")
    private String studentUUID;

    @Schema(description = "角色编码")
    private String roleCode;


}
