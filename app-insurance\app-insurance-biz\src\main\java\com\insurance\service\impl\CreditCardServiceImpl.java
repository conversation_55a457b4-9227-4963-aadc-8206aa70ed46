package com.insurance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.insurance.config.JsonRedisService;
import com.insurance.config.RedisService;
import com.insurance.constant.RedisConstant;
import com.insurance.dto.order.AllianFormData;
import com.insurance.dto.order.CreditCardEncryptionData;
import com.insurance.dto.order.NibFormData;
import com.insurance.dto.order.NibLoginFormData;
import com.insurance.entity.CreditCard;
import com.insurance.entity.InsuranceOrder;
import com.insurance.enums.*;
import com.insurance.event.publisher.SendNotifyMessageEventPublisher;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.mapper.CreditCardMapper;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.service.CreditCardService;
import com.insurance.service.CreditCardStatementService;
import com.insurance.service.InsuranceOrderService;
import com.insurance.util.DateFormatUtils;
import com.insurance.util.EncryptionUtils;
import com.insurance.util.ExchangeRateUtils;
import com.insurance.util.SecureEncryptUtil;
import com.insurance.vo.settlement.RateDetail;
import com.payment.enums.PayStatusEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class CreditCardServiceImpl extends ServiceImpl<CreditCardMapper, CreditCard> implements CreditCardService {

    @Autowired
    private CreditCardMapper creditCardMapper;
    @Autowired
    private InsuranceOrderMapper insuranceOrderMapper;
    @Autowired
    @Lazy
    private InsuranceOrderService insuranceOrderService;
    @Autowired
    private JsonRedisService jsonRedisService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private CreditCardStatementService creditCardStatementService;
    @Autowired
    private ExchangeRateUtils exchangeRateUtils;
    @Autowired
    private SendNotifyMessageEventPublisher notifyMessageEventPublisher;
    @Autowired
    private EncryptionUtils encryptionUtils;

    @Override
    @SneakyThrows
    public CreditCard getAvailableCreditCard(String orderNo, String secret) {
        InsuranceOrder insuranceOrder = insuranceOrderMapper.selectByOrderNum(orderNo);
        if (Objects.isNull(insuranceOrder)) {
            log.error("使用公司信用卡支付失败,订单不存在,订单编号:{}", orderNo);
            return null;
        }

        BigDecimal price = insuranceOrder.getInsuranceAmount();
        LocalDate today = LocalDate.now();
        //获取可用的信用卡
        //1：已激活 2：有效期内 3：当前额度充足(大于支付金额)
        CreditCard creditCard = creditCardMapper.selectOne(new LambdaQueryWrapper<CreditCard>()
                .eq(CreditCard::getIsActive, 1)
                .ge(CreditCard::getExpirationDate, today)
                .ge(CreditCard::getCurrentAmount, price)
                .orderByDesc(CreditCard::getViewOrder)
                .last("LIMIT 1"));
        if (Objects.nonNull(creditCard)) {
            //解密
            String cardNumPlaintext = SecureEncryptUtil.decrypt(creditCard.getCardNum(), secret);
            String safetyCodePlaintext = SecureEncryptUtil.decrypt(creditCard.getSafetyCode(), secret);
            creditCard.setCardNumPlaintext(cardNumPlaintext);
            creditCard.setSafetyCodePlaintext(safetyCodePlaintext);
            return creditCard;
        }

        log.error("没有可用的信用卡,价格:{},当前日期:{}", price, today);
        //todo全部都不可用-暂定逻辑
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendCreditCardOrderRequest(String orderNo) {
        //获取解密密钥
        String secret = encryptionUtils.getSecret();
        if (StringUtils.isBlank(secret)) {
            log.error("使用公司信用卡支付失败,密钥为空");
            throw new InsuranceGlobalException("使用公司信用卡支付失败,密钥为空");
        }
        //1:获取可用的信用卡
        CreditCard creditCard = getAvailableCreditCard(orderNo, secret);
        if (Objects.isNull(creditCard)) {
            log.error("使用公司信用卡支付失败,没有可用的信用卡,订单号:{}", orderNo);
            throw new InsuranceGlobalException("使用公司信用卡支付失败,没有可用的信用卡");
        }
        //2:填充表单信息-卡号信息
        InsuranceOrder order = insuranceOrderMapper.selectByOrderNum(orderNo);
        log.info("使用公司信用卡支付,订单信息:{}", JSONObject.toJSONString(order));
        String orderInfo = initOrderInfo(ProductTypeEnum.getEnumByCode(order.getProductTypeKey()),
                order.getOrderJson(),
                order.getOrderNum(),
                creditCard);
        if (StringUtils.isBlank(orderInfo)) {
            log.error("使用公司信用卡支付失败,表单信息为空,订单号:{}", orderNo);
            throw new InsuranceGlobalException("使用公司信用卡支付失败,表单信息为空");
        }
        //更新订单信息
        order.setOrderJson(orderInfo);
        order.setFkCreditCardId(creditCard.getId());
        order.setMpPaymentStatus(PayStatusEnum.PAY_SUCCESS.getCode());
        insuranceOrderMapper.updateById(order);
        //3：表单信息存入redis
        // 构建 Redis Hash key 和数据
        CreditCardEncryptionData encryptionData = CreditCardEncryptionData.builder()
                .encryptedCardNumber(creditCard.getCardNum())
                .endingNumber(creditCard.getSafetyCode())
                .secretKey(secret)
                .build();

        // 存入 Redis-后续由自动化那边直接读取，可以不用存入redis,测试环境可保留
        redisService.setObjectAsJson(RedisConstant.ORDER_ENCRYPT_CARD + order.getOrderNum(), encryptionData, 1, TimeUnit.DAYS);

        //将订单信息放入缓存-key就是密钥+"-"+订单号
        jsonRedisService.setObjectAsJson(RedisConstant.ORDER_INFO + secret + "|" + order.getOrderNum()
                , orderInfo, 1, TimeUnit.DAYS);
        //4：发送下单请求
        insuranceOrderService.sendRequest(orderNo);
        // 5. 删除 Redis 中的关单 key，避免被 RocketMQ 延时消息关闭
        redisService.del(RedisConstant.CLOSE_ORDER + order.getOrderNum());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void CreditCardPaySuccess(InsuranceOrder order) {
        CreditCard creditCard = checkCreditCard(order);
        if (Objects.isNull(creditCard)) {
            return;
        }
        //1:更新信用卡额度
        BigDecimal amount = order.getInsuranceAmount();
        //获取汇率-下单币种转信用卡币种
        RateDetail rateDetail = exchangeRateUtils.getRateDetail(order.getFkCurrencyTypeNum(), creditCard.getFkCurrencyTypeNum());
        //信用卡本次支出的金额-按照信用卡币种
        BigDecimal expenditureAmount = amount.multiply(rateDetail.getRate()).setScale(2, RoundingMode.HALF_UP);
        creditCard.setCurrentAmount(creditCard.getCurrentAmount().subtract(expenditureAmount));
        creditCardMapper.updateById(creditCard);

        //2：添加信用卡消费记录-成功记录
        creditCardStatementService.createCreditCardStatement(creditCard,
                order,
                CreditCardStatementBusinessTypeEnum.EXPENSES.getCode(),
                CreditCardStatementRelateTypeEnum.INSURANCE_ORDER.getCode(),
                order.getId(),
                CreditCardPayStatusEnum.SUCCESS.getCode());
        //3:发送额度提醒相应的通知
        notifyMessageEventPublisher.publishPartnerUserOfflineEvent(creditCard.getId(), NotifyMessageTypeEnum.QUOTA_REMIND.getCode(), "", new Date());
        //4:发送交易通知
        notifyMessageEventPublisher.publishPartnerUserOfflineEvent(creditCard.getId(), NotifyMessageTypeEnum.TRANSACTION_REMIND.getCode(), order.getOrderNum(), new Date());
    }

    @Override
    public void CreditCardPayFail(InsuranceOrder order) {
        //1:添加信用卡消费记录-失败记录
        CreditCard creditCard = checkCreditCard(order);
        if (Objects.isNull(creditCard)) {
            return;
        }
        creditCardStatementService.createCreditCardStatement(creditCard,
                order,
                CreditCardStatementBusinessTypeEnum.EXPENSES.getCode(),
                CreditCardStatementRelateTypeEnum.INSURANCE_ORDER.getCode(),
                order.getId(),
                CreditCardPayStatusEnum.FAIL.getCode());
        //2:若有配置支付失败提醒，发送相应的通知
        notifyMessageEventPublisher.publishPartnerUserOfflineEvent(creditCard.getId(),
                NotifyMessageTypeEnum.FAIL_REMIND.getCode(),
                order.getOrderNum(),
                new Date());
    }

    @Override
    public void sendRepaymentNotify(Integer type) {
        //1：账单提醒 2：还款提醒
        List<Long> creditCardIds = creditCardMapper.selectRemindCreditCardIds(type);
        log.info("====>发送信用卡账单提醒,类型:{},信用卡Ids:{}", type, creditCardIds);
        if (CollectionUtils.isNotEmpty(creditCardIds)) {
            for (Long creditCardId : creditCardIds) {
                notifyMessageEventPublisher.publishPartnerUserOfflineEvent(creditCardId,
                        type.equals(1) ? NotifyMessageTypeEnum.DISBURSEMENT_REMIND.getCode() : NotifyMessageTypeEnum.REPAYMENT_REMIND.getCode(),
                        "",
                        new Date());
            }
        }
        log.info("====>发送信用卡账单提醒结束");
    }

    @Override
    public void sendQuotaRemindNotify() {
        List<Long> creditCardIds = creditCardMapper.selectQuotaRemindCreditCardIds();
        log.info("====>发送信用卡额度提醒,信用卡Ids:{}", creditCardIds);
        if (CollectionUtils.isNotEmpty(creditCardIds)) {
            for (Long creditCardId : creditCardIds) {
                notifyMessageEventPublisher.publishPartnerUserOfflineEvent(creditCardId,
                        NotifyMessageTypeEnum.QUOTA_REMIND.getCode(),
                        "",
                        new Date());
            }
        }
        log.info("====>发送信用卡额度提醒结束");
    }

    private CreditCard checkCreditCard(InsuranceOrder order) {
        if (Objects.isNull(order.getFkCreditCardId())) {
            log.error("订单未关联信用卡，订单号：{}", order.getOrderNum());
            return null;
        }
        CreditCard creditCard = creditCardMapper.selectById(order.getFkCreditCardId());
        if (Objects.isNull(creditCard)) {
            log.error("订单关联的信用卡不存在，订单号：{},信用卡ID:{}", order.getOrderNum(), order.getFkCreditCardId());
            return null;
        }
        return creditCard;
    }

    private String initOrderInfo(ProductTypeEnum type, String formData, String orderNo, CreditCard creditCard) {
        log.info("使用公司信用卡下单,初始化订单信息===>type:{},orderNo:{},creditCardId:{}", type, orderNo, creditCard.getId());
        if (StringUtils.isBlank(formData)) {
            log.error("表单数据为空,订单号:{}", orderNo);
            throw new InsuranceGlobalException(500, "信用卡下单失败,表单数据为空");
        }
        //0:订单号 1:卡号 2:安全码 3:持卡人姓名 4:有效期
        switch (type) {
            case NIB:
                log.info("======>使用公司信用卡下单,初始化NIB订单信息");
                NibFormData nibFormData = JSONObject.parseObject(formData, NibFormData.class);
                nibFormData.setOrderNo(orderNo);
                nibFormData.getStep4().setCard_number(creditCard.getCardNum());
                nibFormData.getStep4().setCvv(creditCard.getSafetyCode());
                nibFormData.getStep4().setCardholder_name(creditCard.getHolderName());
                //有效期:mm/yyyy格式
                nibFormData.getStep4().setExpiry_date(DateFormatUtils.formatToMonthYear(creditCard.getExpirationDate()));
                return JSONObject.toJSONString(nibFormData);
            case ALLIAN:
                log.info("======>使用公司信用卡下单,初始化安联订单信息");
                AllianFormData allianFormData = JSONObject.parseObject(formData, AllianFormData.class);
                allianFormData.setOrderNo(orderNo);
                allianFormData.getStep3().setPolicy_agreement(1);
                allianFormData.getStep3().setMarket_consent(0);
                allianFormData.getStep3().setLast_name("");
                allianFormData.getStep3().setCardholder_name(creditCard.getHolderName());
                allianFormData.getStep3().setCard_number(creditCard.getCardNum());
                allianFormData.getStep3().setCvc(creditCard.getSafetyCode());
                // 有效期-月份
                allianFormData.getStep3().setExpiration_MM(DateFormatUtils.formatToMonthOnly(creditCard.getExpirationDate()));
                // 有效期-年份
                allianFormData.getStep3().setExpiration_YYYY(DateFormatUtils.formatToYearOnly(creditCard.getExpirationDate()));
                return JSONObject.toJSONString(allianFormData);
            case NIB_LOGIN:
                log.info("======>使用公司信用卡下单初始化,NIB登录订单信息");
                NibLoginFormData nibLoginFormData = JSONObject.parseObject(formData, NibLoginFormData.class);
                nibLoginFormData.setOrderNo(orderNo);
                nibLoginFormData.getStep4().setCard_number(creditCard.getCardNum());
                nibLoginFormData.getStep4().setCvv(creditCard.getSafetyCode());
                nibLoginFormData.getStep4().setCardholder_name(creditCard.getHolderName());
                //有效期:mm/yyyy格式
                nibLoginFormData.getStep4().setExpiry_date(DateFormatUtils.formatToMonthYear(creditCard.getExpirationDate()));
                return JSONObject.toJSONString(nibLoginFormData);
            default:
                return null;
        }
    }
}
