package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
@Data
@TableName("s_media_and_attached ")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MediaAndAttached extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文件guid(文档中心)")
    private String fkFileGuid;

    @Schema(description = "表名")
    private String fkTableName;

    @Schema(description = "表Id")
    private Long fkTableId;

    @Schema(description = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @Schema(description = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;

    @Schema(description = "链接")
    private String link;

    @Schema(description = "备注")
    private String remark;
}
