package com.coupon.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.coupon.dto.AddCouponTypeDto;
import com.coupon.dto.CouponQuotaDto;
import com.coupon.dto.CouponTakenRecDto;
import com.coupon.dto.CouponTypeDto;
import com.coupon.entity.*;
import com.coupon.mapper.*;
import com.coupon.service.ICouponManageService;
import com.coupon.service.ITencentCloudService;
import com.coupon.util.AppendixUtils;
import com.coupon.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CouponManageServiceImpl implements ICouponManageService {
    @Resource
    private MCouponTypeMapper couponTypeMapper;

    @Resource
    private MFileCouponMapper fileCouponMapper;

    @Resource
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Resource
    private MCouponFetchQuotaMapper couponFetchQuotaMapper;

    @Resource
    private MCouponMapper couponMapper;

    @Resource
    private RCouponFetchMapper rCouponFetchMapper;

    @Resource
    private MUserMapper userMapper;

    @Resource
    private MCouponFetchQuotaMapper mCouponFetchQuotaMapper;

    @Resource
    private Environment env;

    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    //文件存储
    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;

    @Resource
    private ITencentCloudService tencentCloudService;

    @Override
    public Long addNewCouponType(AddCouponTypeDto addCouponTypeDto) throws Exception {
        // 插入数据库
        MCouponTypeEntity couponTypeEntity = new MCouponTypeEntity();
        couponTypeEntity.setTitle(addCouponTypeDto.getTitle());
        couponTypeEntity.setDescription(java.net.URLDecoder.decode(addCouponTypeDto.getDescription(), "utf-8"));
        couponTypeEntity.setPrice(addCouponTypeDto.getPrice());
        couponTypeEntity.setSubTitle(addCouponTypeDto.getSubTitle());
        couponTypeEntity.setUuid(UUID.randomUUID().toString().replaceAll("-", ""));
        couponTypeEntity.setIsActive(addCouponTypeDto.isActive());
        couponTypeEntity.setValidPeriodStart(addCouponTypeDto.getValidPeriodStart());
        couponTypeEntity.setValidPeriodEnd(addCouponTypeDto.getValidPeriodEnd());
        couponTypeEntity.setRecommendedType(addCouponTypeDto.getRecommendedType());
        couponTypeEntity.setDiscountMethod(addCouponTypeDto.getDiscountMethod());
        couponTypeEntity.setCodeImage(addCouponTypeDto.getCodeImage());
        couponTypeEntity.setRedeemPeriodStart(addCouponTypeDto.getRedeemPeriodStart());
        couponTypeEntity.setRedeemPeriodEnd(addCouponTypeDto.getRedeemPeriodEnd());
        couponTypeEntity.setRuleTitle(addCouponTypeDto.getRuleTitle());
        couponTypeEntity.setViewOrder(addCouponTypeDto.getViewOrder());
        // 如果置顶
//        QueryWrapper<MCouponTypeEntity> topQueryWrapper = new QueryWrapper<>();
//        List<MCouponTypeEntity> topList = couponTypeMapper.selectList(topQueryWrapper);
//        if (addCouponTypeDto.getIsTop()) {
//            for (MCouponTypeEntity mCouponTypeEntity : topList) {
//                mCouponTypeEntity.setViewOrder(mCouponTypeEntity.getViewOrder() + 1);
//                couponTypeMapper.updateById(mCouponTypeEntity);
//            }
//            couponTypeEntity.setViewOrder(0L);
//        } else {
//            couponTypeEntity.setViewOrder((long) topList.size());
//        }
        FzhUser fzhUser = SecurityUtils.getUser();
        couponTypeEntity.setGmtCreate(LocalDateTime.now());
        couponTypeEntity.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
        couponTypeMapper.insert(couponTypeEntity);
        //附件
        if (addCouponTypeDto.getFileGuids() != null) {
            for (String guid : addCouponTypeDto.getFileGuids()) {
                SMediaAndAttachedEntity sMediaAndAttachedEntity = new SMediaAndAttachedEntity();
                sMediaAndAttachedEntity.setFkFileGuid(guid);
                sMediaAndAttachedEntity.setFkTableName("m_coupon_type");
                sMediaAndAttachedEntity.setFkTableId(couponTypeEntity.getId());
                sMediaAndAttachedEntity.setGmtCreate(LocalDateTime.now());
                sMediaAndAttachedEntity.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
                sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
            }
        }
        MCouponFetchQuotaEntity couponFetchQuotaEntity = new MCouponFetchQuotaEntity();
        couponFetchQuotaEntity.setFkCouponTypeId(couponTypeEntity.getId());
        couponFetchQuotaEntity.setFkRoleCode("1");
        couponFetchQuotaEntity.setQuota(1);
        couponFetchQuotaEntity.setGmtCreate(LocalDateTime.now());
        couponFetchQuotaEntity.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
        couponFetchQuotaMapper.insert(couponFetchQuotaEntity);
        MCouponFetchQuotaEntity couponFetchQuotaEntity1 = new MCouponFetchQuotaEntity();
        couponFetchQuotaEntity1.setFkCouponTypeId(couponTypeEntity.getId());
        couponFetchQuotaEntity1.setFkRoleCode("2");
        couponFetchQuotaEntity1.setQuota(2);
        couponFetchQuotaEntity1.setGmtCreate(LocalDateTime.now());
        couponFetchQuotaEntity1.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
        couponFetchQuotaMapper.insert(couponFetchQuotaEntity1);
        MCouponFetchQuotaEntity couponFetchQuotaEntity2 = new MCouponFetchQuotaEntity();
        couponFetchQuotaEntity2.setFkCouponTypeId(couponTypeEntity.getId());
        couponFetchQuotaEntity2.setFkRoleCode("3");
        couponFetchQuotaEntity2.setQuota(2);
        couponFetchQuotaEntity2.setGmtCreate(LocalDateTime.now());
        couponFetchQuotaEntity2.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
        couponFetchQuotaMapper.insert(couponFetchQuotaEntity2);
        return couponTypeEntity.getId();
    }

    @Override
    public IPage<GetAllCouponTypeVo> getAllCouponType(CouponTypeDto couponTypeDto) throws Exception {
        Page page = new Page();
        page.setCurrent(couponTypeDto.getCurrent());
        page.setSize(couponTypeDto.getSize());
        IPage<GetAllCouponTypeVo> getAllCouponTypeVoIPage = couponTypeMapper.getCouponTypePage(page, couponTypeDto);
        List<GetAllCouponTypeVo> getAllCouponTypeVoList = getAllCouponTypeVoIPage.getRecords();
        for (GetAllCouponTypeVo getAllCouponTypeVo : getAllCouponTypeVoList) {
            //填充优惠券封面
            if (getAllCouponTypeVo.getImageGuid() != null) {
                QueryWrapper<MFileCoupon> mFileCouponQueryWrapper = new QueryWrapper<>();
                mFileCouponQueryWrapper.eq("file_guid", getAllCouponTypeVo.getImageGuid());
                List<MFileCoupon> mFileCouponList = fileCouponMapper.selectList(mFileCouponQueryWrapper);
                if (!mFileCouponList.isEmpty()) {
                    getAllCouponTypeVo.setImagePath(mFileCouponList.get(0).getFilePath());
                }
            }

            //填充优惠券二维码
            if (getAllCouponTypeVo.getCodeImageGuid() != null) {
                QueryWrapper<MFileCoupon> mFileCouponQueryWrapper1 = new QueryWrapper<>();
                mFileCouponQueryWrapper1.eq("file_guid", getAllCouponTypeVo.getCodeImageGuid());
                List<MFileCoupon> mFileCouponList = fileCouponMapper.selectList(mFileCouponQueryWrapper1);
                if (!mFileCouponList.isEmpty()) {
                    getAllCouponTypeVo.setCodeImagePath(mFileCouponList.get(0).getFilePath());
                }
            }

            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            if ((getAllCouponTypeVo.getValidPeriodStart() != null && now.isBefore(getAllCouponTypeVo.getValidPeriodStart()))) {
                getAllCouponTypeVo.setPreHot(true);
            }
            if (getAllCouponTypeVo.getValidPeriodEnd() != null && now.isAfter(getAllCouponTypeVo.getValidPeriodEnd())) {
                getAllCouponTypeVo.setLapse(true);
            }

            QueryWrapper<MCouponEntity> mCouponEntityQueryWrapper = new QueryWrapper<>();
            mCouponEntityQueryWrapper.eq("fk_coupon_type_id", getAllCouponTypeVo.getCouponTypeId());
            mCouponEntityQueryWrapper.eq("is_taken", false);
            List<MCouponEntity> mCouponEntities = couponMapper.selectList(mCouponEntityQueryWrapper);

            QueryWrapper<MCouponEntity> mCouponEntityQueryWrapper1 = new QueryWrapper<>();
            mCouponEntityQueryWrapper1.eq("fk_coupon_type_id", getAllCouponTypeVo.getCouponTypeId());
            mCouponEntityQueryWrapper1.eq("is_taken", true);
            List<MCouponEntity> mCouponEntities1 = couponMapper.selectList(mCouponEntityQueryWrapper1);


            QueryWrapper<MCouponEntity> mCouponEntityQueryWrapper2 = new QueryWrapper<>();
            mCouponEntityQueryWrapper2.eq("fk_coupon_type_id", getAllCouponTypeVo.getCouponTypeId());
            mCouponEntityQueryWrapper2.eq("is_taken", true);
            mCouponEntityQueryWrapper2.eq("is_used", true);
            List<MCouponEntity> mCouponEntities2 = couponMapper.selectList(mCouponEntityQueryWrapper2);

            QueryWrapper<MCouponEntity> mCouponEntityQueryWrapper3 = new QueryWrapper<>();
            mCouponEntityQueryWrapper3.eq("fk_coupon_type_id", getAllCouponTypeVo.getCouponTypeId());
            List<MCouponEntity> mCouponEntities3 = couponMapper.selectList(mCouponEntityQueryWrapper3);

            getAllCouponTypeVo.setIsUsedNum(mCouponEntities2.size());
            getAllCouponTypeVo.setIsTakenNum(mCouponEntities1.size());
            getAllCouponTypeVo.setRemainderNum(mCouponEntities.size());
            getAllCouponTypeVo.setAllCouponNum(mCouponEntities3.size());
        }
        return getAllCouponTypeVoIPage;
    }

    @Override
    public List<UploadAttached> uploadAttached(MultipartFile[] files) throws Exception {
        String annexSavePath = env.getProperty("annex-save-path");
        List<UploadAttached> uploadAttachedList = new ArrayList<>();
        FzhUser fzhUser = SecurityUtils.getUser();
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                throw new Exception("文件不能为空");
            }
            // 源文件名
            String fileNameOrc = file.getOriginalFilename();
            int i = fileNameOrc.lastIndexOf(".");
            // 获取后缀名
            String fileTypeOrc = fileNameOrc.substring(i, fileNameOrc.length()).toLowerCase();
            // 文件guid
            String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");

            String fileurl = subString(AppendixUtils.getFileHtiPath(file));
            int j = fileurl.lastIndexOf("/");
            // 新文件名
            String fileName = fileurl.substring(j + 1, fileurl.length());
            MFileCoupon fileCoupon = new MFileCoupon();
            fileCoupon.setFilePath(fileurl);
            fileCoupon.setFileNameOrc(fileNameOrc);
            fileCoupon.setFileTypeOrc(fileTypeOrc);
            fileCoupon.setFileName(fileName);
            fileCoupon.setFileGuid(fileGuid);
            fileCoupon.setGmtCreate(LocalDateTime.now());
            fileCoupon.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
            tencentCloudService.uploadObject(true, imageBucketName, file, fileurl);
            fileCoupon.setFileKey(fileurl);
            fileCouponMapper.insert(fileCoupon);
            UploadAttached uploadAttached = new UploadAttached();
            uploadAttached.setFileGuid(fileGuid);
            uploadAttached.setFilePath(fileurl);
            uploadAttachedList.add(uploadAttached);
        }
        return uploadAttachedList;
    }

    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/file-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }

    @Override
    public void deleteCouponType(Long id) throws Exception {
        QueryWrapper<MCouponTypeEntity> mCouponTypeEntityQueryWrapper = new QueryWrapper<>();
        mCouponTypeEntityQueryWrapper.eq("id", id);
        couponTypeMapper.delete(mCouponTypeEntityQueryWrapper);
        QueryWrapper<MCouponEntity> mCouponEntityQueryWrapper = new QueryWrapper<>();
        mCouponEntityQueryWrapper.eq("fk_coupon_type_id", id);
        couponMapper.delete(mCouponEntityQueryWrapper);
        QueryWrapper<RCouponFetchEntity> rCouponFetchEntityQueryWrapper = new QueryWrapper<>();
        rCouponFetchEntityQueryWrapper.eq("fk_coupon_type_id", id);
        rCouponFetchMapper.delete(rCouponFetchEntityQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCouponType(CouponTypeDto couponTypeDto) throws Exception {
        FzhUser fzhUser = SecurityUtils.getUser();
        QueryWrapper<MCouponTypeEntity> mCouponTypeEntityQueryWrapper = new QueryWrapper<>();
        mCouponTypeEntityQueryWrapper.eq("uuid", couponTypeDto.getUuid());
        MCouponTypeEntity mCouponTypeEntity = couponTypeMapper.selectOne(mCouponTypeEntityQueryWrapper, false);
        if (Objects.isNull(mCouponTypeEntity)) {
            log.error("优惠卷不存在,uuid:{}", couponTypeDto.getUuid());
            return;
        }
        mCouponTypeEntity.setDiscountMethod(couponTypeDto.getDiscountMethod());
        mCouponTypeEntity.setTitle(couponTypeDto.getTitle());
        mCouponTypeEntity.setSubTitle(couponTypeDto.getSubTitle());
        mCouponTypeEntity.setDescription(java.net.URLDecoder.decode(couponTypeDto.getDescription(), "utf-8"));
        mCouponTypeEntity.setPrice(couponTypeDto.getPrice());
        mCouponTypeEntity.setIsActive(couponTypeDto.isActive());
        mCouponTypeEntity.setValidPeriodStart(couponTypeDto.getValidPeriodStart());
        mCouponTypeEntity.setValidPeriodEnd(couponTypeDto.getValidPeriodEnd());
        mCouponTypeEntity.setRedeemPeriodStart(couponTypeDto.getRedeemPeriodStart());
        mCouponTypeEntity.setRedeemPeriodEnd(couponTypeDto.getRedeemPeriodEnd());
        mCouponTypeEntity.setCodeImage(couponTypeDto.getCodeImageGuid());
        mCouponTypeEntity.setRecommendedType(couponTypeDto.getRecommendedType());
        mCouponTypeEntity.setGmtModified(LocalDateTime.now());
        mCouponTypeEntity.setGmtModifiedUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
        mCouponTypeEntity.setRuleTitle(couponTypeDto.getRuleTitle());
        mCouponTypeEntity.setViewOrder(couponTypeDto.getViewOrder());
        //修改优惠卷图片
        if (CollectionUtils.isNotEmpty(couponTypeDto.getFileGuids())) {
            String fileGuid = couponTypeDto.getFileGuids().get(0);
            SMediaAndAttachedEntity attach = sMediaAndAttachedMapper.selectList(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                            .eq(SMediaAndAttachedEntity::getFkTableName, "m_coupon_type")
                            .eq(SMediaAndAttachedEntity::getFkTableId, mCouponTypeEntity.getId())
                            .orderByDesc(SMediaAndAttachedEntity::getId))
                    .stream().findFirst().orElse(null);
            if (Objects.nonNull(attach) && !fileGuid.equals(attach.getFkFileGuid())) {
                attach.setFkFileGuid(fileGuid);
                sMediaAndAttachedMapper.updateById(attach);
            }
        }
        couponTypeMapper.updateById(mCouponTypeEntity);
    }

    @Override
    public void addCouponQuota(CouponQuotaDto couponQuotaDto) throws Exception {
        FzhUser fzhUser = SecurityUtils.getUser();
        QueryWrapper<MCouponFetchQuotaEntity> mCouponFetchQuotaEntityQueryWrapper = new QueryWrapper<>();
        mCouponFetchQuotaEntityQueryWrapper.eq("fk_role_code", couponQuotaDto.getRoleCode());
        mCouponFetchQuotaEntityQueryWrapper.eq("fk_coupon_type_id", couponQuotaDto.getCouponTypeId());
        List<MCouponFetchQuotaEntity> mCouponTypeEntities = couponFetchQuotaMapper.selectList(mCouponFetchQuotaEntityQueryWrapper);
        if (mCouponTypeEntities != null && !mCouponTypeEntities.isEmpty()) {
            throw new Exception("优惠卷已存在");
        }
        MCouponFetchQuotaEntity mCouponFetchQuotaEntity = new MCouponFetchQuotaEntity();
        mCouponFetchQuotaEntity.setFkCouponTypeId(couponQuotaDto.getCouponTypeId());
        mCouponFetchQuotaEntity.setFkRoleCode(couponQuotaDto.getRoleCode());
        mCouponFetchQuotaEntity.setQuota(couponQuotaDto.getQuota());
        mCouponFetchQuotaEntity.setGmtCreate(LocalDateTime.now());
        mCouponFetchQuotaEntity.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
        couponFetchQuotaMapper.insert(mCouponFetchQuotaEntity);
    }

    @Override
    public IPage<GetAllCouponQuotaVo> getAllCouponQuoTa(CouponQuotaDto couponQuotaDto) throws Exception {
        Page page = new Page();
        page.setCurrent(couponQuotaDto.getCurrent());
        page.setSize(couponQuotaDto.getSize());
        return couponFetchQuotaMapper.getAllCouponQuotaVo(page, couponQuotaDto);
    }

    @Override
    public void deleteCouponQuota(Long id) throws Exception {
        couponFetchQuotaMapper.deleteById(id);
    }

    @Override
    public void updateCouponQuota(CouponQuotaDto couponQuotaDto) throws Exception {
        QueryWrapper<MCouponFetchQuotaEntity> mCouponFetchQuotaEntityQueryWrapper = new QueryWrapper<>();
        mCouponFetchQuotaEntityQueryWrapper.eq("fk_coupon_type_id", couponQuotaDto.getCouponTypeId());
        mCouponFetchQuotaEntityQueryWrapper.eq("fk_role_code", couponQuotaDto.getRoleCode());
        MCouponFetchQuotaEntity mCouponFetchQuotaEntity = couponFetchQuotaMapper.selectOne(mCouponFetchQuotaEntityQueryWrapper);
        mCouponFetchQuotaEntity.setQuota(couponQuotaDto.getQuota());
        couponFetchQuotaMapper.updateById(mCouponFetchQuotaEntity);
    }

    @Override
    public R couponTakenRec(CouponTakenRecDto couponTakenRecDto) throws Exception {
        QueryWrapper<MUserEntity> userEntityQueryWrapper = new QueryWrapper<>();
        if (couponTakenRecDto.getMobile() != null && !couponTakenRecDto.getMobile().isEmpty()) {
            userEntityQueryWrapper.like("mobile", couponTakenRecDto.getMobile());
        }
        if (couponTakenRecDto.getName() != null && !couponTakenRecDto.getName().isEmpty()) {
            userEntityQueryWrapper.like("name", couponTakenRecDto.getName());
        }
        if (couponTakenRecDto.getRole() != null && !couponTakenRecDto.getRole().isEmpty()) {
            userEntityQueryWrapper.eq("role", couponTakenRecDto.getRole());
        }
        List<MUserEntity> userEntities = userMapper.selectList(userEntityQueryWrapper);
        List<CouponManageTakeRecVo> couponManageTakeRecVos = new ArrayList<>();
        for (MUserEntity userEntity : userEntities) {
            QueryWrapper<RCouponFetchEntity> rCouponFetchEntityQueryWrapper = new QueryWrapper<>();
            rCouponFetchEntityQueryWrapper.eq("fk_coupon_user_id", userEntity.getId());
            if (couponTakenRecDto.getIsUsed() != null) {
                rCouponFetchEntityQueryWrapper.eq("is_used", couponTakenRecDto.getIsUsed());
            }
            if (couponTakenRecDto.getCode() != null && !couponTakenRecDto.getCode().isEmpty()) {
                rCouponFetchEntityQueryWrapper.like("fk_coupon_code", couponTakenRecDto.getCode());
            }
            List<RCouponFetchEntity> mCouponFetchQuotaEntities = rCouponFetchMapper.selectList(rCouponFetchEntityQueryWrapper);
            // 根据 fkCouponTypeId 分组
            Map<Long, List<RCouponFetchEntity>> groupedByCouponTypeId = mCouponFetchQuotaEntities.stream()
                    .collect(Collectors.groupingBy(RCouponFetchEntity::getFkCouponTypeId));
            for (Map.Entry<Long, List<RCouponFetchEntity>> entry : groupedByCouponTypeId.entrySet()) {

                Long couponTypeId = entry.getKey();
                List<RCouponFetchEntity> entities = entry.getValue();
                QueryWrapper<MCouponTypeEntity> mCouponTypeEntityQueryWrapper = new QueryWrapper<>();
                mCouponTypeEntityQueryWrapper.eq("id", couponTypeId);
                MCouponTypeEntity mCouponTypeEntity = couponTypeMapper.selectOne(mCouponTypeEntityQueryWrapper);
                if (mCouponTypeEntity == null) {
                    continue;
                }
                CouponManageTakeRecVo couponManageTakeRecVo = new CouponManageTakeRecVo();
                couponManageTakeRecVo.setUserName(userEntity.getNickname());
                couponManageTakeRecVo.setUserMobile(userEntity.getMobile());
                couponManageTakeRecVo.setUserRole(userEntity.getRole());
                couponManageTakeRecVo.setCouponTitle(mCouponTypeEntity.getTitle());
                couponManageTakeRecVo.setCouponTakenRecList(entities);
                couponManageTakeRecVo.setCouponNum(entities.size());
                if (userEntity.getBdCode() != null && !userEntity.getBdCode().isEmpty()) {
                    BDInfo bdInfo = mCouponFetchQuotaMapper.getBDInfo(userEntity.getBdCode());
                    couponManageTakeRecVo.setBdName(bdInfo.getBdName());
                    couponManageTakeRecVo.setAreaName(bdInfo.getAreaName());
                }
                couponManageTakeRecVos.add(couponManageTakeRecVo);
            }
        }
        //排序-按领取的最新一条的时间排序
        couponManageTakeRecVos.sort(Comparator.comparing(
                        (CouponManageTakeRecVo vo) -> vo.getCouponTakenRecList().stream()
                                .map(RCouponFetchEntity::getGmtCreate)
                                .filter(Objects::nonNull)
                                .max(LocalDateTime::compareTo)
                                .orElse(LocalDateTime.MIN)
                ).reversed() // 降序
        );
        return R.restResult(couponManageTakeRecVos, 0, "");
    }

    @Override
    public MCouponTypeEntity getCouponTypeById(String id) {
        return couponTypeMapper.selectOne(new LambdaQueryWrapper<MCouponTypeEntity>()
                .eq(MCouponTypeEntity::getId, id)
                .or()
                .eq(MCouponTypeEntity::getUuid, id), false);
    }
}
