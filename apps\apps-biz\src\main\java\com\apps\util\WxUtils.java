package com.apps.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.exception.AppsGlobalException;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidParameterSpecException;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class WxUtils {

    private static final Logger log = LoggerFactory.getLogger(WxUtils.class);

    public static String getAccessToken(String APP_ID, String APP_SECRET) throws Exception {
        if (StringUtils.isEmpty(APP_ID) || StringUtils.isEmpty(APP_SECRET)) {
            throw new AppsGlobalException(GlobExceptionEnum.MP_SECRET_NOT_EXISTS);
        }
        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + APP_ID + "&secret=" + APP_SECRET;
        URL url = new URL(requestUrl);
        // 打开和URL之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        // 得到请求的输出流对象
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.writeBytes("");
        out.flush();
        out.close();
        // 建立实际的连接
        connection.connect();
        // 定义 BufferedReader输入流来读取URL的响应
        BufferedReader in = null;
        if (requestUrl.contains("nlp")) {
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "GBK"));
        } else {
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        }
        String result = "";
        String getLine;
        while ((getLine = in.readLine()) != null) {
            result += getLine;
        }
        in.close();

        JSONObject jsonObject = JSONObject.parseObject(result);
        log.info("获取access_token结果:{}", jsonObject);
        if (!jsonObject.containsKey("access_token")) {
            log.error("获取access_token失败:{}", jsonObject);
            throw new AppsGlobalException(GlobExceptionEnum.MP_GET_ACCESS_TOKEN_ERROR);
        }
        return jsonObject.getString("access_token");
    }

    /**
     * 获取用户手机号
     * @param code
     * @param accessToken
     * @return
     * @throws Exception
     */
    public static JSONObject getUserPhone(String code, String accessToken) throws Exception {
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(accessToken)) {
            throw new Exception("code或者accessToken参数异常");
        }
        String requestUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;
        URL url = new URL(requestUrl);
        // 打开和 URL 之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);

        // 构造请求 body 的 JSON 参数
        JSONObject requestBody = new JSONObject();
        requestBody.put("code", code); // 将 code 参数放入 JSON 中

        // 发送请求 body
        try (DataOutputStream out = new DataOutputStream(connection.getOutputStream())) {
            out.write(requestBody.toJSONString().getBytes(StandardCharsets.UTF_8));
            out.flush();
        }
        // 建立实际的连接
        connection.connect();
        // 定义 BufferedReader 输入流来读取 URL 的响应
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder result = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }

            // 解析响应结果
            JSONObject jsonObject = JSONObject.parseObject(result.toString());
            log.info("获取用户手机号结果:{}", jsonObject);
            return jsonObject;
        } catch (Exception e) {
            log.error("读取响应失败", e);
            return null;
        }
    }

    /**
     * 解密用户敏感数据获取用户信息
     *
     * @param encryptedData 包括敏感数据在内的完整用户信息的加密数据
     * @param sessionKey    数据进行加密签名的密钥
     * @param iv            加密算法的初始向量
     * @return
     * <AUTHOR>
     */
    public static com.alibaba.fastjson.JSONObject getUserInfo(String encryptedData, String sessionKey, String iv) {
        Base64 base64 = new Base64();
        // 被加密的数据
        byte[] dataByte = base64.decode(encryptedData);
        // 加密秘钥
        byte[] keyByte = base64.decode(sessionKey);
        // 偏移量
        byte[] ivByte = base64.decode(iv);
        try {
            // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, "UTF-8");
                return JSON.parseObject(result);
            }
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
        } catch (NoSuchPaddingException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidParameterSpecException e) {
            log.error(e.getMessage(), e);
        } catch (IllegalBlockSizeException e) {
            log.error(e.getMessage(), e);
        } catch (BadPaddingException e) {
            log.error(e.getMessage(), e);
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            log.error(e.getMessage(), e);
        } catch (InvalidAlgorithmParameterException e) {
            log.error(e.getMessage(), e);
        } catch (NoSuchProviderException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static String encodeParam(String param) {
        try {
            return URLEncoder.encode(param, "UTF-8");
        } catch (Exception e) {
            return param;
        }
    }

    /**
     * GET 请求
     *
     * @param url
     * @param params
     * @param tTypeReference
     * @param <T>
     * @return
     */
    public static  <T> T doRequest(String url,
                            Map<String, String> params,
                            TypeReference<T> tTypeReference) {
        String apiUrl = url;
        String urlParams = "";
        Set<String> keySet = params.keySet();
        for (String paramKey : keySet) {
            urlParams += "&" + paramKey + "=" + encodeParam(params.get(paramKey));
        }
        try {
            String result = doGet(apiUrl + "?" + urlParams, null);
            try {
                T response = JSON.parseObject(result, tTypeReference);
                return response;
            } catch (Exception e) {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public static String doGet(String url, Map<String, String> param) {
        if (StringUtils.isEmpty(url)) {
            return null;
        } else {
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(15000).setConnectTimeout(15000).build();
            CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
            String resultString = "";
            CloseableHttpResponse response = null;

            try {
                URIBuilder builder = new URIBuilder(url);
                if (param != null) {
                    for (String key : param.keySet()) {
                        builder.addParameter(key, (String) param.get(key));
                    }
                }

                URI uri = builder.build();
                HttpGet httpGet = new HttpGet(uri);
                response = httpclient.execute(httpGet);
                if (response.getStatusLine().getStatusCode() == 200) {
                    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                }
            } catch (Exception e) {
                log.error("HttpClientUtil-doGet方法发生异常:", e);
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }

                    httpclient.close();
                } catch (IOException e) {
                    log.error("HttpClientUtil-doGet方法发生异常:", e);
                }

            }

            return resultString;
        }
    }
}
