package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-27 15:24:28
 */

@Data
@TableName("u_area_state")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" u_area_state ")
public class UAreaStateEntity extends Model<UAreaStateEntity>{

  @Schema(description = "州省Id")
  private Long id;
 

  @Schema(description = "国家Id")
  private Long fkAreaCountryId;
 

  @Schema(description = "州省编号")
  private String num;
 

  @Schema(description = "州省名称")
  private String name;
 

  @Schema(description = "州省中文名称")
  private String nameChn;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
