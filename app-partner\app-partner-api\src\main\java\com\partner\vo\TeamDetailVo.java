package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17 下午4:44
 * @Version 1.0
 */
@Data
public class TeamDetailVo {

    @Schema(description = "伙伴用户Id")
    private Long id;

    @Schema(description = "角色ID")
    private Long roleId;


    @Schema(description = "租户Id")
    private Long fkTenantId;


    @Schema(description = "公司Id")
    private Long fkCompanyId;


    @Schema(description = "学生代理Id")
    private Long fkAgentId;


    @Schema(description = "用户Id")
    private Long fkUserId;


    @Schema(description = "姓名（中文）")
    private String name;


    @Schema(description = "姓名（英文）")
    private String nameEn;


    @Schema(description = "昵称")
    private String nickname;


    @Schema(description = "性别：0女/1男")
    private Integer gender;


    @Schema(description = "生日")
    private LocalDateTime birthday;


    @Schema(description = "手机区号")
    private String mobileAreaCode;


    @Schema(description = "移动电话")
    private String mobile;


    @Schema(description = "电话区号")
    private String telAreaCode;


    @Schema(description = "电话")
    private String tel;


    @Schema(description = "邮箱地址")
    private String email;


    @Schema(description = "公司名称")
    private String company;


    @Schema(description = "部门")
    private String department;


    @Schema(description = "职位")
    private String position;


    @Schema(description = "QQ号")
    private String qq;


    @Schema(description = "whatsapp号")
    private String whatsapp;


    @Schema(description = "微信号")
    private String wechat;


    @Schema(description = "微信昵称")
    private String wechatNickname;


    @Schema(description = "微信头像URL")
    private String wechatIconUrl;


    @Schema(description = "身份校验（手机或邮件验证），0否/1是")
    private Boolean isIdentityChecked;


    @Schema(description = "是否强制修改密码，0否/1是")
    private Boolean isModifiedPs;


    @Schema(description = "是否激活：0否/1是")
    private Boolean isActive;


    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;


    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;


    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;


    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;

    @Schema(description = "登陆用户Id")
    private String fkUserLoginId;

    @Schema(description = "是否有查看佣金权限:0否/1是-仅针对文案和顾问角色")
    private Boolean isViewCommission;

    @Schema(description = "上司Id")
    private Long superiorId;

    @Schema(description = "国家ID")
    private List<Long> countyIds;

    @Schema(description = "是否拥有下属")
    private Boolean hasSubordinates;

}
