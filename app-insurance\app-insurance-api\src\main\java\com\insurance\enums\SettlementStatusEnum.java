package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 结算状态枚举类
 */

@Getter
@AllArgsConstructor
public enum SettlementStatusEnum {

    TO_BE_CONFIRM(0, "待确认"),
    CONFIRMED(1, "已确认-可以展示在小程序的"),
    AGENT_CONFIRMED(2, "代理确认-代理在小程序已经提交的"),
    FINANCE_CONFIRMED(3, "财务确认"),
    COMPLETED(4, "已结算"),
    ;


    private Integer code;

    private String msg;


    public static SettlementStatusEnum getEnumByCode(Integer code) {
        for (SettlementStatusEnum value : SettlementStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
