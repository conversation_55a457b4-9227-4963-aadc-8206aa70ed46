package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.MyParamsDto;
import com.partner.service.MyManagerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;

@Tag(description = "mymanager" , name = "小程序-我的-管理" )
@RestController
@RequestMapping("/mymanager")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MyManagerController {

    private final MyManagerService myManagerService;

    @Operation(summary = "头像-保存保存" , description = "头像-保存保存" )
    @SysLog("头像-保存保存" )
    @PostMapping("/addOrUpdateMyProfilePhoto" )
    public R addOrUpdateMyProfilePhoto(@RequestBody @Valid MyParamsDto paramsDto){

        return R.ok(myManagerService.addOrUpdateMyProfilePhoto(paramsDto));
    }




}
