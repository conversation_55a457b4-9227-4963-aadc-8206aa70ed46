package com.apps.service.impl;

import com.apps.api.dto.system.SaveUserPlatformLoginDto;
import com.apps.api.entity.SystemUserPlatformLoginEntity;
import com.apps.mapper.SystemUserPlatformLoginMapper;
import com.apps.service.SystemUserPlatformLoginService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SystemUserPlatformLoginServiceImpl extends ServiceImpl<SystemUserPlatformLoginMapper, SystemUserPlatformLoginEntity> implements SystemUserPlatformLoginService {

    private final SystemUserPlatformLoginMapper systemUserPlatformLoginMapper;

    @Override
    public SystemUserPlatformLoginEntity getUserByOpenId(Long fkPlatformId, String fkPlatformCode, String openId) {
        return systemUserPlatformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(Objects.nonNull(fkPlatformId), SystemUserPlatformLoginEntity::getFkPlatformId, fkPlatformId)
                .eq(Objects.nonNull(fkPlatformCode), SystemUserPlatformLoginEntity::getFkPlatformCode, fkPlatformCode)
                .eq(SystemUserPlatformLoginEntity::getMiniProgramOpenid, openId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveUserPlatformLogin(SaveUserPlatformLoginDto platformLoginDto) {
        //todo 用户密码、加密盐的生成方式
        SystemUserPlatformLoginEntity loginEntity = new SystemUserPlatformLoginEntity();
        BeanUtils.copyProperties(platformLoginDto, loginEntity);
        loginEntity.setGmtCreate(LocalDateTime.now());
        loginEntity.setGmtModified(LocalDateTime.now());
        return this.save(loginEntity);
    }
}
