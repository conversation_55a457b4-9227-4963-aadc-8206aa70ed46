<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pmp.mapper.PmpMediaAndAttachedMapper">

    <select id="selectMediaList" resultType="com.pmp.vo.commission.MediaVo">
        select m.id,
        m.fk_file_guid as fileGuid,
        m.link,
        m.remark,
        m.gmt_create,
        f.file_type_orc,
        f.file_name_orc,
        f.file_name,
        f.file_path,
        f.file_key,
        m.fk_table_name as table_name,
        m.fk_table_id as table_id
        from s_media_and_attached m
        left join ais_file_center.m_file_pmp f on m.fk_file_guid = f.file_guid
        where m.fk_table_name = #{tableName}
        and m.fk_table_id in
        <foreach item="tableId" collection="tableIds" open="(" separator="," close=")">
            #{tableId}
        </foreach>
        order by m.id desc
    </select>
</mapper>