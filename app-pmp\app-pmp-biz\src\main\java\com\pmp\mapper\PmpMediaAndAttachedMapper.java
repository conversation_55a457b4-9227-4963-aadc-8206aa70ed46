package com.pmp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pmp.entity.PmpMediaAndAttached;
import com.pmp.vo.commission.MediaVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PmpMediaAndAttachedMapper extends BaseMapper<PmpMediaAndAttached> {

    /**
     * 根据表名和表id查询媒体信息列表
     *
     * @param tableName
     * @param tableIds
     * @return
     */
    List<MediaVo> selectMediaList(@Param("tableName") String tableName, @Param("tableIds") List<Long> tableIds);

}
