package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-03 11:30:58
 */

@Data
@TableName("r_agent_uuid")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_agent_uuid ")
public class RAgentUuidEntity extends Model<RAgentUuidEntity>{

  @Schema(description = "代理UUID关系Id")
  private Long id;
 

  @Schema(description = "学生代理Id")
  private Long fkAgentId;
 

  @Schema(description = "学生代理UUID")
  private String fkAgentUuid;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
