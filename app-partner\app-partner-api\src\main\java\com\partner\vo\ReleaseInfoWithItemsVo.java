package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 发版信息聚合返回对象
 * <p>
 * 包含发版信息主表数据和相关子项列表，用于一次性返回完整的发版信息内容
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@Schema(description = "发版信息聚合返回对象")
public class ReleaseInfoWithItemsVo {

    /**
     * 发版信息ID
     */
    @Schema(description = "发版信息ID")
    private Long id;

    /**
     * 平台应用ID
     */
    @Schema(description = "平台应用ID")
    private Long fkPlatformId;

    /**
     * 平台应用代码
     */
    @Schema(description = "平台应用代码")
    private String fkPlatformCode;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private String versionNum;

    /**
     * 发版状态：0待发布/1已发布/2已撤回
     */
    @Schema(description = "发版状态：0待发布/1已发布/2已撤回")
    private Integer status;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date releaseTime;

    /**
     * 发布人
     */
    @Schema(description = "发布人")
    private String releaseUser;

    /**
     * 撤回时间
     */
    @Schema(description = "撤回时间")
    private Date withdrawTime;

    /**
     * 撤回人
     */
    @Schema(description = "撤回人")
    private String withdrawUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date gmtCreate;

    /**
     * 创建用户
     */
    @Schema(description = "创建用户")
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date gmtModified;

    /**
     * 修改用户
     */
    @Schema(description = "修改用户")
    private String gmtModifiedUser;

    /**
     * 发版信息子项列表（根据用户权限过滤）
     */
    @Schema(description = "发版信息子项列表（根据用户权限过滤）")
    private List<MReleaseInfoItemVo> releaseInfoItems;

}