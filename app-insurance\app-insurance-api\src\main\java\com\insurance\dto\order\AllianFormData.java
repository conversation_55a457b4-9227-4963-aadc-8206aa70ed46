package com.insurance.dto.order;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * 安联下单表单数据
 */
@Data
public class AllianFormData {
    private Step1 step1;
    private Step2 step2;
    private Step3 step3;
    private String orderNo;

    @Data
    public static class Step1 {
        private String start_time;
        private String end_time;
        private String number_of_adults;
        private String number_of_children;
    }

    @Data
    public static class Step2 {
        private List<Detail> details;
        private Integer already_living_australia;
        private Integer hava_australian_address;
        private String australian_address;
        private Integer have_different_postal_address;
        private String student_address;
        private String australian_mobile;
        private String study_address;

        @Data
        public static class Detail {
            private String first_name;
            private String family_name;
            private String birthday;
            private String gender;
            private String email;
            private String passport_number;
            private String country;
        }
    }

    @Data
    public static class Step3 {
        private Integer policy_agreement;
        private Integer market_consent;
        private String last_name;
        private String card_number;
        private String expiration_MM;
        private String expiration_YYYY;
        private String cvc;
        private String cardholder_name;
    }
}
