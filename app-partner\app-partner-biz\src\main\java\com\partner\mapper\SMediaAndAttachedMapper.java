package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.SMediaAndAttachedPublicDto;
import com.partner.entity.SMediaAndAttachedEntity;
import com.partner.vo.FileArray;
import com.partner.vo.SMediaAndAttachedVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【s_media_and_attached】的数据库操作Mapper
 * @createDate 2025-01-08 17:33:34
 * @Entity com.partner.entity.SMediaAndAttached
 */
@Mapper
public interface SMediaAndAttachedMapper extends BaseMapper<SMediaAndAttachedEntity> {
    int insertSelective(SMediaAndAttachedEntity record);


    List<SMediaAndAttachedVo> selectInstitutionFile(SMediaAndAttachedEntity params);

    List<FileArray> selectPublicFileArrays(SMediaAndAttachedPublicDto params);

    FileArray selectPublicFileOne(SMediaAndAttachedPublicDto params);


    SMediaAndAttachedEntity selectSaleOne(SMediaAndAttachedEntity contractVo);


    /**
     * 查询销售中心合同附件
     *
     * @param tableName
     * @param tableKey
     * @param tableId
     * @return
     */
    List<SMediaAndAttachedEntity> selectSaleCenterAttach(@Param("tableName") String tableName,
                                                         @Param("typeKey") String typeKey,
                                                         @Param("tableId") Long tableId);

    /**
     * 批量删除销售中心合同附件
     * @param fileGuids
     */
    void deleteSaleCenterAttach(@Param("fileGuids") List<String> fileGuids);


    /**
     * 根据参数删除销售中心合同附件
     * @param typeKey
     * @param tableId
     */
    void deleteSaleCenterAttachByParams(@Param("typeKey") String typeKey,
                                        @Param("tableId") Long tableId,
                                        @Param("tableName") String tableName);

    /**
     * 根据参数查询销售中心合同附件
     * @param typeKey
     * @param tableId
     * @return
     */
    List<FileArray> selectSaleCenterAttachAndFile(@Param("tableName") String tableName,
                                                         @Param("typeKey") String typeKey,
                                                         @Param("tableId") Long tableId);
}




