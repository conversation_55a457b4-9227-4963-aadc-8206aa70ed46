package com.partner.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.CommissionParamsDetailDto;
import com.partner.dto.CommissionParamsDto;
import com.partner.service.InstitutionCommissionService;
import com.pmp.dto.CommissionDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.vo.commission.MajorLevelTreeVo;
import com.pmp.vo.commission.MajorLevelVo;
import com.pmp.vo.commission.MergeCommissionVo;
import com.pmp.vo.institution.CountryVo;
import com.pmp.vo.institution.GroupVo;
import com.pmp.vo.institution.InstitutionTypeVo;
import com.pmp.vo.institution.InstitutionVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Tag(description = "institutionCommission", name = "小程序-院校佣金")
@RestController
@RequestMapping("/institutionCommission")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class InstitutionCommissionController {

    private final InstitutionCommissionService institutionCommissionService;


    @Operation(summary = "佣金分页查询", description = "院校佣金-院校佣金列表")
    @SysLog("佣金分页查询")
    @GetMapping("/getCommissionPage")
    public R getCommissionPage(Page page, @ParameterObject CommissionParamsDto dto) {

        return R.ok(institutionCommissionService.getCommissionPage(page, dto));
    }

    @Operation(summary = "院校佣金-查看全部", description = "院校佣金-查看全部")
    @SysLog("院校佣金-查看全部")
    @GetMapping("/getCommissionDetail")
    public R getCommissionDetail(@ParameterObject CommissionParamsDetailDto dto) {

        return R.ok(institutionCommissionService.getCommissionDetail(dto));
    }


    @Operation(summary = "院校佣金-国家反查接口", description = "院校佣金-国家反查接口")
    @SysLog("院校佣金-国家反查接口")
    @GetMapping("/getCountryCombox")
    public R getCountryCombox(@ParameterObject CommissionParamsDetailDto dto) {
        return R.ok(institutionCommissionService.getCountryCombox(dto, 0));
    }


    @Operation(summary = "高佣专区-首页(几个)", description = "高佣专区-首页")
    @SysLog("高佣专区-首页(几个)")
    @GetMapping("/getTallCommissionList")
    public R getTallCommissionList(@ParameterObject CommissionParamsDto dto) {
        dto.setType(0);
        return R.ok(institutionCommissionService.getTallCommissionList(dto));
    }


    @Operation(summary = "高佣专区-查看全部", description = "高佣专区-查看全部")
    @SysLog("高佣专区-查看全部")
    @GetMapping("/getTallCommissionAll")
    public R getTallCommissionAll(Page page, @ParameterObject CommissionParamsDto dto) {
        dto.setType(1);
        return R.ok(institutionCommissionService.getTallCommissionList(page, dto));
    }


    @Operation(summary = "院校佣金-首页高佣详细", description = "院校佣金-首页高佣详细")
    @SysLog("院校佣金-首页高佣详细")
    @GetMapping("/getHomeCommissionDetail")
    public R getHomeCommissionDetail(@ParameterObject CommissionParamsDetailDto dto) {

        return R.ok(institutionCommissionService.getHomeCommissionDetail(dto));
    }

    /**
     * 根据佣金查询学校类型数据
     */
    @Operation(summary = "院校佣金-学校类型数据(类型)", description = "院校佣金-学校类型数据(类型)")
    @PostMapping("/getTypeList")
    public R getTypeList(@RequestBody CommissionParamsDetailDto dto) {
        return R.ok(institutionCommissionService.getTypeList(dto));
    }


    /**
     * 根据佣金查询集团数据
     */
    @Operation(summary = "院校佣金-查询集团数据(集团)", description = "院校佣金-查询集团数据(集团)")
    @PostMapping("/getGroupList")
    public R getGroupList(@RequestBody CommissionParamsDetailDto dto) {
        return R.ok(institutionCommissionService.getGroupByCommission(dto));
    }

    /**
     * 根据佣金查询专业等级数据
     */
    @PostMapping("/getLeaveList")
    @Operation(summary = "根据佣金查询专业数据(课程)", description = "根据佣金查询专业数据(课程)")
    public R getLeaveList(@RequestBody CommissionParamsDetailDto dto) {
        return R.ok(institutionCommissionService.getLeaveList(dto));
    }


    @Operation(summary = "院校佣金-适用国家(国籍)", description = "院校佣金-适用国家(国籍)")
    @SysLog("院校佣金-适用国家")
    @GetMapping("/getCountryComboxAll")
    public R getCountryComboxAll(@ParameterObject CommissionParamsDetailDto dto) {
        return R.ok(institutionCommissionService.getCountryCombox(dto, 1));
    }

    @Operation(summary = "院校佣金明细-pmp2.0", description = "院校佣金明细-pmp2.0")
    @PostMapping("/getMergeCommission")
    public R<MergeCommissionVo> getMergeCommission(@RequestBody @Valid CommissionDto dto) {
        return R.ok(institutionCommissionService.getMergeCommission(dto));
    }

    @Operation(summary = "适用国家列表-pmp2.0", description = "院校佣金明细-pmp2.0-(agentId和companyId前端不用传)")
    @PostMapping("/countryList")
    public R<List<CountryVo>> countryList(@RequestBody @Valid DateDto dto) {
        return R.ok(institutionCommissionService.countryList(dto));
    }

    @Operation(summary = "学校类型列表-pmp2.0", description = "院校佣金明细-pmp2.0")
    @PostMapping("/institutionTypeList")
    public R<List<InstitutionTypeVo>> institutionTypeList() {
        return R.ok(institutionCommissionService.institutionTypeList());
    }

    @Operation(summary = "集团列表-pmp2.0", description = "院校佣金明细-pmp2.0-(agentId和companyId前端不用传)")
    @PostMapping("/groupList")
    public R<List<GroupVo>> groupList(@RequestBody DateDto dateDto) {
        return R.ok(institutionCommissionService.groupList(dateDto));
    }

    @Operation(summary = "课程等级列表-pmp2.0", description = "院校佣金明细-pmp2.0")
    @PostMapping("/selectMajorLevel")
    public R<List<MajorLevelVo>> selectMajorLevel() {
        return R.ok(institutionCommissionService.selectMajorLevel());
    }

    @Operation(summary = "课程等级树-pmp2.0", description = "院校佣金明细-pmp2.0")
    @PostMapping("/getMajorLevelTree")
    public R<List<MajorLevelTreeVo>> getMajorLevelTree() {
        return R.ok(institutionCommissionService.getMajorLevelTree());
    }

    @Operation(summary = "学校列表-pmp2.0", description = "院校佣金明细-pmp2.0-(agentId和companyId前端不用传)")
    @PostMapping("/institutionList")
    public R<Page<InstitutionVo>> institutionList(@RequestBody @Valid InstitutionDto institutionDto) {
        return R.ok(institutionCommissionService.institutionList(institutionDto));
    }


}
