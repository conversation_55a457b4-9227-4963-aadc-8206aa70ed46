package com.fzh.job.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzh.job.config.TemplateConfig;
import com.fzh.job.config.WechatApplet;
import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.mapper.LogLivePartnerUserAppointmentSenderMapper;
import com.fzh.job.mapper.MLiveMapper;
import com.fzh.job.mapper.RLiveUserAppointmentMapper;
import com.fzh.job.service.MLiveMessageSendService;
import com.fzh.job.util.HttpClientUtils;
import com.fzh.job.util.WechatAppletUtil;
import com.partner.entity.LogLivePartnerUserAppointmentSenderEntity;
import com.partner.entity.MLiveEntity;
import com.partner.entity.RLivePartnerUserAppointmentEntity;
import com.partner.util.BeanCopyUtils;
import com.partner.util.MyDateUtils;
import com.partner.vo.job.UserAppointmentVo;
import com.partner.wechat.Template.BaseValue;
import com.partner.wechat.Template.LiveMessageTemplate;
import com.partner.wechat.params.SendMessageParams;
import com.partner.wechat.result.AccessTokenResult;
import com.partner.wechat.result.SendMessageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class MLiveMessageSendServiceImpl extends ServiceImpl<RLiveUserAppointmentMapper, RLivePartnerUserAppointmentEntity>
        implements MLiveMessageSendService {
    private static final Logger log = LoggerFactory.getLogger(MLiveMessageSendServiceImpl.class);
    @Resource
    private RLiveUserAppointmentMapper liveUserAppointmentMapper;
    @Resource
    private MLiveMapper mLiveMapper;
    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private WechatApplet wechatApplet;

    @Resource
    private LogLivePartnerUserAppointmentSenderMapper logLivePartnerUserAppointmentSenderMapper;




    @Override
    public Boolean sendLiveMessage(BaseParamDto paramDto) {
        Boolean flag=true;
        //查询出最近15分钟准备直播内容
        List<MLiveEntity> listMlive= mLiveMapper.selectDetail();






        if(wechatApplet.getSendMessageUrl()==null || wechatApplet.getSendMessageUrl().trim().equals("")){
            log.info("直播消息推送失败:未找到推送URL");
            return Boolean.FALSE;
        }

        List<TemplateConfig> wechatAppletArr= wechatApplet.getTemplateArr();
        TemplateConfig config;
        if(wechatAppletArr==null||wechatAppletArr.size()==0){
            log.info("直播消息推送失败:未找到消息模板");
            return Boolean.FALSE;
        }else {
            Map<String,TemplateConfig> resultmap= wechatAppletArr.stream().collect(Collectors.toMap(TemplateConfig::getCode,o->o));
            config=resultmap.get("partnerMlive");
            if(config==null){
                log.info("直播消息推送失败:未找到消息模板");
                return Boolean.FALSE;
            }
            if(config.getTemplateId()==null|| "".equals(config.getTemplateId()) ){
                log.info("直播消息推送失败:未找到消息模板");
                return Boolean.FALSE;
            }
        }

        // 尝试获取锁，设置过期时间防止死锁
        for(MLiveEntity livePO: listMlive){

            String lockKey = "mLivePartner:reminder:" + livePO.getId();
            boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 5, TimeUnit.MINUTES);
            if(!locked){
                continue;//锁定中的直播直接处理下一个
            }

            if (locked) {
                try {
                    //登录小程序获取token
                    String token= WechatAppletUtil.getToken(wechatApplet);
                    if(StringUtils.isEmpty(token)){
                        log.info("直播消息推送失败:获取token失败");
                        return Boolean.FALSE;
                    }
                    List<UserAppointmentVo> rliveList=mLiveMapper.selectDetailList(livePO.getId());//未发送预约消息用户
                    List<LogLivePartnerUserAppointmentSenderEntity> sendLogList=new ArrayList<>();//使用LIST记录发送日志批量保存


                    //发送消息
                    for(UserAppointmentVo tmptouserInfo:rliveList){
                        try{
                            RLivePartnerUserAppointmentEntity rLiveAppointModify=new RLivePartnerUserAppointmentEntity();
                            BeanCopyUtils.copyProperties(tmptouserInfo,rLiveAppointModify);

                            LogLivePartnerUserAppointmentSenderEntity logtmp=new LogLivePartnerUserAppointmentSenderEntity();
                            logtmp.setFkLiveId(livePO.getId());
                            logtmp.setFkPartnerUserId(tmptouserInfo.getFkPartnerUserId());
                            logtmp.setGmtCreate(LocalDateTime.now());
                            logtmp.setGmtCreateUser("system");
                            if(tmptouserInfo.getTouser()==null || "".equals(tmptouserInfo.getTouser())){
                                logtmp.setStatus(2);//发送失败
                                logtmp.setRemark("用户openId为空");
                                sendLogList.add(logtmp);

                                rLiveAppointModify.setStatus(2);
                                liveUserAppointmentMapper.updateById(rLiveAppointModify);
                                continue;
                            }
                            Map headerParams = new HashMap();
                            headerParams.put("Content-Type", "application/json");
                            SendMessageParams sendMessageParams = new SendMessageParams();
                            sendMessageParams.setTemplate_id(config.getTemplateId());
                            sendMessageParams.setPage("/pages/index/index");
                            sendMessageParams.setTouser(tmptouserInfo.getTouser());

                            LiveMessageTemplate messageTemplate = new LiveMessageTemplate();

                            BaseValue thingValue = new BaseValue();
                            thingValue.setValue(livePO.getTitle()==null?"华通伙伴直播等你来！":livePO.getTitle());
                            messageTemplate.setThing1(thingValue);
                            BaseValue timeValue = new BaseValue();
                            timeValue.setValue(MyDateUtils.formatDate(livePO.getLiveTimeStart()));
                            messageTemplate.setTime2(timeValue);

                            sendMessageParams.setData(messageTemplate);
                            //System.out.println(JSONObject.toJSON(sendMessageParams));

                            String sendMessageUrl=wechatApplet.getSendMessageUrl()+"?access_token="+token;

                            String messageReponse=HttpClientUtils.sendMessagePostRequest(sendMessageUrl,headerParams,sendMessageParams);
                            SendMessageResult messageResult = JSON.parseObject(messageReponse, SendMessageResult.class);
                            if(messageResult.getErrcode()==0){
                                //成功  -更新状态
                                logtmp.setStatus(1);
                                logtmp.setRemark(messageReponse);
                                sendLogList.add(logtmp);

                                rLiveAppointModify.setStatus(1);
                                liveUserAppointmentMapper.updateById(rLiveAppointModify);
                            }else{
                                //失败 -更新状态
                                logtmp.setStatus(2);
                                logtmp.setRemark(messageReponse);
                                sendLogList.add(logtmp);

                                rLiveAppointModify.setStatus(2);
                                liveUserAppointmentMapper.updateById(rLiveAppointModify);
                            }
                        }catch (Exception e){
                            log.info("直播消息推送失败:{};失败人员:{}", livePO.getId(), tmptouserInfo.getFkPartnerUserId());
                        }
                    }
                    //保存请求日志
                    logLivePartnerUserAppointmentSenderMapper.insert(sendLogList);

                }catch (Exception e){
                    log.error(e.getMessage());
                } finally {
                    //发送成功后删除或保留key直到直播开始
                    redisTemplate.delete(lockKey);
                }
            }else{
                log.info("获取直播锁失败:{}", livePO.getId());
                flag=false;
            }
            //break;
        }




        return flag;
    }





   /* public  String getToken(){
        String token="";
        Map<String, String> headerParams = new HashMap<>();
        Map<String, String> bodyParams = new HashMap<>();
        bodyParams.put("grant_type", "client_credential");
        bodyParams.put("appid",wechatApplet.getAppId());
        bodyParams.put("secret",wechatApplet.getSecret());
        String tokenResult= HttpClientUtils.sendPostRequest(wechatApplet.getStableTokenUrl(), headerParams, bodyParams);
        System.out.println(tokenResult);
        AccessTokenResult accessTokenResult = JSON.parseObject(tokenResult, AccessTokenResult.class);
        if(accessTokenResult!=null){
            token=accessTokenResult.getAccess_token();
        }
        return token;
    }*/



}
