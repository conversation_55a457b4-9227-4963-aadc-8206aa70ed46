package com.partner.dto.student;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "详情查询参数")
public class MStudentParamsDetailDto   {

   /* @Schema(description = "代理UUID")
    @NotBlank(message = "代理UUID不能为空")
    private String agentUUID;*/

    @Schema(description = "学生UUID")
    @NotBlank(message = "学生UUID不能为空")
    private String studentUUID;

    @Schema(description = "申请UUID")
    private String offerItemUUID;

    @Schema(description = "角色编码")
    private String roleCode;



}
