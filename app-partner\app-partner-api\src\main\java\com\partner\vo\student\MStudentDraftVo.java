package com.partner.vo.student;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


@Data
@Schema(description = "Partner新增学生信息处理")
public class MStudentDraftVo {
    @Schema(description = "学生草稿的Id")
    private String id;

    @Schema(description = "平台ID")
    private Long fkPlatformId;

    @Schema(description = "学生UUID")
    private String fkStudentUuid;

    @Schema(description = "学生名字")
    private String studentName;

    @Schema(description = "学生性别 0女 1男")
    private String gender;
    @Schema(description = "学生生日")
    private LocalDate birthday;

    @Schema(description = "国籍名称")
    private String areaCountryName;

    @Schema(description = "手机区号")
    private String  mobileAreaCode;
    @Schema(description = "移动电话")
    private String mobile;


    @Schema(description = "状态：-1作废/0未提交(草稿)/1已提交(待确认处理)/2正式生效")
    private Integer status;

    @Schema(description = "说明")
    private String checkComment;

    @Schema(description = "阅读时间")
    private LocalDateTime checkReadTime;
    @Schema(description = "阅读状态 1已读")
    private int readstatus=0;


    @Schema(description = "审核时间")
    private LocalDateTime gmtCreate;

    public int getReadstatus() {
        if(checkReadTime!=null){
            readstatus=1;
        }
        return readstatus;
    }


}
