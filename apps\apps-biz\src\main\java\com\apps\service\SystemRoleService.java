package com.apps.service;

import com.apps.api.dto.partner.UpdateUserRoleDto;
import com.apps.api.dto.system.SaveRoleDto;
import com.apps.api.entity.SystemRoleEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SystemRoleService extends IService<SystemRoleEntity> {

    /**
     * 保存角色
     *
     * @param roleDto
     */
    void saveRole(SaveRoleDto roleDto);

    /**
     * 删除角色
     *
     * @param roleId
     */
    void delRole(Long roleId);

    /**
     * 获取角色列表
     *
     * @return
     */
    List<Map<String, Object>> getRoleList(Long platformId, String platformCode);

    void updateUserRole(UpdateUserRoleDto updateUserRoleDto);

    void delUserCache(Long userId);

    /**
     * 批量下线用户
     * @param loginIds
     */
    void userOfferLine(List<String> loginIds);

}
