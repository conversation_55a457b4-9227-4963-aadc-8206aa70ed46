package com.apps.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * app登录前校验用户
 */
@Data
public class VerifyUserDto {

    @Schema(description = "登录账号")
    @NotBlank(message = "登录账号不能为空")
    private String account;

    @Schema(description = "用户注册平台应用CODE")
    @NotBlank(message = "用户注册平台应用CODE不能为空")
    private String formPlatformCode;

    @Schema(description = "用户注册平台应用Id")
    @NotNull(message = "用户注册平台应用Id不能为空")
    private Long formPlatformId;
}
