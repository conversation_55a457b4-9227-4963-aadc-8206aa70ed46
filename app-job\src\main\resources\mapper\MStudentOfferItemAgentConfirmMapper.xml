<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fzh.job.mapper.MStudentOfferItemAgentConfirmMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MStudentOfferItemAgentConfirmEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkTenantId" column="fk_tenant_id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkStudentOfferItemId" column="fk_student_offer_item_id" jdbcType="BIGINT"/>
            <result property="fkPartnerUserId" column="fk_partner_user_id" jdbcType="BIGINT"/>
            <result property="isSystemConfirmed" column="is_system_confirmed" jdbcType="BIT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
        <select id="insertAffirmOfferItem" >

            INSERT INTO app_partner_center.m_student_offer_item_agent_confirm(
                fk_tenant_id,
                fk_agent_id,
                fk_student_offer_item_id,
                fk_partner_user_id,
                is_system_confirmed,
                gmt_create,
                gmt_create_user)
            SELECT
                2 AS fk_tenant_id,
                offerItem.fk_agent_id  AS agentId,
                offerItem.id AS offerItemId,
                0 AS fk_partner_user_id,
                1 AS is_system_confirmed,
                now() AS gmt_create,
                'system' AS gmt_create_user
            FROM ais_sale_center.m_student_offer_item offerItem
                     INNER JOIN  (
                SELECT  offerItem.id AS itemId,min(rStudentOfferItemStep.gmt_create) AS gmt_create FROM ais_sale_center.m_student_offer_item offerItem
                                                                                                            INNER JOIN  ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
                WHERE    rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10)
                GROUP BY offerItem.id   HAVING min(rStudentOfferItemStep.gmt_create)  BETWEEN #{firstDayOfLastMonth} AND #{firstDayOfToMonth}
            ) a  ON  a.itemId=offerItem.id
                     LEFT JOIN app_partner_center.m_student_offer_item_agent_confirm offerItemAgentConfim ON offerItemAgentConfim.fk_student_offer_item_id=offerItem.id
            WHERE offerItem.fk_agent_id=#{agentId}
              AND    offerItemAgentConfim.id is NULL

        </select>
    <select id="getAllAgentId" resultType="java.lang.Long">
        SELECT DISTINCT fk_agent_id FROM app_partner_center.m_partner_user mPartnerUser WHERE mPartnerUser.is_active=1
    </select>

    <select id="getAllAgentIdSendMessage" resultType="com.partner.vo.job.SystemUserJobVo">
        SELECT DISTINCT
            systemUser.num,
            systemUser.name,
            systemUser.email,
            mPartnerUser.fk_agent_id AS agentId,
            mPartnerUser.fk_company_id
        FROM app_system_center.system_user systemUser
            INNER JOIN app_system_center.system_user_role systemUserRole ON systemUserRole.fk_user_id=systemUser.id
            INNER JOIN app_partner_center.m_partner_user  mPartnerUser ON mPartnerUser.fk_user_id=systemUser.id
        WHERE  systemUser.fk_from_platform_code='PARTNER' AND systemUser.is_lock_flag=0 AND systemUser.is_del_flag=0
            AND systemUser.email IS NOT NULL AND systemUserRole.fk_role_id in(4,16)
    </select>


</mapper>
