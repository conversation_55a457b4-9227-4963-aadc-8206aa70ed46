package com.partner.dto.finance.paramsmapper;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
@Schema(description = "佣金-历史账单-参数(+权限信息)")
public class MSettlementBillSearchParams  extends UserInfoParams {
    @Schema(description = "结算ID")
    private Long msettlementId;


    @Schema(description = "申请开始时间")
    private LocalDate gmtCreateStart;

    @Schema(description = "申请结束时间")
    private LocalDate gmtCreateEnd;


}
