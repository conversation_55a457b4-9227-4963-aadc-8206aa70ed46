package com.apps.rocketmq.msg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/20
 * @Version 1.0
 * @apiNote:用户下线消息体
 */
@Data
public class UserOfflineDto {
    @Schema(description = "用户登录账号")
    private List<String> loginIds;

    @Schema(description = "平台ID")
    private Long platformId;

    public UserOfflineDto(List<String> userIds, Long platformId) {
        this.loginIds = userIds;
        this.platformId = platformId;
    }
}
