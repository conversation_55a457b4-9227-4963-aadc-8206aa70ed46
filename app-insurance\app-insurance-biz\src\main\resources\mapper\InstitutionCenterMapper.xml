<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.InstitutionCenterMapper">


    <select id="selectCountryList" resultType="com.insurance.vo.institution.CountryVo">
        SELECT c.id       AS countryId,
               c.name,
               c.name_chn AS nameChn
        FROM u_area_country c
        where  c.num not in ('N/A', 'GLB')
        order by c.view_order desc
    </select>

    <select id="selectAreaState" resultType="com.insurance.vo.institution.AreaStateVo">
        select s.id as areaStateId, s.name, s.name_chn
        from u_area_state s
        <where>
            <if test="countryId != null and countryId > 0">
                s.fk_area_country_id =
                #{countryId}
            </if>
        </where>
        order by s.view_order desc
    </select>
</mapper>