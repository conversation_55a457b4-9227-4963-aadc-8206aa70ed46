package com.pmp.vo.commission;

import com.pmp.vo.institution.InstitutionVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/8
 * @Version 1.0
 * @apiNote: 学校佣金明细-合并
 */
@Data
public class MergeCommissionVo {

    @Schema(description = "集团名称")
    private String group;

    @Schema(description = "集团中文名称")
    private String groupChn;

    @Schema(description = "佣金适用地区和佣金明细和方案信息")
    private List<TerritoryInfoVo> territoryInfos;

    @Schema(description = "学校详情")
    private InstitutionVo institutionDetail;

    @Schema(description = "组合课程列表")
    private List<AgentCommissionListVo.AgentCombinationInfo> agentCombinationList;

    @Schema(description = "整体佣金Bonus条件列表")
    private List<AgentCommissionListVo.AgentBonusInfo> agentBonusList;

    @Schema(description = "附件列表")
    private List<MediaVo> mediaList;

    public MergeCommissionVo() {
        this.group = Strings.EMPTY;
        this.groupChn = Strings.EMPTY;
        this.territoryInfos = new ArrayList<>();
        this.agentCombinationList = new ArrayList<>();
        this.agentBonusList = new ArrayList<>();
        this.institutionDetail = null;
    }
}
