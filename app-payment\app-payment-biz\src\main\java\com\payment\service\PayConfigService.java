package com.payment.service;

import com.payment.config.PayConfig;
import com.payment.config.PayProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author:Oliver
 * @Date: 2025/7/16
 * @Version 1.0
 * @apiNote:支付参数SERVICE
 */
@Service
@Slf4j
public class PayConfigService {

    @Autowired
    private PayProperties payProperties;

    /**
     * 根据微信平台证书序列号查找支付配置
     */
    public PayConfig getByPlatformCertId(String PlatformCertId) {
        return payProperties.getConfigs().stream()
                .filter(config -> PlatformCertId.equals(config.getWxPlatformCertId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据 appId 查找（可用于下单）
     */
    public PayConfig getByAppId(String appId) {
        return payProperties.getConfigs().stream()
                .filter(config -> appId.equals(config.getAppId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据 appId 和 type 获取
     *
     * @param appId
     * @param type
     * @return
     */
    public PayConfig getByAppIdAndType(String appId, String type) {
        return payProperties.getConfigs().stream()
                .filter(config -> config.getAppId().equals(appId))
                .filter(config -> config.getType().equalsIgnoreCase(type))
                .findFirst()
                .orElse(null);
    }
}
