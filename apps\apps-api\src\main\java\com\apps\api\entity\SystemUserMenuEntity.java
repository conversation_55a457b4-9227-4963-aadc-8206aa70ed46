package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_user_menu")
@Schema(description = "系统用户菜单")
public class SystemUserMenuEntity extends Model<SystemUserMenuEntity> {
    private static final long serialVersionUID = 1L;


    /**
     * 用户和菜单权限关系Id
     */
    @Schema(description="用户和菜单权限关系Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @Schema(description="租户Id")
    private Long fkTenantId;

    /**
     * 用户Id
     */
    @Schema(description="用户Id")
    private Long fkUserId;

    /**
     * 菜单Id
     */
    @Schema(description="菜单Id")
    private Long fkMenuId;

    /**
     * 权限：0禁止/1允许，禁止权限大于允许权限
     */
    @Schema(description="权限：0禁止/1允许，禁止权限大于允许权限")
    private Integer permission;


    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}