package com.partner.service;

import com.apps.api.dto.base.BaseUserInfoDto;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.entity.RPartnerUserAreaCountryEntity;
import com.partner.vo.base.PartnerUserInfoVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【r_partner_user_area_country】的数据库操作Service
 * @createDate 2025-01-14 21:05:44
 */
public interface RPartnerUserAreaCountryService extends IService<RPartnerUserAreaCountryEntity> {

    PartnerUserInfoVo getPartnerInfo(BaseUserInfoDto paramsDto);


    public void deleteSupCache(Long partnerUserId);

    /**
     * 保存伙伴国家
     *
     * @param partnerUserId
     * @param countryIds
     */
    void savePartnerCountry(Long partnerUserId, List<Long> countryIds);
}
