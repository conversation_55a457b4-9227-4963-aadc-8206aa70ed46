package com.partner.vo.work;

import com.partner.entity.MFeedbackOrderEntity;
import com.partner.entity.MFeedbackOrderReplyEntity;
import com.partner.vo.FileArray;
import com.partner.vo.SMediaAndAttachedVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class MFeedbackOrderDetailVo extends MFeedbackOrderEntity {
    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "反馈附件")
    List<FileArray> fileArray;

    List<MFeedbackOrderReplyEntity> replyVoList;


}
