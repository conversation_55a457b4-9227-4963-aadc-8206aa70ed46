package com.partner.vo.student;

import com.partner.vo.combox.StudentOfferItemCourtyCombox;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
@Data
public class StudentApplySumVo {

    @Schema(description="学生申请人数")
    private int studentsApplayPeopleTotal;

    @Schema(description="学生申请国家数量")
    List<StudentOfferItemCourtyCombox> studentsApplyCourty;

}
