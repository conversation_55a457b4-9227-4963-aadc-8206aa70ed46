package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.partner.dto.work.MFeedbackOrderDddDto;
import com.partner.dto.work.MFeedbackOrderDto;
import com.partner.dto.work.MFeedbackOrderReplyDto;
import com.partner.service.MFeedbackOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Tag(description = "mFeedbackOrder" , name = "小程序-反馈" )
@RestController
@RequestMapping("/mFeedbackOrder")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MFeedbackOrderController {

    private final MFeedbackOrderService mFeedbackOrderService;

    @Operation(summary = "反馈分页查询" , description = "反馈分页查询" )
    @GetMapping("/getPage" )
    public R getPage(Page page, @ParameterObject MFeedbackOrderDto dto) {
        return R.ok(mFeedbackOrderService.getPage(page, dto));
    }


    @Operation(summary = "反馈疑问详情" , description = "反馈疑问详情" )
    @GetMapping("/getMFeedbackDetail/{id}" )
    public R getMFeedbackDetail(@PathVariable("id") Long id) {
        return R.ok(mFeedbackOrderService.getMFeedbackDetail(id));
    }


    @Operation(summary = "提交反馈" , description = "提交反馈" )
    @PostMapping("/addWorkOrder" )
    public R addWorkOrder(@RequestBody  @Validated MFeedbackOrderDddDto adddto) {
        return R.ok(mFeedbackOrderService.addWorkOrder(adddto));
    }


    @Operation(summary = "反馈回复" , description = "反馈回复" )
    @PostMapping("/feedDack" )
    public R feedDack(@RequestBody  @Validated MFeedbackOrderReplyDto replydto) {
        return R.ok(mFeedbackOrderService.feedDack(replydto));
    }


    @Operation(summary = "反馈类型查询" , description = "反馈类型查询" )
    @GetMapping("/getUFeedbackOrderType" )
    public R getUFeedbackOrderType() {
        return R.ok(mFeedbackOrderService.getUFeedbackOrderType());
    }


    @Operation(summary = "反馈解决" , description = "反馈解决" )
    @GetMapping("/solve/{id}" )
    public R solve(@PathVariable Long id) {
        return R.ok(mFeedbackOrderService.getUFeedbackOrderType());
    }


}
