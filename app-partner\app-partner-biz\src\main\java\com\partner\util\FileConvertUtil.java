package com.partner.util;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.poi.util.IOUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;

public class FileConvertUtil {

    public static MultipartFile fileToMultipartFile(File file) throws IOException {
        DiskFileItem fileItem = new DiskFileItem(
                "file",                                  // form field name
                "application/octet-stream",              // content type
                false,                                   // isFormField
                file.getName(),                          // original file name
                (int) file.length(),                     // file size
                file.getParentFile()                     // parent directory
        );

        try (InputStream input = new FileInputStream(file);
             OutputStream os = fileItem.getOutputStream()) {
            IOUtils.copy(input, os);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return new CommonsMultipartFile(fileItem);
    }
}
