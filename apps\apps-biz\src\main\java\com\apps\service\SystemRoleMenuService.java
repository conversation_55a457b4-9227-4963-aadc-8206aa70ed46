package com.apps.service;

import com.apps.api.dto.system.SaveRoleMenuDto;
import com.apps.api.entity.SystemRoleMenuEntity;
import com.apps.api.vo.system.MenuTreeVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemRoleMenuService extends IService<SystemRoleMenuEntity> {

    /**
     * 根据角色id获取菜单树
     * @param roleId
     * @return
     */
    List<MenuTreeVo> getRoleMenu(Long roleId);


    /**
     * 根据用户id获取菜单树
     * @param userId
     * @return
     */
    List<MenuTreeVo> getUserMenu(Long userId);

    /**
     * 保存角色菜单权限
     * @param roleMenuDto
     */
    void saveRoleMenu(SaveRoleMenuDto roleMenuDto);

    /**
     * 根据用户id获取菜单ID集合
     * @param userId
     * @return
     */
    List<Long> getUserMenuIds(Long userId);
}
