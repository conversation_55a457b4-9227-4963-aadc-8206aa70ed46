package com.apps.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum LoginTypeEnum {

    MP("MP", "微信小程序"),
    ACCOUNT("ACCOUNT", "账号密码"),
    VCODE("VCODE", "验证码(手机或邮件)"),
    QRCODE("QRCODE", "公众号扫码登录"),
    ;

    private String code;

    private String msg;

    public static String getEnumByCode(String code) {
        for (LoginTypeEnum value : LoginTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

}
