package com.partner.dto.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Date;

@Data
public class MSettlementBillParamsDto {

    @Schema(description = "结算ID")
    @NotNull(message = "结算Id不能为空")
    private Long msettlementId;

    @Schema(description = "申请开始时间")
    private LocalDate gmtCreateStart;

    @Schema(description = "申请结束时间")
    private LocalDate gmtCreateEnd;


}
