package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-27 15:20:44
 */

@Data
@TableName("u_area_city")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" u_area_city ")
public class UAreaCityEntity extends Model<UAreaCityEntity>{

  @Schema(description = "城市Id")
  private Long id;
 

  @Schema(description = "州省Id")
  private Long fkAreaStateId;
 

  @Schema(description = "城市编号")
  private String num;
 

  @Schema(description = "城市名称")
  private String name;
 

  @Schema(description = "城市中文名称")
  private String nameChn;
 

  @Schema(description = "邮编")
  private String zipCode;
 

  @Schema(description = "人口")
  private String population;
 

  @Schema(description = "面积")
  private String area;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
  private String publicLevel;
 

  @Schema(description = "排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
