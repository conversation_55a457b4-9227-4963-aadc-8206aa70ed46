package com.partner.controller;


import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.service.LogoutService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(description = "logout" , name = "小程序-注销" )
@RestController
@RequestMapping("/logout")
@RequiredArgsConstructor
public class LogoutController {

    private final LogoutService logoutService;

    @Operation(summary = "用户注销调用清理缓存" , description = "用户注销调用清理缓存" )
    @SysLog("用户注销调用清理缓存" )
    @PostMapping("/deleteCache")
    public R deleteCache() {
        logoutService.deleteCache();
        return R.ok();
    }

}
