package com.insurance.vo.partner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Data
public class PartnerUserInfo {

    @Schema(description = "伙伴用户Id")
    private Long partnerUserId;

    @Schema(description = "分公司ID")
    private Long companyId;

    @Schema(description = "代理ID")
    private Long agentId;

    @Schema(description = "系统用户Id")
    private Long systemUserId;
}
