package com.apps.service.impl;

import com.apps.api.entity.SystemPlatformEntity;
import com.apps.mapper.SystemPlatformMapper;
import com.apps.service.SystemPlatformService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SystemPlatformServiceImpl extends ServiceImpl<SystemPlatformMapper, SystemPlatformEntity> implements SystemPlatformService {

    private final SystemPlatformMapper systemPlatformMapper;
}
