package com.pmp.vo.commission;

import com.pmp.vo.institution.InstitutionVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/25  17:44
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentCommissionDetailVo {

    @Schema(description = "集团名称")
    private String group;

    @Schema(description = "集团中文名称")
    private String groupChn;

    @Schema(description = "佣金方案摘要及说明")
    private List<TerritoryInfoVo> territoryInfos;

    @Schema(description = "学校详情")
    private InstitutionVo institutionDetail;

    @Schema(description = "佣金明细")
    private AgentCommissionListVo commissions;

}
