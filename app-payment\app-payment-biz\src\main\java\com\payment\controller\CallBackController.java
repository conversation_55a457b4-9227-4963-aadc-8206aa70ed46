package com.payment.controller;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.common.security.annotation.Inner;
import com.payment.entity.InsuranceOrderMpPayment;
import com.payment.enums.PayStatusEnum;
import com.payment.rocketmq.producer.MqProducer;
import com.payment.service.CallBackService;
import com.payment.service.MpPaymentService;
import com.payment.service.wx.WxPayRefundService;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.Transaction;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:
 */
@RestController
@Slf4j
@RequestMapping("/callback")
@Tag(description = "支付回调管理", name = "支付回调管理")
@Inner(value = false)
public class CallBackController {

    @Autowired
    private CallBackService callBackService;
    @Autowired
    private WxPayRefundService wxPayRefundService;
    @Autowired
    private MpPaymentService mpPaymentService;
    @Autowired
    private MqProducer mqProducer;

    @Operation(summary = "微信支付回调")
    @Inner(value = false)
    @PostMapping("/wx")
    public Map<String, String> wxCallBack(HttpServletRequest request) {
        log.info("====================>来自微信支付回调");
        boolean success = "SUCCESS".equalsIgnoreCase(callBackService.wxCallBack(request));
        return success
                ? createResponse("SUCCESS")
                : createResponse("FAIL");
    }

    @Operation(summary = "微信退款支付回调")
    @Inner(value = false)
    @PostMapping("/wx/refund")
    public Map<String, String> wxRefundCallBack(HttpServletRequest request) {
        log.info("====================>来自微信支付退款的回调");
        boolean success = "SUCCESS".equalsIgnoreCase(wxPayRefundService.handleRefundNotify(request));
        return success
                ? createResponse("SUCCESS")
                : createResponse("FAIL");
    }


    @Operation(summary = "微信支付回调-本地模拟成功")
    @Inner(value = false)
    @PostMapping("/simulatePaySuccess")
    public void simulatePaySuccess(String orderNo, String topic) {
        Transaction transaction = new Transaction();
        transaction.setOutTradeNo(orderNo);
        transaction.setTransactionId(UuidUtils.generateUuid().replaceAll("-", ""));
        transaction.setTradeState(Transaction.TradeStateEnum.SUCCESS);
        transaction.setAttach(topic);
        InsuranceOrderMpPayment mpPayment = InsuranceOrderMpPayment.builder()
                .orderNo(transaction.getOutTradeNo())
                .mpPaymentOrderNum(transaction.getTransactionId())
                .mpPaymentStatus(Transaction.TradeStateEnum.SUCCESS.equals(transaction.getTradeState()) ?
                        PayStatusEnum.PAY_SUCCESS.getCode() : PayStatusEnum.PAY_FAIL.getCode())
                .build();
        mpPaymentService.updateMpPayment(mpPayment);
        String attach = transaction.getAttach();
        if (StringUtils.isNotBlank(attach)) {
            log.info("====================>模拟微信支付回调，通知业务处理,topic:{},订单号:{}", attach, transaction.getOutTradeNo());
            mqProducer.sendPaySuccessMsg(attach, transaction.getOutTradeNo());
        }
    }

    /**
     * 创建响应
     *
     * @param code
     * @return
     */
    private Map<String, String> createResponse(String code) {
        Map<String, String> response = new HashMap<>(2);
        response.put("code", code);
        response.put("message", code);
        return response;
    }
}
