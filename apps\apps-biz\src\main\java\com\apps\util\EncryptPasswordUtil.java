package com.apps.util;

import lombok.SneakyThrows;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @Author:Oliver
 * @Date: 2025/1/18  14:31
 * @Version 1.0
 * 密码加密工具类
 */
public class EncryptPasswordUtil {

    @SneakyThrows
    public static String encrypt(String text, String keyStr) {
        // 使用 UTF-8 编码将密钥转换为字节
        byte[] key = keyStr.getBytes(StandardCharsets.UTF_8);
        // 初始化向量与密钥相同
        byte[] iv = key;
        // 创建密钥和初始化向量
        SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        // 创建 Cipher 对象，并初始化为加密模式
        Cipher cipher = Cipher.getInstance("AES/CFB/NoPadding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);

        // 执行加密
        byte[] encryptedBytes = cipher.doFinal(text.getBytes(StandardCharsets.UTF_8));

        // 使用 Base64 编码输出加密后的字符串
        String aesStr = Base64.getEncoder().encodeToString(encryptedBytes);
        return aesStr;
    }

    // 解密
    @SneakyThrows
    public static String decrypt(String base64Encrypted, String keyStr) {
        byte[] key = keyStr.getBytes(StandardCharsets.UTF_8);
        byte[] iv = key;
        SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance("AES/CFB/NoPadding");
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);

        byte[] decodedBytes = Base64.getDecoder().decode(base64Encrypted);
        byte[] decryptedBytes = cipher.doFinal(decodedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

//    public static void main(String[] args) {
//
//        String encrypt = encrypt("898000", "thanks,fzh202411");
//        System.out.println(encrypt);
//        String rawPassword = "QUTj+OO5";
//        System.out.println(decrypt("QUTj+OO5","thanks,fzh202411"));
//        PasswordEncoder encoder = new BCryptPasswordEncoder();
//        String aa="$2a$10$0zlUwUArYlMnYz1XW3Oy2eL5jhusoJ2cr3PzZKW0MzDxapk2v2OJe";
//        String bb="$2a$10$95TmGXAVc/9cEkM/rUk.sOqv8.MByLvvLGhD.R1BDFtuYVpKXVk0m";
//        System.out.println(encoder.matches("123456",aa));
//    }
}
