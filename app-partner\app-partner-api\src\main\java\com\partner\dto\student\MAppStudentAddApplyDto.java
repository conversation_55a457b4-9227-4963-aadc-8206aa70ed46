package com.partner.dto.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MAppStudentAddApplyDto {
    @Schema(description = "申请学生Id")
    @NotNull(message = "申请学生fkAppStudentId不能为空!")
    private Long fkAppStudentId;



    @Schema(description = "申请学生Id")
    @NotNull(message = "加申 申请计划不能为空!")
    @Valid
    private List<MAppStudentOfferItemDto> list;
}
