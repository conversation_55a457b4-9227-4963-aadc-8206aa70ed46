package com.partner.dto;

import com.partner.entity.EventEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MEventParamsDto extends EventEntity {

    @Schema(description = "图片桶域名")
    private String mMageAddress;

    @Schema(description = "partner用户Id")
    private Long partnerUserId;



    @Schema(description = "姓名")
    private String name;
    @Schema(description = "手机区号")
    private String mobileAreaCode;
    @Schema(description = "移动电话")
    private String mobile;
    @Schema(description = "参加人数")
    private Integer peopleCount;
}
