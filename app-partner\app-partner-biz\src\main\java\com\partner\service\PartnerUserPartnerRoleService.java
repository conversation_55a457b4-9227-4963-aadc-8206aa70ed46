package com.partner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.entity.PartnerUserPartnerRole;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
public interface PartnerUserPartnerRoleService extends IService<PartnerUserPartnerRole> {

    /**
     * 保存用户角色关系
     * @param partnerUserId
     * @param roleIds
     */
    void savePartnerUserPartnerRole(Long partnerUserId, List<Long> roleIds);
}
