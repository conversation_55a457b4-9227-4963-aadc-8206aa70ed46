package com.partner.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "申请学生步骤")
public class MStudenItemInfo {

    @Schema(description = "学校ID")
    private Long fkInstitutionId;

    @Schema(description = "学校名称")
    private String institutionName;

    @Schema(description = "国家ID")
    private Long fkAreaCountryId;

    @Schema(description = "申请步骤排序，由0开始按顺序排列")
    private int stepOrder;


}
