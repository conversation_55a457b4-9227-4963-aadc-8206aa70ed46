package com.partner.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.work.MFeedbackOrderDddDto;
import com.partner.dto.work.MFeedbackOrderDto;
import com.partner.dto.work.MFeedbackOrderReplyDto;
import com.partner.entity.MFeedbackOrderEntity;
import com.partner.entity.MFeedbackOrderReplyEntity;
import com.partner.entity.UFeedbackOrderTypeEntity;
import com.partner.enums.FileUploadEnum;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.DynamicFileMapper;
import com.partner.mapper.MFeedbackOrderMapper;
import com.partner.mapper.MFeedbackOrderReplyMapper;
import com.partner.mapper.SMediaAndAttachedPlatformMapper;
import com.partner.service.MFeedbackOrderService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.GetStringUtils;
import com.partner.util.TencentCloudUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.file.UploadFileParam;
import com.partner.vo.file.UploadFileVo;
import com.partner.vo.work.MFeedbackOrderDetailVo;
import com.partner.vo.work.MFeedbackOrderVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【m_feedback_order】的数据库操作Service实现
 * @createDate 2025-05-23 10:16:24
 */
@Service
@AllArgsConstructor
public class MFeedbackOrderServiceImpl extends ServiceImpl<MFeedbackOrderMapper, MFeedbackOrderEntity>
        implements MFeedbackOrderService {

    private final MFeedbackOrderMapper mFeedbackOrderMapper;

    private final SMediaAndAttachedPlatformMapper sMediaAndAttachedPlatformMapper;

    private final MFeedbackOrderReplyMapper mFeedbackOrderReplyMapper;
    private final TencentCloudUtils tencentCloudUtils;
    private final DynamicFileMapper dynamicFileMapper;

    @Override
    public IPage getPage(Page page, MFeedbackOrderDto params) {
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());
        params.setPartnerUserId(userinfo.getPartnerUserId());


        IPage<MFeedbackOrderVo> dataPage = mFeedbackOrderMapper.selectListPage(page, params);

        return dataPage;
    }

    @Override
    public MFeedbackOrderDetailVo getMFeedbackDetail(Long id) {

        MFeedbackOrderDetailVo result = mFeedbackOrderMapper.getMFeedbackDetail(id);
        if (ObjectUtils.isEmpty(result)) {
            return null;
        }


//        SMediaAndAttachedPublicDto params = new SMediaAndAttachedPublicDto(); //反馈附件
//        String baseurl = tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
//        params.setMMageAddress(baseurl);
//        params.setFkTableName("m_feedback_order");
//        params.setFkTableId(id);
//        List<FileArray> fileArrays = sMediaAndAttachedPlatformMapper.selectPublicPlatformFileArrays(params);
//        if (ObjectUtils.isNotEmpty(fileArrays)) {
//            result.setFileArray(fileArrays);
//
//        }
        //附件信息是从平台附件表获取
        result.setFileArray( sMediaAndAttachedPlatformMapper.selectPlatformFileArrays("m_feedback_order", "m_feedback_order", id));

        List<MFeedbackOrderReplyEntity> replyVoList = mFeedbackOrderReplyMapper.selectList(new LambdaQueryWrapper<MFeedbackOrderReplyEntity>()
                .eq(MFeedbackOrderReplyEntity::getFkFeedbackOrderId, id)
                .orderByDesc(MFeedbackOrderReplyEntity::getGmtCreate));
        if (ObjectUtils.isNotEmpty(replyVoList)) {
            result.setReplyVoList(replyVoList);
        }
        return result;
    }

    @Override
    public Long addWorkOrder(MFeedbackOrderDddDto adddto) {
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        String loginId = fzhUser.getLoginId();

        MFeedbackOrderEntity orderEntity = BeanCopyUtils.objClone(adddto, MFeedbackOrderEntity::new);
        orderEntity.setFkCompanyId(userinfo.getCompanyId());
        orderEntity.setFkPlatformId(userinfo.getPlatformId());
        orderEntity.setFkPlatformCode("PARTNER");
        orderEntity.setFkPlatformCreateUserId(userinfo.getPartnerUserId());
        orderEntity.setStatus(0);
        orderEntity.setGmtCreate(LocalDateTime.now());
        orderEntity.setGmtCreateUser(loginId);
        //保存反馈工单
        mFeedbackOrderMapper.insert(orderEntity);

        orderEntity.setNum(GetStringUtils.getWorkOrderNum(orderEntity.getId()));
        mFeedbackOrderMapper.updateById(orderEntity);

        if (ObjectUtils.isNotEmpty(adddto.getFileGuidArray())) {
            String[] fileArray = adddto.getFileGuidArray();
            for (String fileGuid : fileArray) {
//                SMediaAndAttachedEntity sMediaAndAttachedEntity = new SMediaAndAttachedEntity();
//                sMediaAndAttachedEntity.setFkFileGuid(fileGuid);
//                sMediaAndAttachedEntity.setFkTableName("m_feedback_order");
//                sMediaAndAttachedEntity.setFkTableId(orderEntity.getId());
//                sMediaAndAttachedEntity.setRemark("反馈附件");
//                sMediaAndAttachedEntity.setGmtCreate(LocalDateTime.now());
//                sMediaAndAttachedEntity.setGmtCreateUser(loginId);
//                sMediaAndAttachedPlatformMapper.insert(sMediaAndAttachedEntity);
                UploadFileVo media = UploadFileVo.builder()
                        .fileGuid(fileGuid)
                        .gmtCreate(new Date())
                        .gmtCreateUser(loginId)
                        .build();
                UploadFileParam uploadFileParam = UploadFileParam.builder()
                        .fileDb(StringUtils.substringBefore(FileUploadEnum.PLATFORM_CENTER_FEEDBACK_ATTACH.getFileCenter(), "."))
                        .fileTable(StringUtils.substringAfter(FileUploadEnum.PLATFORM_CENTER_FEEDBACK_ATTACH.getFileCenter(), "."))
                        .mediaDb(StringUtils.substringBefore(FileUploadEnum.PLATFORM_CENTER_FEEDBACK_ATTACH.getMediaTable(), "."))
                        .mediaTable(StringUtils.substringAfter(FileUploadEnum.PLATFORM_CENTER_FEEDBACK_ATTACH.getMediaTable(), "."))
                        .tableId(orderEntity.getId())
                        .remark("反馈附件")
                        .tableName("m_feedback_order")
                        .typeKey("m_feedback_order")
                        .mediaInfo(media)
                        .gmtCreate(new Date())
                        .gmtCreateUser(loginId)
                        .build();
                dynamicFileMapper.insertMediaRecord(uploadFileParam);
            }
        }
        return orderEntity.getId();
    }

    @Override
    public Long feedDack(MFeedbackOrderReplyDto replydto) {


        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        String loginId = fzhUser.getLoginId();
        MFeedbackOrderReplyEntity mFeedbackOrderReplyEntity = BeanCopyUtils.objClone(replydto, MFeedbackOrderReplyEntity::new);

        mFeedbackOrderReplyEntity.setGmtCreate(LocalDateTime.now());
        mFeedbackOrderReplyEntity.setGmtCreateUser(loginId);

        mFeedbackOrderReplyMapper.insert(mFeedbackOrderReplyEntity);
        return mFeedbackOrderReplyEntity.getId();
    }

    @Override
    public List<UFeedbackOrderTypeEntity> getUFeedbackOrderType() {
        List<UFeedbackOrderTypeEntity> uFeedbackOrderTypeEntities = mFeedbackOrderMapper.getUFeedbackOrderType();
        return uFeedbackOrderTypeEntities;
    }

    @Override
    public void solve(Long id) {
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        String loginId = fzhUser.getLoginId();
        MFeedbackOrderEntity mFeedbackOrder = mFeedbackOrderMapper.selectById(id);
        if (mFeedbackOrder.getStatus() == 2 || mFeedbackOrder.getStatus() == 3) {
            //代理数据不存在!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":反馈单已关闭!");
        }
        mFeedbackOrder.setStatus(2);
        mFeedbackOrder.setGmtModified(LocalDateTime.now());
        mFeedbackOrder.setGmtModifiedUser(loginId);
        mFeedbackOrderMapper.updateById(mFeedbackOrder);

    }

}




