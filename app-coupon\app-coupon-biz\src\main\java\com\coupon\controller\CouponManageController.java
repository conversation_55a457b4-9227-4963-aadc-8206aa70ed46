package com.coupon.controller;

import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.coupon.dto.*;
import com.coupon.entity.MAppIntroductionEntity;
import com.coupon.service.ICouponManageService;
import com.coupon.service.impl.CouponDistributedManageServiceImpl;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


@RestController
@RequestMapping("/couponManage")
@Inner(false)
public class CouponManageController {
    @Resource
    private ICouponManageService ICouponManageService;

    @Resource
    private CouponDistributedManageServiceImpl couponDistributedManageService;

    @PostMapping("/addCouponType")
    public R addCouponType(@RequestBody AddCouponTypeDto addCouponTypeDto) throws Exception {
        ICouponManageService.addNewCouponType(addCouponTypeDto);
        return R.ok();
    }

    @PostMapping("/getAllCouponType")
    @Inner(false)
    public R getAllCouponType(@RequestBody CouponTypeDto couponTypeDto) throws Exception {
        return R.ok(ICouponManageService.getAllCouponType(couponTypeDto));
    }

    @PostMapping("/uploadAttached")
    public R uploadAttached(@RequestParam("files") MultipartFile[] files) throws Exception {
        return R.ok(ICouponManageService.uploadAttached(files));
    }

    @PostMapping("/deleteCouponType")
    public R deleteCouponType(Long id) throws Exception {
        ICouponManageService.deleteCouponType(id);
        return R.ok();
    }

    @PostMapping("/updateCouponType")
    public R updateCouponType(@RequestBody CouponTypeDto couponTypeDto) throws Exception {
        ICouponManageService.updateCouponType(couponTypeDto);
        return R.ok();
    }

    @PostMapping("/addCouponFetchQuota")
    public R addCouponFetchQuota(@RequestBody CouponQuotaDto couponQuotaDto) throws Exception {
        ICouponManageService.addCouponQuota(couponQuotaDto);
        return R.ok();
    }

    @PostMapping("/getAllCouponQuota")
    public R getAllCouponQuota(@RequestBody CouponQuotaDto couponQuotaDto) throws Exception {
        return R.ok(ICouponManageService.getAllCouponQuoTa(couponQuotaDto));
    }

    @PostMapping("/deleteCouponQuota")
    public R deleteCouponQuota(Long id) throws Exception {
        ICouponManageService.deleteCouponQuota(id);
        return R.ok();
    }

    @PostMapping("/updateCouponQuota")
    public R updateCouponQuota(@RequestBody CouponQuotaDto couponQuotaDto) throws Exception {
        ICouponManageService.updateCouponQuota(couponQuotaDto);
        return R.ok();
    }

    @PostMapping("/importCoupon")
    public R importCouPon(@RequestParam("couponTypeId") Long couponTypeId, @RequestParam("files") MultipartFile[] files) throws Exception {
        return couponDistributedManageService.importCoupon(couponTypeId, files);
    }

    @PostMapping("/addCoupon")
    public R addCoupon(@RequestBody AddCouponDto addCouponDto) throws Exception {
        return couponDistributedManageService.addCoupon(addCouponDto);
    }

    @PostMapping("/selectFetchRec")
    public R selectFetchRec(@RequestBody CouponTakenRecDto couponTakenRecDto) throws Exception {
        return ICouponManageService.couponTakenRec(couponTakenRecDto);
    }

    @PostMapping("/importVerification")
    public R importVerification(@RequestParam("couponTypeId") Long couponTypeId, @RequestParam("files") MultipartFile[] files) throws Exception {
        return couponDistributedManageService.importVerification(couponTypeId, files);
    }

    @PostMapping("/verificationCoupon")
    public R verificationCoupon(@RequestBody VerificationCouponDto verificationCouponDto) throws Exception {
        return couponDistributedManageService.verificationCoupon(verificationCouponDto);
    }

    @PostMapping("/updateIntroduction")
    public R updateIntroduction(@RequestBody MAppIntroductionEntity appIntroductionEntity) throws Exception {
        return couponDistributedManageService.updateIntroduction(appIntroductionEntity);
    }
}
