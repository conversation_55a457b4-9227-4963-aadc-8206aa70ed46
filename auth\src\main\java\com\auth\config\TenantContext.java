//package com.auth.config;
//
///**
// * @Author:Oliver
// * @Date: 2025/1/21  14:05
// * @Version 1.0
// */
//public class TenantContext {
//
//    private static final ThreadLocal<String> tenantIdHolder = new ThreadLocal<>();
//
//    // 设置租户ID
//    public static void setTenantId(String tenantId) {
//        tenantIdHolder.set(tenantId);
//    }
//
//    // 获取租户ID
//    public static String getTenantId() {
//        return tenantIdHolder.get();
//    }
//
//    // 清理租户ID
//    public static void clear() {
//        tenantIdHolder.remove();
//    }
//}
