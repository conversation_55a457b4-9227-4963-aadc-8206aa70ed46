package com.partner.service;

import com.partner.dto.attach.UploadRequestDto;
import com.partner.vo.attach.AttachVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/11
 * @Version 1.0
 * @apiNote:附件管理-调用AIS上传和下载
 */
public interface AttachService {


    /**
     * 上传附件-调用AIS
     *
     * @param files
     * @param isPub
     * @return
     */
    List<AttachVo> uploadAttach(MultipartFile[] files, Boolean isPub);

    /**
     * 保存媒体和附件
     *
     * @param requestList
     */
    void saveMediaAndAttached(List<UploadRequestDto> requestList);
}
