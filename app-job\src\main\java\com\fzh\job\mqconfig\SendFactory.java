package com.fzh.job.mqconfig;

import com.fzh.job.service.SendBaseMessageService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class SendFactory {

    @Resource
    public Map<String, SendBaseMessageService> sendFactoryHashMap = new HashMap<>();


    public SendBaseMessageService getMessageObject(String code) {
        return sendFactoryHashMap.get(code);
    }


}
