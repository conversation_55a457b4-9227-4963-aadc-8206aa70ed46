package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.insurance.entity.CreditCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface CreditCardMapper extends BaseMapper<CreditCard> {

    /**
     * 查询提醒的信用卡-1:出账提醒 2:还款提醒
     * @param type
     * @return
     */
    List<Long> selectRemindCreditCardIds(@Param("type") Integer type);

    /**
     * 查询额度提醒的信用卡
     * @return
     */
    List<Long> selectQuotaRemindCreditCardIds();
}

