package com.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "院校大数据")
public class MInstitutionRankingParamsDto {

    @Schema(description = "年度")
    private Integer year;
    @Schema(description = "排名类型：QS=0/TIMES=1/THE=2/USNews=3/Macleans=4/ARWU=5/英国卫报=6/QS专业排名=10")
    private Integer rankingType;
    @Schema(description = "院校名称")
    private String institutionName;

    @Schema(description="国家ID")
    Long areaCountryId;

    @Schema(description="学校类型ID")
    Long institutionTypeId;

    @Schema(description="专业类型id")
    Long courseCypeGroupId;




    private String mMageAddress;
}
