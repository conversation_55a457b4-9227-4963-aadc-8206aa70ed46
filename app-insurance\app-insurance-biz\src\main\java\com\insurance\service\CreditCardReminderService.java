package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.insurance.entity.CreditCardReminder;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
public interface CreditCardReminderService extends IService<CreditCardReminder> {

    /**
     * 根据信用卡ID获取信用卡提醒信息
     *
     * @param creditCardId 信用卡ID
     * @return 信用卡提醒信息
     */
    CreditCardReminder getCreditCardReminderByCreditCard(Long creditCardId);

}
