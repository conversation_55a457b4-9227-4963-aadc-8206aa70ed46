package com.partner.controller;


import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.partner.dto.MFilePartnerDto;
import com.partner.service.FileService;
import com.partner.service.ITencentCloudService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

@Tag(description = "fileUploadDownload" , name = "小程序-文件上传下载" )
@RestController
@RequestMapping("/fileUploadDownload")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;

    private final ITencentCloudService tencentCloudService;


    /**
     *
     */
    @Operation(summary = "上传附件-私密桶" , description = "上传附件" )
    @PostMapping("uploadAttached")
    public R uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files){
        return R.ok(fileService.uploadAppendix(files,false,false));
    }


    @Operation(summary = "下载文件接口-私密桶", description = "下载文件接口-私密桶")
    @PostMapping("downloadAttached")
    public void downloadAttached(HttpServletResponse response, @RequestBody MFilePartnerDto fileParams) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        tencentCloudService.downLoadObject(fileParams, response,false,true,false);
    }

    @Operation(summary = "上传附件-公开桶" , description = "上传附件" )
    @PostMapping("uploadHtiPublicFile")
    public R uploadHtiPublicFile(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files){
        return R.ok(fileService.uploadAppendix(files,true,false));
    }

    @Operation(summary = "上传附件-公开桶+原始文件名称" , description = "上传附件" )
    @PostMapping("uploadHtiPublicFileAndfileName")
    public R uploadHtiPublicFileAndfileName(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files,
                                            @RequestParam("fileName") @NotNull(message = "文件名称不能为空") String fileName){
        return R.ok(fileService.uploadAppendix(files,true,false,fileName));
    }

    @Operation(summary = "上传附件-公开桶(PARTNER)路径" , description = "上传附件-公开桶(PARTNER)路径" )
    @PostMapping("uploadPartnerPublicFile")
    public R uploadPartnerPublicFile(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files){
        return R.ok(fileService.uploadAppendix(files,true,true));
    }


}
