package com.partner.dto.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  11:25
 * @Version 1.0
 * 保存团队成员DTO
 */
@Data
public class SaveUserDto {

    @Schema(description = "伙伴用户ID-编辑传")
    private Long partnerUserId;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "电子邮箱")
    @NotBlank(message = "电子邮箱不能为空")
    @Email(message = "电子邮箱格式不正确")
    private String email;

    @Schema(description = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private List<Long> roleIds;

    @Schema(description = "上司伙伴用户ID")
    private List<Long> superiorIds;

    @Schema(description = "国家ID数组")
    private List<Long> countryIds;

}
