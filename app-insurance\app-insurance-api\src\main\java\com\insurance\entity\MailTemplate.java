package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险公司
 */
@Data
@TableName("u_mail_template")
public class MailTemplate extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "类型Key")
    private String typeKey;

    @Schema(description = "标题，可应用到邮件标题")
    private String title;

    @Schema(description = "电邮模板")
    private String emailTemplate;

    @Schema(description = "电邮模板（英语版）")
    private String emailTemplateEn;
}
