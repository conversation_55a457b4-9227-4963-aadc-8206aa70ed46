
package com.pmp.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.common.core.util.R;
import com.pmp.dto.AreaStateDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.mapper.InstitutionCenterMapper;
import com.pmp.service.InstitutionCenterService;
import com.pmp.util.UserInfoUtils;
import com.pmp.vo.institution.*;
import com.pmp.vo.partner.PartnerUserVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/institution/")
@Tag(description = "国家-学校管理", name = "国家-学校管理")
public class InstitutionController {

    @Autowired
    private InstitutionCenterService institutionCenterService;
    @Autowired
    private UserInfoUtils userInfoUtils;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;


    @Operation(summary = "国家列表", description = "国家列表")
    @PostMapping("/countryList")
    public R<List<CountryVo>> countryList(@RequestBody DateDto dateDto) {
        return R.ok(institutionCenterService.countryList(dateDto));
    }

    @Operation(summary = "学校列表", description = "学校列表")
    @PostMapping("/institutionList")
    public R<IPage<InstitutionVo>> institutionList(@RequestBody @Valid InstitutionDto institutionDto) {
        PartnerUserVo partnerUser = userInfoUtils.getPartnerUser();
        institutionDto.setAgentId(partnerUser.getAgentId());
        institutionDto.setCompanyId(partnerUser.getCompanyId());
        institutionDto.setHighCommissionCode(null);
        return R.ok(institutionCenterService.institutionList(institutionDto));
    }

    @Operation(summary = "集团列表", description = "集团列表")
    @PostMapping("/groupList")
    public R<List<GroupVo>> groupList(@RequestBody DateDto dateDto) {
        return R.ok(institutionCenterService.groupList(dateDto));
    }

    @Operation(summary = "学校类型列表", description = "学校类型列表")
    @PostMapping("/institutionTypeList")
    public R<List<InstitutionTypeVo>> institutionTypeList() {
        return R.ok(institutionCenterService.institutionTypeList());
    }

    @Operation(summary = "州省列表", description = "州省列表")
    @PostMapping("/areaStateList")
    public R<List<AreaStateVo>> areaStateList(@RequestBody AreaStateDto areaStateDto) {
        return R.ok(institutionCenterService.areaStateList(areaStateDto));
    }

    @Operation(summary = "适用地区列表", description = "适用地区列表")
    @PostMapping("/territoryList")
    public R<List<CountryVo>> territoryList(@RequestBody DateDto dateDto) {
        return R.ok(institutionCenterService.territoryList(dateDto));
    }

    @Operation(summary = "大区列表", description = "大区列表")
    @GetMapping("/regionList")
    public R<List<RegionVo>> regionList() {
        return R.ok(institutionCenterMapper.regionList(null));
    }

    @Operation(summary = "大区列表-按照方案过滤", description = "大区列表-按照方案过滤")
    @PostMapping("/regionList")
    public R<List<RegionVo>> regionList(@RequestBody DateDto dateDto) {
        return R.ok(institutionCenterService.regionList(dateDto));
    }

    @Operation(summary = "当前用户信息", description = "当前用户信息")
    @GetMapping("/currentUserInfo")
    public R<PartnerUserVo> currentUserInfo() {
        return R.ok(userInfoUtils.getPartnerUser());
    }

}
