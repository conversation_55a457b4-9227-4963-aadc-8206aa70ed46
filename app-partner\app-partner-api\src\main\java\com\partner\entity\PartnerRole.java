package com.partner.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@TableName("m_partner_role")
@Schema(description = "伙伴角色")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PartnerRole extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "租户Id")
    private Long fkTenantId;

    @Schema(description = "公司Id")
    private Long fkCompanyId;

    @Schema(description = "学生代理Id")
    private Long fkAgentId;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色名称（英文）")
    private String roleNameEn;

    @Schema(description = "角色CODE")
    private String roleCode;

    @Schema(description = "角色描述")
    private String roleDesc;

    @Schema(description = "是否激活：0否/1是")
    private Integer isActive;

    @Schema(description = "是否默认角色：0否/1是")
    @TableField(exist = false)
    private Integer isDefault = 0;

    @Schema(description = "关联的菜单ID")
    @TableField(exist = false)
    private List<Long> menuIds;
}
