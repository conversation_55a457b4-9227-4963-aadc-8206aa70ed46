package com.pmp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pmp.dto.AreaStateDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.vo.institution.*;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  10:10
 * @Version 1.0
 */
public interface InstitutionCenterService {

    /**
     * 国家列表
     *
     * @return
     */
    List<CountryVo> countryList(DateDto dto);

    /**
     * 学校列表
     *
     * @param institutionDto
     * @return
     */
    IPage<InstitutionVo> institutionList(InstitutionDto institutionDto);

    /**
     * 学校类型列表
     *
     * @return
     */
    List<InstitutionTypeVo> institutionTypeList();


    /**
     * 集团列表
     *
     * @return
     */
    List<GroupVo> groupList(DateDto dto);

    /**
     * 学校详情
     *
     * @param institutionId
     * @return
     */
    InstitutionVo institutionDetail(Long institutionId,Integer year);


    /**
     * 州/省列表
     * @param dto
     * @return
     */
    List<AreaStateVo> areaStateList(AreaStateDto dto);

    /**
     * 适用地区列表
     * @param dto
     * @return
     */
    List<CountryVo> territoryList(DateDto dto);

    /**
     * 高佣金学校列表
     * @param dto
     * @return
     */
    List<InstitutionVo> highCommissionList(List<Long> institutionIds,Long companyId,DateDto dto,String highCommissionCode);

    /**
     * 大区列表-根据方案过滤
     * @return
     */
    List<RegionVo> regionList(DateDto dto);

}
