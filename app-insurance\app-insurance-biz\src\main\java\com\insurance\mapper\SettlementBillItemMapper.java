package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.insurance.entity.SettlementBillItem;
import com.insurance.vo.settlement.SettlementBillItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SettlementBillItemMapper extends BaseMapper<SettlementBillItem> {

    /**
     * 根据结算单Id查询结算单子项列表
     * @param billId
     * @return
     */
    List<SettlementBillItemVo> selectSettlementBillItemListByBillId(@Param("billId") Long billId);


    /**
     * 根据结算单Id查询历史结算单子项列表
     * @param billId
     * @return
     */
    List<SettlementBillItem> selectHistoricalSettlementItemListByBillId(@Param("billId") Long billId);
}
