package com.apps.api.dto.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  10:10
 * @Version 1.0
 * coupon用户保存
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SaveCouponUserDto {

    @Schema(description = "平台/应用Code")
    @NotBlank(message = "平台/应用Code不能为空")
    private String platformCode;

    @Schema(description = "平台/应用Id")
    @NotNull(message = "平台/应用Id不能为空")
    private Long platformId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "性别：0女/1男")
    private Integer gender;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "公司")
    private String company;

    @Schema(description = "手机区号")
    private String mobileAreaCode;

    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    @Schema(description = "角色编码")
    private String roleCode;
}
