package com.pmp.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.feign.annotation.NoToken;
import com.common.mybatis.tenant.FzhFeignTenantInterceptor;
import com.pmp.dto.DateDto;
import com.pmp.dto.FeignCommissionDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.vo.commission.MajorLevelTreeVo;
import com.pmp.vo.commission.MajorLevelVo;
import com.pmp.vo.commission.MergeCommissionVo;
import com.pmp.vo.institution.CountryVo;
import com.pmp.vo.institution.GroupVo;
import com.pmp.vo.institution.InstitutionTypeVo;
import com.pmp.vo.institution.InstitutionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/1/15  16:44
 * @Version 1.0
 */
@FeignClient(contextId = "remotePmpService", value = "app-pmp-biz", configuration = FzhFeignTenantInterceptor.class)
public interface RemotePmpService {

    /**
     * 获取合并佣金计划
     *
     * @param commissionDto
     * @return
     */
    @NoToken
    @PostMapping("/feign/commission/getMergeCommission")
    R<MergeCommissionVo> getMergeCommission(@RequestBody FeignCommissionDto commissionDto);

    /**
     * 国家列表
     *
     * @param dto
     * @return
     */
    @NoToken
    @PostMapping("/feign/institution/countryList")
    R<List<CountryVo>> countryList(@RequestBody DateDto dto);

    /**
     * 学校类型列表
     *
     * @return
     */
    @NoToken
    @PostMapping("/feign/institution/institutionTypeList")
    R<List<InstitutionTypeVo>> institutionTypeList();

    /**
     * 集团列表
     *
     * @param dateDto
     * @return
     */
    @NoToken
    @PostMapping("/feign/institution/groupList")
    R<List<GroupVo>> groupList(@RequestBody DateDto dateDto);

    /**
     * 课程等级列表
     *
     * @return
     */
    @NoToken
    @GetMapping("/feign/commission/selectMajorLevel")
    R<List<MajorLevelVo>> selectMajorLevel();

    /**
     * 课程等级树
     *
     * @return
     */
    @NoToken
    @GetMapping("/feign/commission/getMajorLevelTree")
    R<List<MajorLevelTreeVo>> getMajorLevelTree();

    /**
     * 学校列表
     *
     * @param institutionDto
     * @return
     */
    @NoToken
    @PostMapping("/feign/commission/institutionList")
    R<Page<InstitutionVo>> institutionList(@RequestBody @Valid InstitutionDto institutionDto);

}
