package com.partner.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "partnerapplet")
public class WechatApplet {
    @Schema(description = "小程序 appId")
    private String appId;

    @Schema(description = "小程序 appSecret")
    private String secret;

    @Schema(description = "小程序名称")
    private String name;

    @Schema(description = "小程序编码")
    private String code;

    @Schema(description = "获取token的url")
    private String stableTokenUrl;

    @Schema(description = "发送订阅消息url")
    private String sendMessageUrl;
    @Schema(description = "订阅模板信息")
    private List<TemplateConfig> templateArr;

}
