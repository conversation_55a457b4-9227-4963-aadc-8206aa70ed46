package com.insurance.vo.partner;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.insurance.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/18
 * @Version 1.0
 * @apiNote:伙伴汇率
 */
@Data
public class PartnerExchangeRate extends BaseEntity {

    @Schema(description = "日汇率Id")
    private Long id;

    @Schema(description = "参照币种编号")
    private String fkCurrencyTypeNumFrom;

    @Schema(description = "目标币种编号")
    private String fkCurrencyTypeNumTo;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "获取日期")
    private Date getDate;

    @Schema(description = "汇率")
    private BigDecimal exchangeRate;
}
