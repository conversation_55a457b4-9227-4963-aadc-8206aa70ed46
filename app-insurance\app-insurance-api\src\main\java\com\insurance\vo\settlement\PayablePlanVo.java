package com.insurance.vo.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:应付计划Vo
 */
@Data
public class PayablePlanVo {

    @Schema(description = "费率%(代理)")
    private BigDecimal commissionRate;

    @Schema(description = "佣金金额(代理)")
    private BigDecimal commissionAmount;

    @Schema(description = "应付计划Id")
    private Long payablePlanId;
}
