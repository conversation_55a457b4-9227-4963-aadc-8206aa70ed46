package com.partner.dto.student.paramsmapper;


import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "添加分配学生参数(和权限信息)")
public class StudentApportionAdd extends UserInfoParams {

    @Schema(description = "租户Id")
    private Long tenantId;


    @Schema(description = "学生ID-分配")
    private Long studentId;


    private String studentUUID;



}
