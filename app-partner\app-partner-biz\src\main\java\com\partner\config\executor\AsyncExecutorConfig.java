package com.partner.config.executor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author:Oliver
 * @Date: 2025/06/27
 * @Version 1.0
 * 线程池配置类
 */
@Configuration
@ConfigurationProperties(prefix = "async.executor")
@Data
public class AsyncExecutorConfig {

    @Schema(description = "核心线程数")
    private int corePoolSize = 5;

    @Schema(description = "最大线程数")
    private int maxPoolSize = 10;

    @Schema(description = "队列容量")
    private int queueCapacity = 100;

    @Schema(description = "线程名称前缀")
    private String threadNamePrefix = "partner-async-exec-";
}