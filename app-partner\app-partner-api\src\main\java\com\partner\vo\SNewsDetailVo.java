package com.partner.vo;

import com.partner.entity.SMediaAndAttachedEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "返回新闻详情")
public class SNewsDetailVo implements Serializable {

    @Schema(description="新闻Id")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "简介")
    private String profile;

    @Schema(description = "发布时间")
    private LocalDate publishTime;

    @Schema(description = "描述内容")
    private String description;

    @Schema(description = "查看人数")
    private int total;

    @Schema(description = "新闻附件")
    private List<SMediaAndAttachedVo> fileKeyArry;
}
