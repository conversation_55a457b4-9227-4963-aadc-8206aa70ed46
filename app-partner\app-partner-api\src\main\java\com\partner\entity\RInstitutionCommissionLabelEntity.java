package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-16 15:29:14
 */

@Data
@TableName("r_institution_commission_label")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_institution_commission_label ")
public class RInstitutionCommissionLabelEntity extends Model<RInstitutionCommissionLabelEntity>{

  @Schema(description = "院校佣金标签关系Id")
  private Long id;
 

  @Schema(description = "公司Id")
  private Long fkCompanyId;
 

  @Schema(description = "学校主键Id")
  private Long fkInstitutionId;
 

  @Schema(description = "标签Id")
  private Long fkLabelId;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
