package com.fzh.job.commonjob.partner;

import com.alibaba.fastjson.JSONObject;
import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.service.SendConfimCommissionService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.xxl.job.core.biz.model.ReturnT.FAIL;
import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

@Slf4j
@Component
public class SendConfimCommissionConfirmJob {
    @Resource
     SendConfimCommissionService sendConfimCommissionService;


    @XxlJob("SendConfimCommissionConfirmJob")
    public ReturnT<String> createJob(String paramDto) {
        //佣金  自动确认提醒
        log.error("SendConfimCommissionConfirmJob start");
        String paramDtotmp= XxlJobHelper.getJobParam();


        BaseParamDto params = JSONObject.parseObject(paramDtotmp, BaseParamDto.class);
        Boolean flag=sendConfimCommissionService.sendMQCommissionMessage(params);
        XxlJobHelper.log("This is a MStudentOfferItemAgentConfirmJob." );
        if(flag){
            return SUCCESS;
        }else {
            return FAIL;
        }
    }


}
