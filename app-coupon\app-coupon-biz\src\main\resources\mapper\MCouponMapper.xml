<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.coupon.mapper.MCouponMapper">
    <select id="getCouponTakenRec" resultType="com.coupon.vo.CouponTakenRecVo">
        SELECT
        couponType.id AS couponTypeId,
        couponType.title AS title,
        couponType.sub_title AS subTitle,
        couponType.description AS description,
        couponType.price AS price,
        couponType.is_active AS isActive,
        couponType.recommended_type AS recommendedType,
        couponType.discount_method AS discountMethod,
        couponType.code_image AS codeImageGuid,
        couponFecth.is_used AS isUsed,
        couponFecth.fk_coupon_code AS code,
        couponFecth.student_name AS studentName,
        couponFecth.student_email AS studentEmail,
        couponFecth.student_neea_id AS studentNeeaId,
        couponFecth.exam_date AS examDate,
        couponType.valid_period_start AS validPeriodStart,
        couponType.valid_period_end AS validPeriodEnd,
        couponType.redeem_period_start AS redeemPeriodStart,
        couponType.redeem_period_end AS redeemPeriodEnd
        FROM
        r_coupon_fetch AS couponFecth
        LEFT JOIN
        m_coupon_type AS couponType ON couponFecth.fk_coupon_type_id=couponType.id
        <where>
            <if test="findTakenRecDto.userid != null and findTakenRecDto.userid != '' ">
                AND couponFecth.fk_coupon_user_id = #{findTakenRecDto.userid}
            </if>
        </where>
    </select>
</mapper>