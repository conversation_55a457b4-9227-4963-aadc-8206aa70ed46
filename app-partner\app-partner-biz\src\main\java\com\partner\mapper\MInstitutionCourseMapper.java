package com.partner.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.MInstitutionCourseEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_institution_course】的数据库操作Mapper
* @createDate 2025-04-07 19:15:08
* @Entity com.partner.entity.MInstitutionCourse
*/
@Mapper
@DS("institutiondb")
public interface MInstitutionCourseMapper extends BaseMapper<MInstitutionCourseEntity> {

}




