package com.apps.service.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.apps.api.dto.AppLoginDto;
import com.apps.api.entity.SystemUserEntity;
import com.apps.api.entity.SystemUserPlatformLoginEntity;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.api.vo.system.SystemUserVo;
import com.apps.api.vo.system.UserPermissionVo;
import com.apps.exception.AppsGlobalException;
import com.apps.mapper.SystemUserMapper;
import com.apps.mapper.SystemUserPlatformLoginMapper;
import com.apps.service.SystemUserService;
import com.apps.service.strategy.AppLoginVerifyStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/1/9  16:26
 * @Version 1.0
 */
@Service("VCODE")
@Slf4j
@AllArgsConstructor
public class VCodeStrategyImpl implements AppLoginVerifyStrategy {

    private final SystemUserMapper systemUserMapper;
    private final SystemUserService systemUserService;
    private final SystemUserPlatformLoginMapper systemUserPlatformLoginMapper;

    @Override
    public UserPermissionVo appLoginVerify(AppLoginDto loginDto) {
        SystemUserEntity systemUser = systemUserMapper.selectOne(new LambdaQueryWrapper<SystemUserEntity>()
                .eq(SystemUserEntity::getMobile, loginDto.getAccount())
                .eq(SystemUserEntity::getFkFromPlatformId, loginDto.getFormPlatformId())
                .eq(SystemUserEntity::getFkFromPlatformCode, loginDto.getFormPlatformCode()));
        if (Objects.isNull(systemUser)) {
            log.error("用户不存在,手机号:{}", loginDto.getAccount());
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }

        SystemUserPlatformLoginEntity loginEntity = systemUserPlatformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getFkUserId, systemUser.getId()));
        if (Objects.isNull(loginEntity)) {
            log.error("登录失败,账号不存在:{}", JSONObject.toJSONString(loginDto));
            throw new AppsGlobalException(GlobExceptionEnum.SYS_ACCOUNT_NOT_EXISTS);
        }
        systemUserService.checkLoginUser(systemUser.getId());
        SystemUserVo systemUserVo = new SystemUserVo();
        BeanUtils.copyProperties(systemUser, systemUserVo);
        return systemUserService.getUserCacheInfo(true, loginEntity, systemUser.getId());
    }
}
