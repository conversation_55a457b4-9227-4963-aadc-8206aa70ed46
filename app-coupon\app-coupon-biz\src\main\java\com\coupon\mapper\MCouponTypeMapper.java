package com.coupon.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coupon.dto.CouponTypeDto;
import com.coupon.entity.MCouponTypeEntity;
import com.coupon.vo.GetAllCouponTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("coupon")
public interface MCouponTypeMapper extends BaseMapper<MCouponTypeEntity> {
    IPage<GetAllCouponTypeVo> getCouponTypePage(IPage<MCouponTypeEntity> page, @Param("couponTypeDto") CouponTypeDto couponTypeDto);
}
