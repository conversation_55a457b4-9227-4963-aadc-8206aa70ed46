package com.pmp.vo.commission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/13  12:05
 * @Version 1.0
 * 学校列表
 */
@Data
public class MajorLevelVo {

    @Schema(description = "课程等级Id")
    private Long levelId;

    @Schema(description = "等级名称")
    private String customName;

    @Schema(description = "等级名称-中文")
    private String customNameChn;

    @Schema(description = "是否通用等级：0否/1是")
    private Integer isGeneral;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @Schema(description = "父级课程等级Id")
    private Long parentLevelId;

    @Schema(description = "父级等级名称")
    private String parentLevelName;

    @Schema(description = "父级等级名称-中文")
    private String parentLevelNameChn;

}
