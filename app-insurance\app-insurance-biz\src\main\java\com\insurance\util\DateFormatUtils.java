package com.insurance.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;

/**
 * 日期格式化工具类
 */
public class DateFormatUtils {

    private DateFormatUtils() {
        // 工具类构造函数私有化
    }

    /**
     * 1. 将日期转换为 "MM/yyyy" 格式的字符串
     * 示例：2025年7月 -> "07/2025"
     */
    public static String formatToMonthYear(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("MM/yyyy");
        return sdf.format(date);
    }

    /**
     * 2. 将日期转换为两位补0的月份字符串
     * 示例：2025年3月 -> "03"
     */
    public static String formatToMonthOnly(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("MM");
        return sdf.format(date);
    }

    /**
     * 3. 将日期转换为年份字符串
     * 示例：2025年7月 -> "2025"
     */
    public static String formatToYearOnly(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        return sdf.format(date);
    }

    /**
     * 可选通用方法：将日期按指定格式转为字符串
     */
    public static String format(Date date, String pattern) {
        if (date == null || pattern == null || pattern.trim().isEmpty()) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 4. 将当前日期转换为中文格式的字符串
     * 示例：2025年7月3日 -> "2025年7月3日"
     */
    public static String formatCurrentDateToChinese() {
        LocalDate today = LocalDate.now();
        return String.format("%d年%d月%d日", today.getYear(), today.getMonthValue(), today.getDayOfMonth());
    }


    /**
     * 5. 将日期转换为 "yyyy-MM-dd HH:mm:ss" 格式的字符串
     * 示例：2025年7月3日 -> "202
     */
    public static String formatDateTime(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

}
