package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_agent_commission_type_agent")
public class AgentCommissionTypeAgent extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "代理佣金分类Id")
    private Long fkAgentCommissionTypeId;

    @Schema(description = "学生代理Id")
    private Long fkAgentId;

    @Schema(description = "是否只显示等级佣金：0否/1是")
    @TableField(exist = false)
    private Integer isShowOnly;

}
