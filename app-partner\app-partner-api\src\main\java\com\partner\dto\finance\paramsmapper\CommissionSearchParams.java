package com.partner.dto.finance.paramsmapper;


import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "佣金-结算页面页面-参数(+权限信息)")
public class CommissionSearchParams extends UserInfoParams {

    private Integer year;
    private Integer month;


    private Integer type;

    @Schema(description = "是否有查看权限")
    private Boolean studentFlag=false;


}
