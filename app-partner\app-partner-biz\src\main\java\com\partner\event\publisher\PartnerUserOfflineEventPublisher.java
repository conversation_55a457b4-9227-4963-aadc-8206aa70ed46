package com.partner.event.publisher;

import com.partner.event.PartnerUserOfflineEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/27
 * @Version 1.0
 * 伙伴用户下线事件发布者
 */
@Component
public class PartnerUserOfflineEventPublisher {

    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    public PartnerUserOfflineEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void publishPartnerUserOfflineEvent(List<Long> partnerUserIds, Boolean isOffline) {
        PartnerUserOfflineEvent event = new PartnerUserOfflineEvent(this, partnerUserIds, isOffline);
        eventPublisher.publishEvent(event);
    }
}
