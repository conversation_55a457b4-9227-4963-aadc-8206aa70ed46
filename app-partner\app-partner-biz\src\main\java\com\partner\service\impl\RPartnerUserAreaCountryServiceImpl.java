package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.dto.base.BaseUserInfoDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.MPartnerUserEntity;
import com.partner.entity.PartnerRole;
import com.partner.entity.RPartnerUserAreaCountryEntity;
import com.partner.mapper.MPartnerUserMapper;
import com.partner.mapper.PartnerUserPartnerRoleMapper;
import com.partner.mapper.RPartnerUserAreaCountryMapper;
import com.partner.mapper.RPartnerUserSuperiorMapper;
import com.partner.service.RPartnerUserAreaCountryService;
import com.partner.service.RPartnerUserSuperiorService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.base.PartnerUserInfoVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【r_partner_user_area_country】的数据库操作Service实现
 * @createDate 2025-01-14 21:05:44
 */
@Service
@AllArgsConstructor
@Slf4j
public class RPartnerUserAreaCountryServiceImpl extends ServiceImpl<RPartnerUserAreaCountryMapper, RPartnerUserAreaCountryEntity> implements RPartnerUserAreaCountryService {

    private final RPartnerUserAreaCountryMapper rPartnerUserAreaCountryMapper;
    private final MPartnerUserMapper mPartnerUserMapper;
    private final RPartnerUserSuperiorMapper rPartnerUserSuperiorMapper;
    private final PartnerUserPartnerRoleMapper userPartnerRoleMapper;
    private final RPartnerUserSuperiorService userSuperiorService;


    @Override
    public PartnerUserInfoVo getPartnerInfo(BaseUserInfoDto paramsDto) {
        PartnerUserInfoVo resultUserVo = BeanCopyUtils.objClone(paramsDto, PartnerUserInfoVo::new);
        log.info("获取用户基础数据,参数:{}", JSONObject.toJSONString(paramsDto));
        MPartnerUserEntity partnerUser = mPartnerUserMapper.selectOne(new LambdaQueryWrapper<MPartnerUserEntity>()
                .eq(MPartnerUserEntity::getFkUserId, paramsDto.getUserId())
                .eq(MPartnerUserEntity::getFkAgentId, paramsDto.getAgentId())
        );
        log.info("Partner用户信息:{}", JSONObject.toJSONString(partnerUser));
        if (ObjectUtil.isEmpty(partnerUser)) {
            log.error("用户信息为空,参数:{}", JSONObject.toJSONString(paramsDto));
            return resultUserVo;
        }
        resultUserVo.setPartnerUserId(partnerUser.getId());
        resultUserVo.setAgentId(partnerUser.getFkAgentId());
        resultUserVo.setCompanyId(partnerUser.getFkCompanyId());
        resultUserVo.setIsAdmin(partnerUser.getIsAdmin());

        List<Long> countryIds = rPartnerUserAreaCountryMapper.selectList(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                        .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUser.getId()))
                .stream().map(RPartnerUserAreaCountryEntity::getFkAreaCountryId).collect(Collectors.toList());
        resultUserVo.setAreaCountryIds(countryIds);

        //角色信息
        List<PartnerRole> partnerRoles = userPartnerRoleMapper.selectRoleByPartnerUserId(partnerUser.getId());
        if (CollectionUtils.isNotEmpty(partnerRoles)) {
            resultUserVo.setRoleIds(partnerRoles.stream().map(PartnerRole::getId).collect(Collectors.toList()));
            resultUserVo.setRoleNames(partnerRoles.stream().map(PartnerRole::getRoleName).collect(Collectors.toList()));
        }

        //权限信息
        List<String> permissionKeys = userPartnerRoleMapper.selectMenuPermissionKeyByPartnerUserId(partnerUser.getId());
        resultUserVo.setPermissionKeys(permissionKeys);

        //查询下级用户
        Set<Long> allSubUserIds = userSuperiorService.getAllSubUserIds(partnerUser.getId());
        List<Long> allSubUserIdList = Objects.isNull(allSubUserIds) ? Collections.emptyList() : new ArrayList<>(allSubUserIds);
        resultUserVo.setLevelPartnerUserIds(allSubUserIdList);
        return resultUserVo;

//        List<Long> childPartnerUserIdList = new ArrayList<>();
//        Set<Long> visited = new HashSet<>(); // 避免循环
//
//        int i = 0;
//        while (visited.size() > 0 || i == 0) {
//            List<Long> params = new ArrayList<>();//查询条件
//            if (i == 0) {
//                params.add(partnerUser.getId());
//            } else {
//                params.addAll(visited);
//            }
//            visited.clear();
//            if (ObjectUtil.isNotEmpty(params)) {
//                List<Long> levelArry = rPartnerUserSuperiorMapper.selectLevelChildList(params);
//                if (ObjectUtil.isNotEmpty(levelArry)) {
//                    for (Long partnerUserId : levelArry) {
//                        if (!childPartnerUserIdList.contains(partnerUserId)) {
//                            childPartnerUserIdList.add(partnerUserId);
//                            visited.add(partnerUserId);
//                        }
//                    }
//                }
//            }
//
//            i++;
//            if (i >= 1000) {
//                break;
//            }
//        }
//        List<Long> levelPartnerUserIds = new ArrayList<>();
//        if (ObjectUtil.isNotEmpty(childPartnerUserIdList)) {
//            levelPartnerUserIds.add(partnerUser.getId());
//            levelPartnerUserIds.addAll(childPartnerUserIdList);
//        } else {
//            levelPartnerUserIds.add(partnerUser.getId());
//        }
//        resultUserVo.setLevelPartnerUserIds(levelPartnerUserIds);
//        return resultUserVo;
    }

    public List<Long> getSupUserIdList(Long partnerUserId) {
        List<Long> subUserIdList = new ArrayList<>();
        Long params = partnerUserId; // 查询条件
        int i = 0;
        while (ObjectUtil.isNotEmpty(params)) {
            UserInfoParams supUserInfo = rPartnerUserSuperiorMapper.selectLevelSupList(params);//查询父级
            params = null;
            if (ObjectUtil.isNotEmpty(supUserInfo)) {
                if (!subUserIdList.contains(supUserInfo.getUserId())) {
                    subUserIdList.add(supUserInfo.getUserId());
                    params = supUserInfo.getPartnerUserId();
                }
            } else {
                params = null;
            }
            i++;
            if (i >= 1000) {
                break;
            }
        }
        return subUserIdList;
    }

    //清空上级缓存
    public void deleteSupCache(Long partnerUserId) {
        List<Long> subUserIdList = getSupUserIdList(partnerUserId);
        if (ObjectUtils.isNotEmpty(subUserIdList)) {
            for (Long userId : subUserIdList) {
                try {
                    Boolean userFlag = UserInfoParamsUtils.deleteUserInfoParams(2l, "PARTNER", userId);
                } catch (Exception e) {
                    log.error("清空父级用户缓存失败");
                }

            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePartnerCountry(Long partnerUserId, List<Long> countryIds) {
        if (Objects.isNull(partnerUserId)) {
            return;
        }
        if (CollectionUtils.isEmpty(countryIds)) {
            this.remove(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>().eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUserId));
            return;
        }

        FzhUser user = SecurityUtils.getUser();
        //当前用户的国家
        List<Long> currentCountryIds = this.list(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                        .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUserId))
                .stream().map(RPartnerUserAreaCountryEntity::getFkAreaCountryId).collect(Collectors.toList());

        //获取新增的国家
        List<Long> addCountryIds = countryIds.stream()
                .filter(id -> !currentCountryIds.contains(id)).collect(Collectors.toList());

        //获取需要删除的国家
        List<Long> delCountryIds = currentCountryIds.stream()
                .filter(id -> !countryIds.contains(id)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addCountryIds)) {
            List<RPartnerUserAreaCountryEntity> saveList = addCountryIds.stream()
                    .map(country -> {
                        RPartnerUserAreaCountryEntity userAreaCountry = new RPartnerUserAreaCountryEntity();
                        userAreaCountry.setFkPartnerUserId(partnerUserId);
                        userAreaCountry.setFkTenantId(Long.valueOf(user.getFkTenantId()));
                        userAreaCountry.setFkAreaCountryId(country);
                        userAreaCountry.setGmtCreate(LocalDateTime.now());
                        userAreaCountry.setGmtModified(LocalDateTime.now());
                        userAreaCountry.setGmtCreateUser(user.getLoginId());
                        userAreaCountry.setGmtModifiedUser(user.getLoginId());
                        return userAreaCountry;
                    }).collect(Collectors.toList());
            this.saveBatch(saveList);
        }

        if (CollectionUtils.isNotEmpty(delCountryIds)) {
            this.remove(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                    .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUserId)
                    .in(RPartnerUserAreaCountryEntity::getFkAreaCountryId, delCountryIds));
        }
    }
}




