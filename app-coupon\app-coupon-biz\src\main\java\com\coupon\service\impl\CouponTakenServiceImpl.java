package com.coupon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.coupon.dto.CouponTakenDto;
import com.coupon.dto.CouponTypeDto;
import com.coupon.dto.FindTakenRecDto;
import com.coupon.entity.*;
import com.coupon.mapper.*;
import com.coupon.service.ICouponTakenService;
import com.coupon.vo.CouponTakenRecVo;
import com.coupon.vo.GetAllCouponTypeVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Service
public class CouponTakenServiceImpl implements ICouponTakenService {

    @Resource
    private CouponManageServiceImpl couponManageServiceImpl;

    @Resource
    private MUserMapper mUserMapper;

    @Resource
    private MCouponFetchQuotaMapper couponFetchQuotaMapper;

    @Resource
    private RCouponFetchMapper couponFetchMapper;

    @Resource
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Resource
    private MCouponMapper couponMapper;

    @Resource
    private MFileCouponMapper fileCouponMapper;

    @Resource
    private MAppIntroductionMapper appIntroductionMapper;

    @Override
    public IPage<GetAllCouponTypeVo> getAllCouponType(CouponTakenDto couponTakenDto) throws Exception {
        CouponTypeDto couponTypeDto = new CouponTypeDto();
        couponTypeDto.setCurrent(couponTakenDto.getCurrent());
        couponTypeDto.setSize(couponTakenDto.getSize());
        IPage<GetAllCouponTypeVo> getAllCouponTypeVoIPage = couponManageServiceImpl.getAllCouponType(couponTypeDto);
        List<GetAllCouponTypeVo> getAllCouponTypeVoList = getAllCouponTypeVoIPage.getRecords();
        QueryWrapper<MUserEntity> mUserEntityQueryWrapper = new QueryWrapper<>();
        mUserEntityQueryWrapper.eq("mobile", couponTakenDto.getMobile());
        MUserEntity mUserEntity = mUserMapper.selectOne(mUserEntityQueryWrapper);
        // 未上架的过滤
        getAllCouponTypeVoList.removeIf(couponTypeVo -> !couponTypeVo.isActive());
        if (mUserEntity == null) {
            return getAllCouponTypeVoIPage;
        }
        String role = mUserEntity.getRole();
        Long userId = mUserEntity.getId();
        for (GetAllCouponTypeVo couponTypeVo : getAllCouponTypeVoList) {
            // 获取角色配额
            QueryWrapper<MCouponFetchQuotaEntity> mCouponFetchQuotaEntityQueryWrapper = new QueryWrapper<>();
            mCouponFetchQuotaEntityQueryWrapper.eq("fk_coupon_type_id", couponTypeVo.getCouponTypeId());
            mCouponFetchQuotaEntityQueryWrapper.eq("fk_role_code", role);
            MCouponFetchQuotaEntity mCouponFetchQuotaEntity = couponFetchQuotaMapper.selectOne(mCouponFetchQuotaEntityQueryWrapper);
            if (mCouponFetchQuotaEntity == null) {
                couponTypeVo.setRemainderQuota(0);
                couponTypeVo.setQuota(0);
                continue;
            }
            QueryWrapper<RCouponFetchEntity> rCouponFetchEntityQueryWrapper = new QueryWrapper<>();
            rCouponFetchEntityQueryWrapper.eq("fk_coupon_type_id", couponTypeVo.getCouponTypeId());
            rCouponFetchEntityQueryWrapper.eq("fk_coupon_user_id", userId);
            List<RCouponFetchEntity> rCouponFetchEntities = couponFetchMapper.selectList(rCouponFetchEntityQueryWrapper);
            couponTypeVo.setQuota(mCouponFetchQuotaEntity.getQuota());
            couponTypeVo.setRemainderQuota(couponTypeVo.getQuota() - rCouponFetchEntities.size());
            couponTypeVo.setTaken(!rCouponFetchEntities.isEmpty());
            // 已经领取数量
            couponTypeVo.setIsTakenNum(rCouponFetchEntities.size());
        }
        getAllCouponTypeVoList.sort(Comparator.comparing(GetAllCouponTypeVo::isLapse));
        return getAllCouponTypeVoIPage;
    }

    @Override
    public synchronized List<String> fetchCoupon(CouponTakenDto couponTakenDto) throws Exception {
        FzhUser fzhUser = SecurityUtils.getUser();
        QueryWrapper<MUserEntity> mUserEntityQueryWrapper = new QueryWrapper<>();
        mUserEntityQueryWrapper.eq("mobile", couponTakenDto.getMobile());
        MUserEntity mUserEntity = mUserMapper.selectOne(mUserEntityQueryWrapper);
        if (mUserEntity == null) {
            throw new Exception("用户不存在");
        }
        String role = mUserEntity.getRole();
        Long userId = mUserEntity.getId();
        // 获取角色配额
        QueryWrapper<MCouponFetchQuotaEntity> mCouponFetchQuotaEntityQueryWrapper = new QueryWrapper<>();
        mCouponFetchQuotaEntityQueryWrapper.eq("fk_coupon_type_id", couponTakenDto.getCouponTypeId());
        mCouponFetchQuotaEntityQueryWrapper.eq("fk_role_code", role);
        MCouponFetchQuotaEntity mCouponFetchQuotaEntity = couponFetchQuotaMapper.selectOne(mCouponFetchQuotaEntityQueryWrapper);
        if (mCouponFetchQuotaEntity == null) {
            throw new Exception("没有权限领取此优惠券");
        }
        QueryWrapper<RCouponFetchEntity> rCouponFetchEntityQueryWrapper = new QueryWrapper<>();
        rCouponFetchEntityQueryWrapper.eq("fk_coupon_type_id", couponTakenDto.getCouponTypeId());
        rCouponFetchEntityQueryWrapper.eq("fk_coupon_user_id", userId);
        List<RCouponFetchEntity> rCouponFetchEntities = couponFetchMapper.selectList(rCouponFetchEntityQueryWrapper);
        List<String> code = new ArrayList<>();
        if (mCouponFetchQuotaEntity.getQuota() > rCouponFetchEntities.size()) {
            QueryWrapper<MCouponEntity> mCouponEntityQueryWrapper = new QueryWrapper<>();
            mCouponEntityQueryWrapper.eq("fk_coupon_type_id", couponTakenDto.getCouponTypeId());
            mCouponEntityQueryWrapper.eq("is_taken", false);
            mCouponEntityQueryWrapper.eq("is_used", false);
            mCouponEntityQueryWrapper.eq("is_active", true);
            List<MCouponEntity> mCouponEntities = couponMapper.selectList(mCouponEntityQueryWrapper);
            if (mCouponEntities == null || mCouponEntities.isEmpty()) {
                throw new Exception("优惠卷余额不足");
            }
            // 剩余领取数量
            int remainderNum = Math.min(mCouponEntities.size(), mCouponFetchQuotaEntity.getQuota() - rCouponFetchEntities.size());
            if (remainderNum > 0) {
                MCouponEntity mCouponEntity = mCouponEntities.get(0);
                mCouponEntity.setIsTaken(true);
                mCouponEntity.setGmtModified(LocalDateTime.now());
                mCouponEntity.setGmtModifiedUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
                couponMapper.updateById(mCouponEntity);
                code.add(mCouponEntity.getCode());
                RCouponFetchEntity rCouponFetchEntity = new RCouponFetchEntity();
                rCouponFetchEntity.setFkCouponCode(mCouponEntity.getCode());
                rCouponFetchEntity.setFkCouponUserId(userId);
                rCouponFetchEntity.setFkCouponTypeId(couponTakenDto.getCouponTypeId());
                rCouponFetchEntity.setStudentName(couponTakenDto.getStudentName());
                rCouponFetchEntity.setStudentEmail(couponTakenDto.getStudentEmail());
                rCouponFetchEntity.setStudentNeeaId(couponTakenDto.getStudentNeeaId());
                rCouponFetchEntity.setExamDate(couponTakenDto.getExamDate());
                rCouponFetchEntity.setIsUsed(false);
                rCouponFetchEntity.setGmtCreate(LocalDateTime.now());
                rCouponFetchEntity.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
                couponFetchMapper.insert(rCouponFetchEntity);
            }
            /*for (int i = 0; i < remainderNum; i++) {
                MCouponEntity mCouponEntity = mCouponEntities.get(i);
                mCouponEntity.setIsTaken(true);
                mCouponEntity.setGmtCreate(LocalDateTime.now());
                mCouponEntity.setGmtCreateUser("admin");
                couponMapper.updateById(mCouponEntity);
                code.add(mCouponEntity.getCode());
                RCouponFetchEntity rCouponFetchEntity = new RCouponFetchEntity();
                rCouponFetchEntity.setFkCouponCode(mCouponEntity.getCode());
                rCouponFetchEntity.setFkUserId(userId);
                rCouponFetchEntity.setFkCouponTypeId(couponTakenDto.getCouponTypeId());
                rCouponFetchEntity.setIsUsed(false);
                rCouponFetchEntity.setGmtCreate(LocalDateTime.now());
                rCouponFetchEntity.setGmtCreateUser("admin");
                couponFetchMapper.insert(rCouponFetchEntity);
            }*/
        } else {
            throw new Exception("已经达到领取上限");
        }
        return code;
    }

    @Override
    public R findTakenRec(FindTakenRecDto findTakenRecDto) throws Exception {
        QueryWrapper<MUserEntity> mUserEntityQueryWrapper = new QueryWrapper<>();
        mUserEntityQueryWrapper.eq("mobile", findTakenRecDto.getMobile());
        MUserEntity mUserEntity = mUserMapper.selectOne(mUserEntityQueryWrapper);
        if (mUserEntity == null) {
            return R.restResult(null, 0, "用户不存在");
        }
        Long userId = mUserEntity.getId();
        findTakenRecDto.setUserid(userId);
        Page page = new Page();
        page.setCurrent(findTakenRecDto.getCurrent());
        page.setSize(findTakenRecDto.getSize());
        IPage<CouponTakenRecVo> couponTakenRecVoIPage = couponMapper.getCouponTakenRec(page, findTakenRecDto);
        List<CouponTakenRecVo> couponTakenRecVos = couponTakenRecVoIPage.getRecords();
        for (CouponTakenRecVo couponTakenRecVo : couponTakenRecVos) {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            if ((couponTakenRecVo.getValidPeriodStart() != null && now.isBefore(couponTakenRecVo.getValidPeriodStart())) ||
                    (couponTakenRecVo.getValidPeriodEnd() != null && now.isAfter(couponTakenRecVo.getValidPeriodEnd()))) {
                couponTakenRecVo.setLapse(true);
            }
            QueryWrapper<SMediaAndAttachedEntity> sMediaAndAttachedEntityQueryWrapper = new QueryWrapper<>();
            sMediaAndAttachedEntityQueryWrapper.eq("fk_table_name", "m_coupon_type");
            sMediaAndAttachedEntityQueryWrapper.eq("fk_table_id", couponTakenRecVo.getCouponTypeId());
            List<SMediaAndAttachedEntity> sMediaAndAttachedEntities = sMediaAndAttachedMapper.selectList(sMediaAndAttachedEntityQueryWrapper);
            if (!sMediaAndAttachedEntities.isEmpty()) {
                SMediaAndAttachedEntity sMediaAndAttachedEntity = sMediaAndAttachedEntities.get(0);
                QueryWrapper<MFileCoupon> mFileCouponQueryWrapper = new QueryWrapper<>();
                mFileCouponQueryWrapper.eq("file_guid", sMediaAndAttachedEntity.getFkFileGuid());
                List<MFileCoupon> mFileCoupons = fileCouponMapper.selectList(mFileCouponQueryWrapper);
                if (!mFileCoupons.isEmpty()) {
                    couponTakenRecVo.setImagePath(mFileCoupons.get(0).getFilePath());
                }
            }
            QueryWrapper<MFileCoupon> mFileCouponQueryWrapper1 = new QueryWrapper<>();
            mFileCouponQueryWrapper1.eq("file_guid", couponTakenRecVo.getCodeImageGuid());
            List<MFileCoupon> mFileCoupons = fileCouponMapper.selectList(mFileCouponQueryWrapper1);
            if (!mFileCoupons.isEmpty()) {
                couponTakenRecVo.setCodeImagePath(mFileCoupons.get(0).getFilePath());
            }
        }
        couponTakenRecVos.sort(Comparator.comparing(CouponTakenRecVo::isLapse));
        return R.restResult(couponTakenRecVoIPage, 0, "");
    }

    @Override
    public R getIntroduction() throws Exception {
        QueryWrapper<MAppIntroductionEntity> mAppIntroductionEntityQueryWrapper = new QueryWrapper<>();
        mAppIntroductionEntityQueryWrapper.eq("is_active",true);
        mAppIntroductionEntityQueryWrapper.orderByDesc("gmt_create");
        List<MAppIntroductionEntity> resultList = appIntroductionMapper.selectList(mAppIntroductionEntityQueryWrapper);
        if (!resultList.isEmpty()) {
            return R.restResult(resultList.get(0).getIntroduction(), 0, "");
        }
        return R.restResult("", 0, "");
    }
}
