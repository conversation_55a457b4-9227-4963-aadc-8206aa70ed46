<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.SaletestMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.Saletest">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name
    </sql>
</mapper>
