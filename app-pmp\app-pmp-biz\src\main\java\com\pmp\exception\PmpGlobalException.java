package com.pmp.exception;

import com.apps.api.enums.GlobExceptionEnum;

/**
 * <AUTHOR>
 */
public class PmpGlobalException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private int errorCode;
    private String errorMessage;


    public PmpGlobalException(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public PmpGlobalException(GlobExceptionEnum codeEnum) {
        this.errorCode = codeEnum.getCode();
        this.errorMessage = codeEnum.getMsg();
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

}
