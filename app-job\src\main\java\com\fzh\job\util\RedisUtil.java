package com.fzh.job.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisUtil {
    private static RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        RedisUtil.redisTemplate = redisTemplate;
    }

    public static String getWechatToken(String redisKey) {
        String token ="";
        Object cachedData = redisTemplate.opsForValue().get(redisKey);
        if (Objects.nonNull(cachedData)) {
            if(cachedData instanceof String){
                token=(String)cachedData;
            }
        }
        return token;
    }

    public static void setWechatToken(String redisKey, String token){
        Object cachedData = redisTemplate.opsForValue().get(redisKey);
        if (Objects.isNull(cachedData)) {
            redisTemplate.opsForValue().set(redisKey, token);
            redisTemplate.expire(redisKey, 5, TimeUnit.MINUTES);
        }

    }

}
