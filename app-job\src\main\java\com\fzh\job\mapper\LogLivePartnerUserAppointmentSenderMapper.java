package com.fzh.job.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.LogLivePartnerUserAppointmentSenderEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【log_live_partner_user_appointment_sender】的数据库操作Mapper
* @createDate 2025-04-18 19:57:21
* @Entity com.partner.entity.LogLivePartnerUserAppointmentSender
*/

@Mapper
@DS("partner")
public interface LogLivePartnerUserAppointmentSenderMapper extends BaseMapper<LogLivePartnerUserAppointmentSenderEntity> {

}




