package com.partner.exception;

import com.partner.enums.PartnerErrorEnum;

public class PartnerExceptionInfo extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private int errorCode;
    private String errorMessage;

    public PartnerExceptionInfo(int errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public PartnerExceptionInfo(PartnerErrorEnum partnerErrorEnum) {
        super(partnerErrorEnum.errorMessage);
        this.errorCode = partnerErrorEnum.errorCode;
        this.errorMessage = partnerErrorEnum.errorMessage;
    }


    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
