package com.coupon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.common.core.util.R;
import com.coupon.dto.CouponTakenDto;
import com.coupon.dto.FindTakenRecDto;
import com.coupon.vo.CouponTakenRecVo;
import com.coupon.vo.GetAllCouponTypeVo;

import java.util.List;

public interface ICouponTakenService {
    IPage<GetAllCouponTypeVo> getAllCouponType(CouponTakenDto couponTakenDto) throws Exception;

    List<String> fetchCoupon(CouponTakenDto couponTakenDto) throws Exception;

    R findTakenRec(FindTakenRecDto findTakenRecDto) throws Exception;

    R getIntroduction()throws Exception;
}
