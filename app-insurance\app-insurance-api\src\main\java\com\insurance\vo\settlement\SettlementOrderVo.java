package com.insurance.vo.settlement;

import com.insurance.entity.InsuranceOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:佣金结算订单列表
 */
@Data
public class SettlementOrderVo extends InsuranceOrder {

    @Schema(description = "费率%(代理)")
    private BigDecimal commissionRate;

    @Schema(description = "佣金金额(代理)")
    private BigDecimal commissionAmount;

    @Schema(description = "结算状态1:可结算;2结算中;4已结算")
    private Integer settlementStatus;

    @Schema(description = "应付计划Id")
    private Long payablePlanId;

    @Schema(description = "代理提交结算批次编号")
    private String numOptBatch;

    @Schema(description = "订单结算ID")
    private Long orderSettlementId;
}
