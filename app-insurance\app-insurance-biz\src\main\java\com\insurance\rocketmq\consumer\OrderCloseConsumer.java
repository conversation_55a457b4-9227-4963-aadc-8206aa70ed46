package com.insurance.rocketmq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insurance.config.RedisService;
import com.insurance.constant.RedisConstant;
import com.insurance.entity.InsuranceOrder;
import com.insurance.enums.OrderStatusEnum;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.service.InsuranceOrderService;
import com.payment.enums.PayStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:订单下单失败消费者
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = "insurance_order_close_topic",
        consumerGroup = "insurance_order_close_topic_consumer_group",
        maxReconsumeTimes = 3,
        consumeMode = ConsumeMode.CONCURRENTLY
)
public class OrderCloseConsumer implements RocketMQListener<OrderMsg> {

    @Autowired
    private RedisService redisService;
    @Autowired
    private InsuranceOrderMapper orderMapper;


    @Override
    public void onMessage(OrderMsg orderMsg) {
        log.info("订单关闭队列消费者收到消息:{}", JSON.toJSONString(orderMsg));
        InsuranceOrder order = orderMapper.selectByOrderNum(orderMsg.getOrderNo());
        if (Objects.isNull(order)) {
            log.error("订单关闭队列处理失败订单不存在,订单号:{}");
            return;
        }
        if (order.getOrderStatus().equals(PayStatusEnum.UN_PAID.getCode())) {
            // 再用 Redis 验证一下是否还有未过期的 key（避免误关）
            boolean hasKey = redisService.hasKey(RedisConstant.CLOSE_ORDER + orderMsg.getOrderNo());
            if (Boolean.FALSE.equals(hasKey)) {
                //Redis key 已过期，说明超过10分钟未支付，可以关闭
                order.setOrderStatus(OrderStatusEnum.FAIL.getCode());
                order.setGmtModified(new Date());
                orderMapper.updateById(order);
                log.info("订单关闭成功，订单号:{}", orderMsg.getOrderNo());
            } else {
                // 用户可能刚好在支付，还没同步过来，跳过
                log.info("订单仍在有效期内，忽略关单，订单号:{}", orderMsg.getOrderNo());
            }
        } else {
            log.info("订单状态不是处于待支付，忽略关单，订单信息:{}", JSONObject.toJSONString(order));
        }
    }
}
