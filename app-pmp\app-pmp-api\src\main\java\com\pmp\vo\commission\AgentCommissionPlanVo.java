package com.pmp.vo.commission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pmp.vo.institution.InstitutionVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/25  17:44
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentCommissionPlanVo {

    @Schema(description = "代理佣金方案ID")
    private Long id;

    @Schema(description = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @Schema(description = "代理佣金分类Id")
    private Long fkAgentCommissionTypeId;

    @Schema(description = "学校提供商佣金方案Id")
    private Long fkInstitutionProviderCommissionPlanId;

    @Schema(description = "方案名称")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @Schema(description = "集团名称")
    private String group;

    @Schema(description = "集团中文名称")
    private String groupChn;

    @Schema(description = "适用国家/地区")
    private List<PlanTerritoryVo> territories;

    @Schema(description = "学校详情")
    private InstitutionVo institutionDetail;

    @Schema(description = "佣金明细")
    private AgentCommissionListVo commissions;

}
