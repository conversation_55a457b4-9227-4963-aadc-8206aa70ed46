package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RStudentUuidEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【r_student_uuid】的数据库操作Mapper
* @createDate 2024-12-24 18:33:41
* @Entity com.partner.entity.RStudentUuid
*/
@Mapper
@DS("saledb")
public interface RStudentUuidMapper extends BaseMapper<RStudentUuidEntity> {

    int insertSelective(RStudentUuidEntity record);

    RStudentUuidEntity selectByUUID(String UUID);

    List<RStudentUuidEntity> selecteBatchByUUIDs(@Param("UUIDs") List<String> UUIDs);
}




