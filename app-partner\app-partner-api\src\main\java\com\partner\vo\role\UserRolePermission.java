package com.partner.vo.role;

import com.partner.entity.PartnerRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:用户角色权限
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class UserRolePermission {

    @Schema(description = "伙伴用户ID")
    private Long partnerUserId;

    @Schema(description = "系统用户ID")
    private Long systemUserId;

    @Schema(description = "代理ID")
    private Long agentId;

    @Schema(description = "分公司ID")
    private Long companyId;

    @Schema(description = "角色ID集合")
    private List<Long> roleIds;

    @Schema(description = "角色名称ID集合")
    private List<String> roleNames;

    @Schema(description = "角色集合")
    private List<PartnerRole> roles;

    @Schema(description = "权限Key集合")
    private List<String> permissions;

    @Schema(description = "是否超级管理员0否1是")
    private Integer isAdmin = 0;

    @Schema(description = "代理最新合同状态:0-无合同/1-有合同/2-未签署/3-待审核/4-审核通过/-4审核驳回/5续约在/6生效中/7已过期")
    private Integer latestAgentContractStatus;

    @Schema(description = "国家集合")
    private List<Long> areaCountryIds;

}
