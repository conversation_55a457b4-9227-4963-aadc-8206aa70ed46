package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.MCompanyEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_company】的数据库操作Mapper
* @createDate 2025-01-23 17:18:31
* @Entity com.partner.entity.MCompany
*/
@Mapper
@DS("permissiondb")
public interface MCompanyMapper extends BaseMapper<MCompanyEntity> {

}




