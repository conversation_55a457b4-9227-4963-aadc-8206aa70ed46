package com.partner.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 发版信息用户已读记录实体
 * <p>
 * 用于记录用户对发版信息的阅读状态，实现智能推送和防重复通知功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("r_release_info_user_read")
@Schema(description = "发版信息用户已读记录")
@EqualsAndHashCode(callSuper = true)
public class RReleaseInfoUserReadEntity extends Model<RReleaseInfoUserReadEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发版信息ID
     */
    @Schema(description = "发版信息ID")
    @JsonProperty("fkReleaseInfoId")
    @TableField("fk_release_info_id")
    private Long fkReleaseInfoId;

    /**
     * 平台应用代码
     */
    @Schema(description = "平台应用代码")
    @JsonProperty("fkPlatformCode")
    @TableField("fk_platform_code")
    private String fkPlatformCode;

    /**
     * 平台用户ID（合作伙伴用户ID）
     */
    @Schema(description = "平台用户ID（合作伙伴用户ID）")
    @JsonProperty("fkPlatformUserId")
    @TableField("fk_platform_user_id")
    private Long fkPlatformUserId;

    /**
     * 创建时间（阅读时间）
     */
    @Schema(description = "创建时间（阅读时间）")
    @JsonProperty("gmtCreate")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 创建用户（阅读用户登录账号）
     */
    @Schema(description = "创建用户（阅读用户登录账号）")
    @JsonProperty("gmtCreateUser")
    @TableField("gmt_create_user")
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonProperty("gmtModified")
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * 修改用户
     */
    @Schema(description = "修改用户")
    @JsonProperty("gmtModifiedUser")
    @TableField("gmt_modified_user")
    private String gmtModifiedUser;

}