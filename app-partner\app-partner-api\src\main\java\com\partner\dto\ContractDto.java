package com.partner.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "合同签订")
public class ContractDto {
    @Schema(description = "合同ID")
    @NotNull(message = "合同ID不能为空")
    private Long contractId;


    @Schema(description = "签名文件")
    @NotBlank(message = "签字文件信息不能为空")
    private String signature;


}
