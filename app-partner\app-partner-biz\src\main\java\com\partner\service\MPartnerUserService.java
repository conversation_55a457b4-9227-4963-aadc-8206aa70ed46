package com.partner.service;

import com.apps.api.dto.partner.UpdatePartnerLockDto;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.*;
import com.partner.dto.agent.SwitchPartnerAgent;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.student.StudentApportionAddDto;
import com.partner.dto.student.StudentApportionParamsDto;
import com.partner.entity.MPartnerUserEntity;
import com.partner.vo.*;
import com.partner.vo.agent.PartnerUserAgentVo;
import com.partner.vo.my.MPayablePlanMyDetailVo;
import com.partner.vo.student.StudentApportionVo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【m_partner_user】的数据库操作Service
* @createDate 2024-12-20 14:13:45
*/
public interface MPartnerUserService extends IService<MPartnerUserEntity> {

    /**
     * 学生管理-查询分配人员列表
     * @param agentId
     * @return
     */
    List<StudentApportionVo>  getApportionPartnerUser();

    Long putStudentApportionAddDto(StudentApportionAddDto addDto);

    /**
     * 保存团队人员
     * @param saveTeamMemberDto
     * @return
     */
    Long saveTeamMember(SaveTeamMemberDto saveTeamMemberDto);


    Long updateTeamCountry(SaveOrUpdateTeamDto saveTeamMemberDto);


    /**
     * 团队成员列表
     * @return
     */
    List<TeamMemberVo> getTeamMemberList(Integer year,Boolean initRoleName);


    /**
     * 锁定用户
     * @param lockDto
     */
    void lockUser(UpdatePartnerLockDto lockDto);

    /**
     * 获取团队架构树
     * @return
     */
    List<TeamMemberTreeVo> getTeamMemberTree(Integer year);

    /**
     * 获取顾问列表
     * @return
     */
    List<TeamMemberVo> getConsultantList();

    TeamDataSumVo getTeamDataSum(TeamDataSumDto params);
    List<TeamMemberDetailVo> getTeamMemberDetailList(TeamDataSumDto params);
    /**
     * 角色列表
     * @return
     */
    List<Map<String,Object>> getRoleList();

    /**
     * 是否具有团队页面权限
     * @return
     */
    Boolean hasTeamPermission();

    /**
     * 重置伙伴密码
     * @param partnerPasswordDto
     */
    void resetPartnerPassword(ResetPartnerPasswordDto partnerPasswordDto);

    /**
     * 我的团队-修改团队成员
     *
     * @param memberDto
     * @return
     */
    void updateTeamMember(UpdateTeamMemberDto memberDto);

    Boolean hasViewCommission();

    TeamDetailVo getTeamDetailByPartnerUserId(Long partnerUserId);


    /**
     * 获取伙伴用户代理列表
     * @return
     */
    List<PartnerUserAgentVo> getPartnerUserAgentList();

    /**
     * 切换代理
     * @param switchPartnerAgent
     * @return
     */
    Boolean switchPartnerAgent(SwitchPartnerAgent switchPartnerAgent);

    /**
     * 获取当前用户代理信息
     * @return
     */
    PartnerUserAgentVo getCurrentPartnerUserAgent();


    public List<MPayablePlanVo> getConvAmount(List<MPayablePlanMyDetailVo> planAmountArr, UserInfoParams userinfo);
}
