package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "团队数据汇总")
public class TeamDataSumVo {
    @Schema(description = "团队人数")
    private int tramPeopleTotal;
    @Schema(description = "学生总数")
    private int studentTotal;
    @Schema(description = "完成学生")
    private int completeStudentTotal;

    @Schema(description = "人均成交人数")
    private String avgCompleteTotal;

    @Schema(description = "人均成交金额")
    private BigDecimal avgCompleteAmount;

}
