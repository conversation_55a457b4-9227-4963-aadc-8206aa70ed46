<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RSettlementSignatureMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.RSettlementSignatureEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkSettlementBillId" column="fk_settlement_bill_id" jdbcType="BIGINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_settlement_bill_id,gmt_create,
        gmt_create_user,gmt_modified,gmt_modified_user,
        signature
    </sql>
</mapper>
