package com.partner.mapper;

import com.apps.api.entity.SystemUserEntity;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【system_user】的数据库操作Mapper
* @createDate 2025-04-25 16:54:21
* @Entity com.partner.entity.SystemUser
*/
@Mapper
@DS("systemdb")
public interface SystemUserMapper extends BaseMapper<SystemUserEntity> {

}




