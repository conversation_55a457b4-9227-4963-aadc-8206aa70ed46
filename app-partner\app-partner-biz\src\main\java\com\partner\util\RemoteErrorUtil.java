package com.partner.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author:Oliver
 * @Date: 2025/4/28
 * @Version 1.0
 * @apiNote:
 */
public class RemoteErrorUtil {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final Pattern MSG_PATTERN = Pattern.compile("\"msg\":\"(.*?)\"");

    public static String extractInnerErrorMsg(String fullJsonMsg) {
        if (fullJsonMsg == null || fullJsonMsg.isEmpty()) {
            return null;
        }

        try {
            // 1. 解析最外层 JSON，提取 "msg" 字段
            JsonNode rootNode = OBJECT_MAPPER.readTree(fullJsonMsg);
            JsonNode msgNode = rootNode.get("msg");
            if (msgNode == null || msgNode.isNull()) {
                return null;
            }

            String rawMsg = msgNode.asText();

            // 2. 取消转义，把 \" 转成 "
            String unescapedMsg = rawMsg.replace("\\\"", "\"");

            // 3. 在unescapedMsg里继续找符合 {"code":xxx,"msg":"xxx",...} 结构的 JSON
            //    提取最里面的 "msg" 字段
            Matcher matcher = MSG_PATTERN.matcher(unescapedMsg);
            if (matcher.find()) {
                return matcher.group(1);
            }

        } catch (Exception e) {
            // 出异常直接返回null，不影响流程
            e.printStackTrace();
        }

        return null;
    }

    public static String extractRemoteErrorMsg(String rawJson) {
        try {
            if (rawJson == null) {
                return null;
            }
            JsonNode root = OBJECT_MAPPER.readTree(rawJson);
            String msg = root.path("msg").asText();
            if (msg != null) {
                // 查找内部的 [{...}]
                int startIndex = msg.indexOf("[{");
                int endIndex = msg.indexOf("}]", startIndex) + 1;
                if (startIndex >= 0 && endIndex > startIndex) {
                    String innerJson = msg.substring(startIndex + 1, endIndex);
                    JsonNode innerNode = OBJECT_MAPPER.readTree(innerJson);
                    return innerNode.path("msg").asText();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
