package com.partner.dto.work;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MFeedbackOrderReplyDto  {

    @Schema(description = "反馈工单Id")
    @NotNull(message = "反馈工单Id为空")
    private Long fkFeedbackOrderId;


    @Schema(description = "回复内容")
    @NotBlank(message = "回复内容为空")
    private String reply;





}
