package com.payment.service.wx;

import com.alibaba.fastjson.JSONObject;
import com.payment.config.PayConfig;
import com.payment.dto.common.RefundRequestDto;
import com.payment.service.PayConfigService;
import com.payment.util.PemUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class WxPayRefundService {

    private final PayConfigService payConfigService;


    public Refund refund(RefundRequestDto requestDto) {
        // 1. 根据 appId 和 payType 拿到对应的支付配置（例如 JSAPI）
        PayConfig config = payConfigService.getByAppIdAndType(requestDto.getAppId(), "WX_JSAPI");
        if (Objects.isNull(config)) {
            log.error("找不到匹配的支付配置，appId={}", requestDto.getAppId());
            throw new RuntimeException("找不到匹配的支付配置");
        }
        // 2. 发起退款
        Refund refund = this.refund(
                requestDto.getTransactionId(),
                requestDto.getOutRefundNo(),
                requestDto.getTotalFee(),
                requestDto.getRefundFee(),
                requestDto.getReason(),
                config.getNotifyUrl(),
                config);
        return refund;

    }

    /**
     * 创建微信退款服务
     *
     * @param payConfig
     * @return
     * @throws Exception
     */
    private RefundService buildRefundService(PayConfig payConfig) throws Exception {
        PrivateKey privateKey;
        try (InputStream inputStream = new ClassPathResource(payConfig.getWxPrivateKeyPath()).getInputStream()) {
            privateKey = PemUtil.loadPrivateKey(inputStream);
        }

        PublicKey publicKey;
        try (InputStream inputStream = new ClassPathResource(payConfig.getWxPublicKeyPath()).getInputStream()) {
            publicKey = PemUtil.loadPublicKey(inputStream);
        }

        Config config = new RSAPublicKeyConfig.Builder()
                .merchantId(payConfig.getMchId())
                .merchantSerialNumber(payConfig.getMchSerialNo())
                .privateKey(privateKey)
                .apiV3Key(payConfig.getApiV3Key())
                .publicKey(publicKey)
                .publicKeyId(payConfig.getWxPlatformCertId())
                .build();

        return new RefundService.Builder().config(config).build();
    }


    /**
     * 调起微信退款服
     *
     * @param payConfig 支付配置
     * @return 退款服务
     */
    public Refund refund(String transactionId, String outRefundNo, Long total, Long refundAmount, String reason, String notifyUrl, PayConfig payConfig) {
        try {
            RefundService refundService = buildRefundService(payConfig);

            CreateRequest request = new CreateRequest();
            request.setTransactionId(transactionId);
            request.setOutRefundNo(outRefundNo);
            request.setReason(reason);
            request.setNotifyUrl(notifyUrl);
            AmountReq amount = new AmountReq();
            amount.setRefund(refundAmount);
            amount.setTotal(total);
            amount.setCurrency("CNY");
            request.setAmount(amount);

            log.info("调用微信退款,参数:{}", JSONObject.toJSONString(request));
            Refund refund = refundService.create(request);
            log.info("微信退款成功: {}", JSONObject.toJSONString(refund));
            return refund;
        } catch (Exception e) {
            log.error("微信退款失败", e);
            throw new RuntimeException("微信退款失败", e);
        }
    }


    /**
     * 处理微信退款回调
     *
     * @param request
     * @return
     */
    public String handleRefundNotify(HttpServletRequest request) {
        try {
            String body = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String serial = request.getHeader("Wechatpay-Serial");
            String signature = request.getHeader("Wechatpay-Signature");

            log.info("【微信退款回调】请求头 serial={}, signature={}, nonce={}, timestamp={}", serial, signature, nonce, timestamp);
            log.info("【微信退款回调】请求体: {}", body);

            PayConfig payConfig = payConfigService.getByPlatformCertId(serial);
            if (payConfig == null) {
                log.error("找不到匹配支付配置，serial={}", serial);
                return "FAIL";
            }

            PrivateKey privateKey;
            try (InputStream is = new ClassPathResource(payConfig.getWxPrivateKeyPath()).getInputStream()) {
                privateKey = PemUtil.loadPrivateKey(is);
            }

            PublicKey publicKey;
            try (InputStream is = new ClassPathResource(payConfig.getWxPublicKeyPath()).getInputStream()) {
                publicKey = PemUtil.loadPublicKey(is);
            }

            NotificationConfig notificationConfig = new RSAPublicKeyConfig.Builder()
                    .merchantId(payConfig.getMchId())
                    .merchantSerialNumber(payConfig.getMchSerialNo())
                    .privateKey(privateKey)
                    .apiV3Key(payConfig.getApiV3Key())
                    .publicKey(publicKey)
                    .publicKeyId(payConfig.getWxPlatformCertId())
                    .build();

            RequestParam param = new RequestParam.Builder()
                    .serialNumber(serial)
                    .nonce(nonce)
                    .signature(signature)
                    .timestamp(timestamp)
                    .body(body)
                    .build();

            NotificationParser parser = new NotificationParser(notificationConfig);
            Refund refund = parser.parse(param, Refund.class);
            log.info("【微信退款回调】验签成功=======================>");
            log.info("【微信退款回调】解密成功: {}", JSONObject.toJSONString(refund));
            return "SUCCESS";
        } catch (Exception e) {
            log.error("【微信退款回调】处理失败", e);
            log.error("【微信退款回调】处理失败: {}", e.getMessage());
            return "FAIL";
        }
    }
}
