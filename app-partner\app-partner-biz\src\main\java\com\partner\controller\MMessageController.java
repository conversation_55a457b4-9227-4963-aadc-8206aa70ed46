package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.MMessageParamsDto;
import com.partner.service.MMessageService;
import com.partner.vo.MMessageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Tag(description = "mmessage" , name = "小程序-消息管理" )
@RestController
@RequestMapping("/mmessage")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MMessageController {

    private final MMessageService mMessageService;
    @Operation(summary = "首页-消息信息" , description = "首页-消息信息" )
    @SysLog("首页-消息信息" )
    @GetMapping("/searchMessage" )
    public R  searchMessage(@ParameterObject @Valid MMessageParamsDto params){
        return R.ok(mMessageService.searchMessage(params));
    }





}
