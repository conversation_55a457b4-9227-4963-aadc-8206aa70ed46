package com.pmp.vo.institution;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/5/21
 * @Version 1.0
 * @apiNote:自定义标签
 */
@Data
public class CustomizeLabelVo {

    @Schema(description = "自定义标签Id")
    private Long labelId;

    @Schema(description = "自定义标签类型Id")
    private Long labelTypeId;

    @Schema(description = "自定义标签类型名称")
    private String labelTypeName;

    @Schema(description = "自定义标签类型Key")
    private String labelTypeKey;

    @Schema(description = "标签名称")
    private String labelName;

    @Schema(description = "标签关键字")
    private String labelKey;

    @Schema(description = "颜色code")
    private String color;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "icon名称")
    private String icoName;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
