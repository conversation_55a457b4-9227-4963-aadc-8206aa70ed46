
package com.insurance.controller;


import com.common.core.util.R;
import com.insurance.dto.settlement.DateDto;
import com.insurance.dto.settlement.SubmitSettlementDto;
import com.insurance.service.SettlementBillService;
import com.insurance.service.SettlementService;
import com.insurance.util.ExchangeRateUtils;
import com.insurance.vo.settlement.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/settlement")
@Tag(description = "佣金管理", name = "佣金管理")
public class SettlementController {

    @Autowired
    private SettlementService settlementService;
    @Autowired
    private SettlementBillService settlementBillService;
    @Autowired
    private ExchangeRateUtils exchangeRateUtils;

    @Operation(summary = "佣金订单列表")
    @GetMapping("/getSettlementOrderList")
    public R<List<SettlementOrderVo>> getSettlementOrderList(@Parameter(description = "结算状态1:可结算;2结算中;4已结算") Integer settlementStatus,
                                                             @Parameter(description = "时间范围(年月)：yyyy-MM") String date) {
        return R.ok(settlementService.getSettlementOrderList(settlementStatus, date));
    }

    @Operation(summary = "获取代理账户列表")
    @GetMapping("/getAgentAccountList")
    public R<Map<String, List<AgentAccountVo>>> getAgentAccountList() {
        return R.ok(settlementBillService.getAgentAccountList());
    }

    @Operation(summary = "对账单详情")
    @GetMapping("/getSettlementBillDetail/{id}")
    public R<SettlementBillDetailVo> getSettlementBillDetail(@PathVariable("id") Long id) {
        return R.ok(settlementBillService.getSettlementBillDetail(id));
    }

    @Operation(summary = "历史账单列表")
    @PostMapping("/getHistoricalBillList")
    public R<List<HistoricalBillVo>> getHistoricalBillList(@RequestBody DateDto dateDto) {
        return R.ok(settlementBillService.getHistoricalBillList(dateDto.getStartDate(), dateDto.getEndDate()));
    }

    @Operation(summary = "提交结算")
    @PostMapping("/submitSettlement")
    public R<String> submitSettlement(@Valid @RequestBody SubmitSettlementDto submitSettlementDto) {
        settlementBillService.submitSettlement(submitSettlementDto);
        return R.ok("提交成功");
    }

    @Operation(summary = "佣金订单统计")
    @GetMapping("/getSettlementOrderStatistics")
    public R<SettlementOrderStatisticsVo> getSettlementOrderStatistics(@Parameter(description = "时间范围(年月)：yyyy-MM") String date) {
        return R.ok(settlementService.getSettlementOrderStatistics(date));
    }

    @Operation(summary = "对账单下载")
    @GetMapping("/downloadSettlementBill/{id}")
    public void getSettlementBillDetail(@PathVariable("id") Long id, HttpServletResponse response) {
        settlementBillService.downloadSettlementBill(id, response);
    }

    @Operation(summary = "获取汇率")
    @GetMapping("/getRateDetail")
    public R<RateDetail> getRateDetail(String from, String to) {
        return R.ok(exchangeRateUtils.getRateDetail(from, to));
    }

}
