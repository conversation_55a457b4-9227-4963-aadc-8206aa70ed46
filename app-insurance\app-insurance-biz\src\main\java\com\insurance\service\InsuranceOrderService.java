package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.insurance.dto.order.EncryptCreditCardDto;
import com.insurance.dto.order.GeneralOrderRequest;
import com.insurance.dto.order.PayParameterDto;
import com.insurance.entity.InsuranceOrder;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.vo.insurance.order.EncryptCreditCardVo;
import com.insurance.vo.insurance.order.OrderDetailVo;
import com.insurance.vo.insurance.order.OrderStatisticsVo;
import com.insurance.vo.insurance.order.PayAmountVo;
import com.payment.vo.PayResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/4/30
 * @Version 1.0
 * @apiNote:
 */
public interface InsuranceOrderService extends IService<InsuranceOrder> {

    /**
     * 发送订单请求
     */
    void sendRequest(String orderNo);

    /**
     * 处理处理中的订单-请求中队列
     *
     * @param orderMsg
     */
    void handleProcessingOrder(OrderMsg orderMsg);

    /**
     * 处理成功的订单-成功中队列
     *
     * @param orderMsg
     */
    void handleSuccessOrder(OrderMsg orderMsg);

    /**
     * 处理失败的订单-失败中队列
     *
     * @param orderMsg
     * @param type     1:常规下单失败 2:直接下单失败
     */
    void handleFailOrder(OrderMsg orderMsg, Integer type);

    /**
     * 提交订单
     *
     * @param request
     */
    InsuranceOrder submitOrder(GeneralOrderRequest request);

    /**
     * 加密信用卡号
     *
     * @param creditCardDto
     * @return
     */
    EncryptCreditCardVo encryptCreditCard(EncryptCreditCardDto creditCardDto);

    /**
     * 获取订单列表
     *
     * @param orderStatus
     * @return
     */
    List<InsuranceOrder> getOrderList(Integer orderStatus, String date);

    /**
     * 订单详情
     *
     * @param orderId
     * @return
     */
    OrderDetailVo getOrderDetail(Long orderId);

    /**
     * 订单统计
     *
     * @param date
     * @return
     */
    OrderStatisticsVo getOrderStatistics(String date);

    /**
     * 提交微信支付订单
     *
     * @param request
     * @return
     */
    PayResponse submitWxPayOrder(GeneralOrderRequest request);

    /**
     * 获取支付金额
     *
     * @param originalAmount
     * @return
     */
    PayAmountVo getPayAmount(BigDecimal originalAmount);

    /**
     * 获取微信支付状态
     *
     * @param orderNo
     * @return
     */
    Integer getWxPayStatus(String orderNo);

    /**
     * 生成订单-微信支付
     *
     * @param request
     * @return
     */
    PayAmountVo generateOrder(GeneralOrderRequest request);

    /**
     * 获取微信支付参数
     *
     * @param payParameterDto
     * @return
     */
    PayResponse getPayParameter(PayParameterDto payParameterDto);

}
