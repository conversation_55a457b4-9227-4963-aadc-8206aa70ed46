package com.partner.dto.attach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/7/11
 * @Version 1.0
 * @apiNote:上传文件参数-ais
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadRequestDto {

    @Schema(description ="文件")
    private MultipartFile[] files;

    @Schema(description ="是否存放私密桶")
    private Boolean isPrivateBucket;

    @Schema(description ="文件对应的微服务名称, 如：销售中心,华通伙伴中心")
    private String serviceName;

    @Schema(description ="类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @Schema(description ="创建人")
    private String gmtCreateUser;

    @Schema(description ="文件桶前缀,如/appendix")
    private String prefix;
}
