package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.MInstitutionRankingParamsDto;
import com.partner.entity.MInstitutionRankingEntity;
import com.partner.entity.MStudentEntity;
import com.partner.vo.MInstitutionRankingVo;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.CourseTypeGroupCombox;
import com.partner.vo.combox.InstitutionTypeCombox;
import com.partner.vo.combox.RankingTypeCombox;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_institution_ranking】的数据库操作Mapper
* @createDate 2024-12-23 19:15:17
* @Entity com.partner.entity.MInstitutionRanking
*/
@Mapper
public interface MInstitutionRankingMapper extends BaseMapper<MInstitutionRankingEntity> {
    List<CountryCombox> getCountryCombox(MInstitutionRankingParamsDto dto);

    List<InstitutionTypeCombox> getInstitutionTypeCombox(MInstitutionRankingParamsDto dto);


    List<CourseTypeGroupCombox> getCourseTypeGroupCombox(MInstitutionRankingParamsDto dto);



    List<RankingTypeCombox> getRankingCombox(MInstitutionRankingParamsDto dto);

    Page<MInstitutionRankingVo> getPageInstitutionRanking(Page page, @Param("query") MInstitutionRankingParamsDto dto);


}




