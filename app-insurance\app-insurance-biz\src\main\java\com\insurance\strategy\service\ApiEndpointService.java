package com.insurance.strategy.service;

import com.insurance.config.InsuranceApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 获取自动化下单API
 */
@Service
@Slf4j
public class ApiEndpointService {

    private final InsuranceApiConfig apiEndpointConfig;

    @Autowired
    public ApiEndpointService(InsuranceApiConfig apiEndpointConfig) {
        this.apiEndpointConfig = apiEndpointConfig;
    }

    public String getApiUrl(String code) {
        String url = apiEndpointConfig.getApiUrl(code);
        if (url == null) {
            log.error("API地址未配置, 订单公司: {}", code);
            return "";
        }
        return url;
    }
}