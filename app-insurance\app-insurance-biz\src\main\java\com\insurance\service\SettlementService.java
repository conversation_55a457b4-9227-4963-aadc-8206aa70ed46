package com.insurance.service;

import com.insurance.dto.settlement.SubmitSettlementDto;
import com.insurance.vo.insurance.order.OrderStatisticsVo;
import com.insurance.vo.settlement.SettlementOrderStatisticsVo;
import com.insurance.vo.settlement.SettlementOrderVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:
 */
public interface SettlementService {

    /**
     * 佣金订单列表
     * @param settlementStatus
     * @param date
     * @return
     */
    List<SettlementOrderVo> getSettlementOrderList(Integer settlementStatus, String date);



    /**
     * 佣金订单统计
     * @param date
     * @return
     */
    SettlementOrderStatisticsVo getSettlementOrderStatistics(String date);
}
