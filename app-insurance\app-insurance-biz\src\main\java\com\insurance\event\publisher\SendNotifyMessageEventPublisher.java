package com.insurance.event.publisher;

import com.insurance.event.SendNotifyMessageEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/27
 * @Version 1.0
 * 伙伴用户下线事件发布者
 */
@Component
public class SendNotifyMessageEventPublisher {

    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    public SendNotifyMessageEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void publishPartnerUserOfflineEvent(Long creditCardId, String messageType,String orderNo, Date payTime) {
        SendNotifyMessageEvent event = new SendNotifyMessageEvent(this, creditCardId, messageType, orderNo, payTime);
        eventPublisher.publishEvent(event);
    }
}
