package com.partner.config;

import com.alibaba.fastjson.JSONObject;
import com.partner.vo.file.UploadFileParam;
import com.partner.vo.file.UploadFileVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;

/**
 * 动态SQL
 */
@Slf4j
public class DynamicSqlProvider {

    /**
     * 插入文件记录-文件库
     *
     * @return
     */
    public String insertFileRecord(@Param("param") UploadFileParam param, @Param("vo") UploadFileVo vo) {
        log.info("vo:{}", JSONObject.toJSONString( vo));
        String s = new StringBuilder()
                .append("INSERT INTO ")
                .append(param.getFileDb()).append(".").append(param.getFileTable())
                .append(" (file_guid, file_type_orc, file_name_orc, file_name, file_path, file_key, gmt_create, gmt_create_user) ")
                .append("VALUES (")
                .append("#{vo.fileGuid}, #{vo.fileTypeOrc}, #{vo.fileNameOrc}, #{vo.fileName}, ")
                .append("#{vo.filePath}, #{vo.fileKey}, #{vo.gmtCreate}, #{vo.gmtCreateUser})")
                .toString();
        log.info("insertSql:{}",s);
        return s;
    }


    /**
     * 插入媒体附件记录-媒体库
     *
     * @param param
     * @return
     */
    public String insertMediaRecord(@Param("param") UploadFileParam param) {
        return new StringBuilder()
                .append("INSERT INTO ")
                .append(param.getMediaDb()).append(".").append(param.getMediaTable())
                .append(" (fk_file_guid, fk_table_name, fk_table_id, type_key, index_key, link, remark, gmt_create, gmt_create_user) ")
                .append("VALUES (")
                .append("#{param.mediaInfo.fileGuid}, #{param.tableName}, #{param.tableId}, #{param.typeKey}, ")
                .append("#{param.indexKey}, #{param.mediaInfo.filePath}, #{param.remark}, #{param.gmtCreate}, #{param.gmtCreateUser})")
                .toString();
    }
}
