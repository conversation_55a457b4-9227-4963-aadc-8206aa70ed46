package com.fzh.job.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.mapper.MStudentOfferItemAgentConfirmMapper;
import com.fzh.job.service.MStudentOfferItemAgentConfirmService;
import com.partner.entity.MStudentOfferItemAgentConfirmEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Service
public class MStudentOfferItemAgentConfirmServiceImpl extends ServiceImpl<MStudentOfferItemAgentConfirmMapper, MStudentOfferItemAgentConfirmEntity>
        implements MStudentOfferItemAgentConfirmService{
    private static final Logger log = LoggerFactory.getLogger(MStudentOfferItemAgentConfirmServiceImpl.class);
    @Resource
    MStudentOfferItemAgentConfirmMapper mStudentOfferItemAgentConfirmMapper;


    @Override
    public Boolean offerConfirm(BaseParamDto paramDto) {
        Boolean flag=true;

        LocalDate today = LocalDate.now();
        LocalDate lastMonth = today.with(today.minusMonths(1));
        LocalDate firstDayOfLastMonth=lastMonth.withDayOfMonth(1);//上个月第一天日期
        LocalDate firstDayOfToMonth = today.withDayOfMonth(1);//当月第一天日期

        if(ObjectUtils.isEmpty(paramDto)){
            paramDto=new BaseParamDto();
        }
        paramDto.setFirstDayOfLastMonth(firstDayOfLastMonth);
        paramDto.setFirstDayOfToMonth(firstDayOfToMonth);


        //查询partner使用中的代理
        List<Long> allAgent=
        mStudentOfferItemAgentConfirmMapper.getAllAgentId();

        if(allAgent!=null && allAgent.size()>0){
            for(Long agentId:allAgent){
                try{
                    paramDto.setAgentId(agentId);
                    mStudentOfferItemAgentConfirmMapper.insertAffirmOfferItem(paramDto);
                }catch (Exception e){
                    log.error("代理名单确认异常 {} {} ",e.getMessage(),agentId,e);
                    flag=false;
                }
            }

        }


        return flag;
    }
}
