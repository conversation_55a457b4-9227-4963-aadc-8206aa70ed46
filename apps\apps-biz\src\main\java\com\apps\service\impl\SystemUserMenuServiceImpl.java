package com.apps.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.dto.system.SaveUserMenuDto;
import com.apps.api.entity.SystemUserMenuEntity;
import com.apps.mapper.SystemUserMenuMapper;
import com.apps.service.SystemUserMenuService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SystemUserMenuServiceImpl extends ServiceImpl<SystemUserMenuMapper, SystemUserMenuEntity> implements SystemUserMenuService {

    private final SystemUserMenuMapper systemUserMenuMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserMenu(SaveUserMenuDto userMenuDto) {
        if (CollectionUtils.isEmpty(userMenuDto.getMenuIds())) {
            this.remove(new LambdaQueryWrapper<SystemUserMenuEntity>().eq(SystemUserMenuEntity::getFkUserId, userMenuDto.getUserId()));
            return;
        }
        //先查询用户目前已有的菜单权限
        List<Long> currentMenuIds = systemUserMenuMapper.selectList(new LambdaQueryWrapper<SystemUserMenuEntity>()
                        .eq(SystemUserMenuEntity::getFkUserId, userMenuDto.getUserId()))
                .stream().map(SystemUserMenuEntity::getFkMenuId).collect(Collectors.toList());
        //获取新增的菜单权限
        List<Long> addMenuIds = userMenuDto.getMenuIds().stream()
                .filter(menuId -> !currentMenuIds.contains(menuId)).collect(Collectors.toList());
        //获取需要删除的菜单权限
        List<Long> delMenuIds = currentMenuIds.stream()
                .filter(menuId -> !userMenuDto.getMenuIds().contains(menuId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addMenuIds)) {
            List<SystemUserMenuEntity> saveList = addMenuIds.stream()
                    .map(menu -> {
                        SystemUserMenuEntity userMenu = new SystemUserMenuEntity();
                        userMenu.setFkUserId(userMenuDto.getUserId());
                        userMenu.setFkMenuId(menu);
                        userMenu.setPermission(1);
                        userMenu.setGmtCreate(LocalDateTime.now());
                        userMenu.setGmtModified(LocalDateTime.now());
                        return userMenu;
                    }).collect(Collectors.toList());
            this.saveBatch(saveList);
        }
        if (CollectionUtils.isNotEmpty(delMenuIds)) {
            this.remove(new LambdaQueryWrapper<SystemUserMenuEntity>()
                    .eq(SystemUserMenuEntity::getFkUserId, userMenuDto.getUserId())
                    .in(SystemUserMenuEntity::getFkMenuId, delMenuIds));
        }
    }
}
