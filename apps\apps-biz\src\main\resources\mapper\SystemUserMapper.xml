<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.apps.mapper.SystemUserMapper">

    <select id="selectMaxId" resultType="java.lang.Long">
        SELECT MAX(id) AS max_id
        FROM system_user
    </select>

    <select id="getInviteTemplate" resultType="com.apps.api.vo.InviteTemplateVo">
        select title, email_template as template
        from app_partner_center.u_mail_template
        where type_key = 'INVITE_TO_REGISTER'
        limit 1
    </select>

    <select id="getResetPasswordTemplate" resultType="com.apps.api.vo.InviteTemplateVo">
        select title, email_template as template
        from app_partner_center.u_mail_template
        where type_key = 'RESET_PASSWORD_APP'
        limit 1
    </select>

    <select id="selectTemplateByType" resultType="com.apps.api.vo.InviteTemplateVo">
        SELECT title, email_template AS template
        FROM app_partner_center.u_mail_template
        <where>
            <choose>
                <when test="type == 1">
                    type_key = 'INVITE_TO_REGISTER'
                </when>
                <when test="type == 2">
                    type_key = 'RESET_PASSWORD_APP'
                </when>
                <when test="type == 3">
                    type_key = 'REGISTER_PARTNER_USER'
                </when>
            </choose>
        </where>
        limit 1
    </select>
</mapper>