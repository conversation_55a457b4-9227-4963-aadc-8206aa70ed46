
package com.pmp.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.pmp.dto.CommissionDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.FeignCommissionDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.mapper.MajorLevelCustomMapper;
import com.pmp.service.AgentCommissionMajorLevelCustomService;
import com.pmp.service.AgentCommissionPlanService;
import com.pmp.service.InstitutionCenterService;
import com.pmp.vo.commission.MajorLevelTreeVo;
import com.pmp.vo.commission.MajorLevelVo;
import com.pmp.vo.commission.MergeCommissionVo;
import com.pmp.vo.institution.CountryVo;
import com.pmp.vo.institution.GroupVo;
import com.pmp.vo.institution.InstitutionTypeVo;
import com.pmp.vo.institution.InstitutionVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/feign/")
@Tag(description = "openFeign管理", name = "远程调度管理")
public class FeignController {

    @Autowired
    private AgentCommissionPlanService commissionPlanService;
    @Autowired
    private InstitutionCenterService institutionCenterService;
    @Autowired
    private MajorLevelCustomMapper majorLevelCustomMapper;
    @Autowired
    private AgentCommissionMajorLevelCustomService levelCustomService;

    @Operation(summary = "根据学校获取佣金计划-合并方案", description = "根据学校获取佣金计划-合并方案")
    @PostMapping("/commission/getMergeCommission")
    @Inner(false)
    public R<MergeCommissionVo> getMergeCommission(@RequestBody @Valid FeignCommissionDto dto) {
        CommissionDto commissionDto = CommissionDto.builder()
                .startDate(dto.getStartDate())
                .institutionId(dto.getInstitutionId())
                .build();
        return R.ok(commissionPlanService.getMergeCommission(commissionDto, dto.getAgentId(), dto.getCompanyId(), dto.getHighCommissionCode()));
    }

    @Operation(summary = "国家列表", description = "国家列表")
    @PostMapping("/institution/countryList")
    @Inner(false)
    public R<List<CountryVo>> countryList(@RequestBody DateDto dto) {
        return R.ok(institutionCenterService.countryList(dto));

    }

    @Operation(summary = "学校类型列表", description = "学校类型列表")
    @PostMapping("/institution/institutionTypeList")
    @Inner(false)
    public R<List<InstitutionTypeVo>> institutionTypeList() {
        return R.ok(institutionCenterService.institutionTypeList());
    }

    @Operation(summary = "集团列表", description = "集团列表")
    @PostMapping("/institution/groupList")
    @Inner(false)
    public R<List<GroupVo>> groupList(@RequestBody DateDto dateDto) {
        return R.ok(institutionCenterService.groupList(dateDto));
    }

    @Operation(summary = "课程等级列表", description = "课程等级列表(可以查询全部以及通用和非通用)")
    @GetMapping("/commission/selectMajorLevel")
    @Inner(false)
    public R<List<MajorLevelVo>> selectMajorLevel(@Schema(description = "1:通用课程等级;0:自定义课程等级;不传查询全部课程等级") Integer isGeneral) {
        return R.ok(majorLevelCustomMapper.selectMajorLevel(isGeneral));
    }

    @Operation(summary = "课程等级树", description = "课程等级树")
    @GetMapping("/commission/getMajorLevelTree")
    @Inner(false)
    public R<List<MajorLevelTreeVo>> getMajorLevelTree() {
        return R.ok(levelCustomService.getMajorLevelTree());
    }

    @Operation(summary = "学校列表", description = "学校列表")
    @PostMapping("/commission/institutionList")
    @Inner(false)
    public R<IPage<InstitutionVo>> institutionList(@RequestBody @Valid InstitutionDto institutionDto) {
        IPage<InstitutionVo> iPage = institutionCenterService.institutionList(institutionDto);
        // 构造 Page 对象（Page 是 IPage 的实现类）
        Page<InstitutionVo> page = new Page<>();
        page.setRecords(iPage.getRecords());
        page.setTotal(iPage.getTotal());
        page.setSize(iPage.getSize());
        page.setCurrent(iPage.getCurrent());
        page.setPages(iPage.getPages());
        return R.ok(page);
    }

}
