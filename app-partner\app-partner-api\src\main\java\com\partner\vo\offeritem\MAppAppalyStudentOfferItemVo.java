package com.partner.vo.offeritem;

import com.partner.entity.MAppStudentOfferItemEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

@Data
@Schema(description = "草稿-加申-申请返回详情")
public class MAppAppalyStudentOfferItemVo extends MAppStudentOfferItemEntity {

    @Schema(description = "申请学校")
    private String institutionName;
    @Schema(description = "申请学校中文名")
    private String institutionNameChn;
    @Schema(description = "申请学校课程")
    private String courseName;
    @Schema(description = "申请步骤")
    private String stepName;

    @Schema(description = "加申状态：0可以删除/2正式生效")
    private Integer statusAdditional;



    public String getStepName() {
        if(getIsAdditional() && Objects.isNull(getFkStudentOfferItemId()) ){
            stepName="加申审批中";
        }
        return stepName;
    }

    public Integer getStatusAdditional() {
        if(getIsAdditional() && Objects.isNull(getFkStudentOfferItemId()) ){
            statusAdditional=0;
        }
        return statusAdditional;
    }
}
