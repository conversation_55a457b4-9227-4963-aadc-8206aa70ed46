package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.MMessageParamsDto;
import com.partner.entity.MMessageEntity;
import com.partner.vo.MMessageVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("aisplatformdb")
public interface MMessageMapper  extends BaseMapper<MMessageEntity> {

    List<MMessageVo> searchMessage(MMessageParamsDto params);

}
