package com.partner.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.partner.entity.MHelpEntity;
import com.partner.service.MHelpService;
import com.partner.mapper.MHelpMapper;
import com.partner.vo.HelpVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_help】的数据库操作Service实现
* @createDate 2025-04-30 10:08:14
*/
@Service
@AllArgsConstructor
public class MHelpServiceImpl extends ServiceImpl<MHelpMapper, MHelpEntity>
    implements MHelpService{

    private final MHelpMapper mHelpMapper;


    @Override
    public List<HelpVo> getDetail() {
        List<HelpVo> helpVo = new ArrayList<>();
        helpVo=mHelpMapper.getDetail();

        return helpVo;
    }
}




