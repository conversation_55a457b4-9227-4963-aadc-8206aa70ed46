package com.pmp.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.common.file.core.FileTemplate;
import com.pmp.config.ConnectTencentCloud;
import com.pmp.config.FileProperties;
import com.pmp.dto.DownloadDto;
import com.pmp.service.FileService;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.COSObjectInputStream;
import com.qcloud.cos.model.GetObjectRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * @Author:Oliver
 * @Date: 2025/9/1
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    private final FileTemplate fileTemplate;
    private final FileProperties fileProperties;

    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname:hti-ais-images-dev-1301376564}")
    private String imageBucketName;

    //文件存储
    @Value("${file.tencentcloudfile.bucketname:hti-ais-files-dev-1301376564}")
    private String fileBucketName;

    @Override
    public void getFile(String bucket, String fileKey, HttpServletResponse response, Boolean isPrivate) {
        fileKey = StringUtils.substringAfter(fileKey, "/");
        if (StrUtil.isBlank(bucket)) {
            bucket = fileProperties.getPublicBucketName();
        }
        if (isPrivate) {
            bucket = fileProperties.getPrivateBucketName();
        }
        try (S3Object s3Object = fileTemplate.getObject(bucket, fileKey)) {
            response.setContentType("application/octet-stream; charset=UTF-8");
            IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
        } catch (Exception e) {
            log.error("文件读取异常: {}", e.getLocalizedMessage());
            throw new RuntimeException("文件下载失败");
        }
    }

    @Override
    public void downLoadObject(DownloadDto downloadDto, HttpServletResponse response, Boolean isPub) {
        String bucketName = isPub ? imageBucketName : fileBucketName;
        response.setCharacterEncoding("UTF-8");
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, downloadDto.getFileKey());
        BufferedOutputStream outputStream = null;
        COSObjectInputStream cosObjectInput = null;

        try {
            COSObject cosObject = null;
            if (!isPub) {
                cosObject = ConnectTencentCloud.getPrivateCosClient().getObject(getObjectRequest);
            } else {
                cosObject = ConnectTencentCloud.getPublicCosClient().getObject(getObjectRequest);
            }
            cosObjectInput = cosObject.getObjectContent();
            outputStream = new BufferedOutputStream(response.getOutputStream());

            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(downloadDto.getFileNameOrc().getBytes("utf-8"), "UTF-8")));
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 处理下载到的流
        // 这里是直接读取，按实际情况来处理
        byte[] bytes = null;
        try {
            bytes = IOUtils.toByteArray(cosObjectInput);
            outputStream.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 用完流之后一定要调用 close()
            try {
                cosObjectInput.close();
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        try {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 在流没有处理完之前，不能关闭 cosClient
            // 确认本进程不再使用 cosClient 实例之后，关闭之
            if (!isPub) {
                ConnectTencentCloud.getPrivateCosClient().shutdown();
            } else {
                ConnectTencentCloud.getPublicCosClient().shutdown();
            }

        }
    }
}
