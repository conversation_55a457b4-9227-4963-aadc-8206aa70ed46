package com.insurance.enums;

import com.insurance.entity.CreditCardReminder;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.function.Function;

/**
 * <AUTHOR>
 * 保险单类型举类
 */

@Getter
@AllArgsConstructor
public enum NotifyMessageTypeEnum {

    QUOTA_REMIND("QUOTA_REMIND", "额度不足提醒", CreditCardReminder::getIsQuotaRemind, CreditCardReminder::getQuotaRemindType),
    FAIL_REMIND("FAIL_REMIND", "支付失败提醒", CreditCardReminder::getIsFailedRemind, CreditCardReminder::getFailedRemindType),
    REPAYMENT_REMIND("REPAYMENT_REMIND", "还款提醒", CreditCardReminder::getIsRepaymentRemind, CreditCardReminder::getRepaymentRemindType),
    DISBURSEMENT_REMIND("DISBURSEMENT_REMIND", "出账提醒", CreditCardReminder::getIsRepaymentRemind, CreditCardReminder::getRepaymentRemindType),
    TRANSACTION_REMIND("TRANSACTION_REMIND", "交易提醒", CreditCardReminder::getIsTransactionRemind, CreditCardReminder::getTransactionRemindType);


    private final String code;
    private final String msg;
    private final Function<CreditCardReminder, Integer> enabledGetter;
    private final Function<CreditCardReminder, String> typeGetter;

    public static NotifyMessageTypeEnum getEnumByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
