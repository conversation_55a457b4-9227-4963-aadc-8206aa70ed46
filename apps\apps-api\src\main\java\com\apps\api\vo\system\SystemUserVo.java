package com.apps.api.vo.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/1/9  11:14
 * @Version 1.0
 */
@Data
public class SystemUserVo {

    @Schema(description = "用户Id")
    private Long id;

    @Schema(description = "租户Id")
    private Long fkTenantId;

    @Schema(description = "用户注册平台应用Id")
    private Long fkFromPlatformId;

    @Schema(description = "用户注册平台应用CODE")
    private String fkFromPlatformCode;

    @Schema(description = "用户编号，U00100000001，3位平台Id8位用户Id")
    private String num;

    @Schema(description = " 姓名（中文）")
    private String name;

    @Schema(description = "姓名（英文）")
    private String nameEn;

    @Schema(description = "姓（拼音）")
    private String lastNamePy;

    @Schema(description = "名（拼音）")
    private String firstNamePy;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "性别：0女/1男")
    private Integer gender;

    @Schema(description = "身份证号")
    private String identityCard;

    @Schema(description = "手机区号")
    private String mobileAreaCode;

    @Schema(description = "移动电话")
    private String mobile;

    @Schema(description = "电话区号")
    private String telAreaCode;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "公司名称")
    private String company;

    @Schema(description = "部门")
    private String department;

    @Schema(description = "职位")
    private String position;

    @Schema(description = "QQ号")
    private String qq;

    @Schema(description = "whatsapp号")
    private String whatsapp;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "微信昵称")
    private String wechatNickname;

    @Schema(description = "微信头像URL")
    private String wechatIconUrl;

    @Schema(description = "登录ID")
    private String loginId;

    @Schema(description = "登陆用户密码")
    private String loginPs;

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description="锁定标记，0未锁定，1已锁定，锁定后不能登录")
    private Integer isLockFlag;

    @Schema(description="删除标记，0未删除，1已删除")
    private Integer isDelFlag;
}
