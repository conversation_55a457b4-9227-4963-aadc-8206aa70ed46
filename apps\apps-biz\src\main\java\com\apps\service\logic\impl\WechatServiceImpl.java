package com.apps.service.logic.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.apps.api.entity.SystemUserPlatformLoginEntity;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.api.vo.wechat.WxLoginVo;
import com.apps.config.AppConfig;
import com.apps.config.AppCredential;
import com.apps.constant.redis.RedisConstant;
import com.apps.constant.wx.WxConstant;
import com.apps.exception.AppsGlobalException;
import com.apps.mapper.SystemUserPlatformLoginMapper;
import com.apps.service.logic.WechatService;
import com.apps.util.RedisUtil;
import com.apps.util.WxUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/1/9  15:05
 * @Version 1.0
 */
@Service
@AllArgsConstructor
@Slf4j
public class WechatServiceImpl implements WechatService {

    @Autowired
    private AppConfig appConfig;
    private final RedisUtil redisUtil;
    private final SystemUserPlatformLoginMapper platformLoginMapper;

    @Override
    @SneakyThrows
    public JSONObject getUserPhone(String appId, String code) {
        String accessToken = getAccessToken(appId);
        JSONObject userPhone = WxUtils.getUserPhone(accessToken, code);
        if (Objects.isNull(userPhone)) {
            log.error("获取用户手机号失败,appId:{},code:{}", appId, code);
            throw new AppsGlobalException(GlobExceptionEnum.MP_GET_USER_PHONE_ERROR);
        }
        if (!userPhone.containsKey("phone_info") || Objects.isNull(userPhone.get("phone_info"))) {
            log.error("获取用户手机号失败,返回结果:{}", userPhone);
            throw new AppsGlobalException(GlobExceptionEnum.MP_GET_USER_PHONE_ERROR);
        }
        return userPhone.getJSONObject("phone_info");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserOpenId(Long userId, String platformCode, String code) {
        log.info("保存用户openId,userId:{},platformCode:{},code:{}", userId, platformCode, code);
        if (StringUtils.isBlank(code)) {
            log.error("code为空,保存用户openId失败,userId:{},platformCode:{},code:{}", userId, platformCode, code);
        }
        SystemUserPlatformLoginEntity loginEntity = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getFkUserId, userId));
        if (Objects.isNull(loginEntity)) {
            log.error("未找到用户登录信息,userId:{},platformCode:{},code:{}", userId, platformCode, code);
            return;
        }
        if (StringUtils.isNotBlank(loginEntity.getMiniProgramOpenid())) {
            return;
        }
        //查询对应的app secret
        AppCredential credential = appConfig.getCredential(platformCode);
        if (Objects.isNull(credential)) {
            log.error("未找到对应的appSecret,平台code:{}", platformCode);
            throw new AppsGlobalException(GlobExceptionEnum.MP_SECRET_NOT_EXISTS);
        }

        Map<String, String> urlParamsMap = new HashMap<>();
        urlParamsMap.put("appid", credential.getAppId());
        urlParamsMap.put("secret", credential.getAppSecret());
        urlParamsMap.put("js_code", code);
        urlParamsMap.put("grant_type", "authorization_code");

        WxLoginVo wxLoginVo = WxUtils.doRequest(
                WxConstant.WX_LOGIN_URL,
                urlParamsMap,
                new TypeReference<WxLoginVo>() {
                }
        );
        log.info("微信登录返回结果:{}", JSONObject.toJSONString(wxLoginVo));
        if (Objects.isNull(wxLoginVo) || Objects.isNull(wxLoginVo.getOpenid())) {
            log.error("微信登录失败,返回结果:{}", wxLoginVo);
            throw new AppsGlobalException(GlobExceptionEnum.MP_GET_OPENID_ERROR);
        }

        loginEntity.setMiniProgramOpenid(wxLoginVo.getOpenid());
        if (StringUtils.isNotBlank(wxLoginVo.getUnionid())) {
            loginEntity.setMiniProgramUid(wxLoginVo.getUnionid());
        }
        loginEntity.setGmtModified(LocalDateTime.now());
        platformLoginMapper.updateById(loginEntity);
        log.info("保存用户openId成功,userId:{},platformCode:{},code:{},openId:{}", userId, platformCode, code, wxLoginVo.getOpenid());
    }

    @SneakyThrows
    private String getAccessToken(String appId) {
        if (redisUtil.hasKey(RedisConstant.WX_ACCESS_TOKEN + appId)) {
            Object o = redisUtil.get(RedisConstant.WX_ACCESS_TOKEN + appId);
            if (Objects.nonNull(o)) {
                return o.toString();
            }
        }
        AppCredential credential = appConfig.getCredential(appId);
        if (Objects.isNull(credential)) {
            log.error("未找到对应的appSecret,appId:{}", appId);
            throw new AppsGlobalException(GlobExceptionEnum.MP_SECRET_NOT_EXISTS);
        }
        String accessToken = WxUtils.getAccessToken(credential.getAppId(), credential.getAppSecret());
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取accessToken失败,appId:{}", appId);
            throw new AppsGlobalException(GlobExceptionEnum.MP_GET_ACCESS_TOKEN_ERROR);
        }
        redisUtil.set(RedisConstant.WX_ACCESS_TOKEN + appId, accessToken, RedisConstant.WX_ACCESS_TOKEN_EXPIRE_TIME);
        return accessToken;
    }

    @Override
    public String getOpenId(String code, String appId, String secret) {
        Map<String, String> urlParamsMap = new HashMap<>();
        urlParamsMap.put("appid", appId);
        urlParamsMap.put("secret", secret);
        urlParamsMap.put("js_code", code);
        urlParamsMap.put("grant_type", "authorization_code");

        WxLoginVo wxLoginVo = WxUtils.doRequest(
                WxConstant.WX_LOGIN_URL,
                urlParamsMap,
                new TypeReference<WxLoginVo>() {
                }
        );
        log.info("微信获取openId返回结果:{}", JSONObject.toJSONString(wxLoginVo));
        if (Objects.isNull(wxLoginVo) || Objects.isNull(wxLoginVo.getOpenid())) {
            log.error("微信获取openId失败,返回结果:{}", wxLoginVo);
            return Strings.EMPTY;
        }
        return wxLoginVo.getOpenid();
    }
}
