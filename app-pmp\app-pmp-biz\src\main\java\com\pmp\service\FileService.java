package com.pmp.service;

import com.pmp.dto.DownloadDto;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author:Oliver
 * @Date: 2025/9/1
 * @Version 1.0
 * @apiNote:
 */
public interface FileService {

    /**
     * 获取文件-直接下载
     *
     * @param bucket    存储桶
     * @param fileKey  文件key
     * @param response  响应
     * @param isPrivate 是否私有
     */
    void getFile(String bucket, String fileKey, HttpServletResponse response, Boolean isPrivate);

    /**
     * 获取文件-下载
     *
     * @param bucket    存储桶
     * @param fileKey  文件key
     * @param isPrivate 是否私有
     * @return 文件流
     */
    void downLoadObject(DownloadDto downloadDto, HttpServletResponse response, Boolean isPub);
}
