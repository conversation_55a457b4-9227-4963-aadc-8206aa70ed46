package com.coupon.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SendStatus {
    @JsonProperty("SerialNo")
    private String serialNo;
    @JsonProperty("PhoneNumber")
    private String phoneNumber;
    @JsonProperty("Fee")
    private int fee;
    @JsonProperty("SessionContext")
    private String sessionContext;
    @JsonProperty("Code")
    private String code;
    @JsonProperty("Message")
    private String message;
    @JsonProperty("IsoCode")
    private String isoCode;

}
