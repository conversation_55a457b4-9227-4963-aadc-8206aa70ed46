package com.partner.dto.student.paramsmapper;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "学生查询参数(和权限信息)")
public class MStudentParams extends UserInfoParams {

    @Schema(description = "公司ID")
    private Long companyId;
    @Schema(description = "国家ID")
    private Long countryId;
    @Schema(description = "当前申请状态步骤StepId")
    private Long studentOfferItemStepId;
    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "年度")
    private int year;
    @Schema(description = "月度")
    private int month;


    @Schema(description = "学生ID-用于查详情")
    private Long studentId;



    @Schema(description = "代理UUID")
    private String agentUUID;
    @Schema(description = "学生UUID-用于查详情")
    private String studentUUID;

    @Schema(description = "学生是否存在")
    private Boolean studentFlag=false;



}
