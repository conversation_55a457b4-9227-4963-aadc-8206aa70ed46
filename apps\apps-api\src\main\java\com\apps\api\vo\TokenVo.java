package com.apps.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/1/9  17:41
 * @Version 1.0
 */
@Data
public class TokenVo {

    @Schema(description = "用户主体")
    private String sub;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "令牌签发者")
    private String iss;

    @Schema(description = "令牌类型")
    @JsonProperty("token_type")
    private String tokenType;

    @Schema(description = "访问令牌")
    @JsonProperty("access_token")
    private String accessToken;

    @Schema(description = "刷新令牌")
    @JsonProperty("refresh_token")
    private String refreshToken;

    @Schema(description = "接收者列表")
    private List<String> aud;

    @Schema(description = "许可证信息")
    private String license;

    @Schema(description = "令牌生效时间")
    private String nbf;

    @Schema(description = "用户信息")
    @JsonProperty("user_info")
    private UserInfo userInfo;

    @Schema(description = "用户ID")
    @JsonProperty("user_id")
    private String userId;

    @Schema(description = "授权范围")
    private List<String> scope;

    @Schema(description = "令牌过期时间")
    private String exp;

    @Schema(description = "令牌有效期(秒)")
    @JsonProperty("expires_in")
    private String expiresIn;

    @Schema(description = "令牌签发时间")
    private String iat;

    @Schema(description = "JWT唯一标识")
    private String jti;

    @Schema(description = "用户名")
    private String username;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class UserInfo {

        @Schema(description = "用户名")
        @JsonProperty("username")
        private String username;

        @Schema(description = "权限列表")
        @JsonProperty("authorities")
        private List<Authority> authorities;

        @Schema(description = "账号是否未过期")
        @JsonProperty("accountNonExpired")
        private boolean accountNonExpired;

        @Schema(description = "账号是否未被锁定")
        @JsonProperty("accountNonLocked")
        private boolean accountNonLocked;

        @Schema(description = "凭据是否未过期")
        @JsonProperty("credentialsNonExpired")
        private boolean credentialsNonExpired;

        @Schema(description = "账号是否启用")
        @JsonProperty("enabled")
        private boolean enabled;

        @Schema(description = "附加的用户属性信息")
        @JsonProperty("attributes")
        private Map<String, Object> attributes;

        @Schema(description = "用户ID")
        @JsonProperty("id")
        private String id;

        @Schema(description = "部门ID")
        @JsonProperty("deptId")
        private String deptId;

        @Schema(description = "用户手机号")
        @JsonProperty("phone")
        private String phone;

        @Schema(description = "用户姓名")
        @JsonProperty("name")
        private String name;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Authority {

        @Schema(description = "权限标识")
        @JsonProperty("authority")
        private String authority;

    }

}
