package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 支付类型枚举类
 */

@Getter
@AllArgsConstructor
public enum RemindTypeEnum {

    EMAIL(1, "邮件"),
    SMS(2, "短信"),
    ;


    private Integer code;

    private String msg;


    public static RemindTypeEnum getEnumByCode(Integer code) {
        for (RemindTypeEnum value : RemindTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
