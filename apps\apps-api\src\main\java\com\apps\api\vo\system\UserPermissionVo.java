package com.apps.api.vo.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/1/15  09:43
 * @Version 1.0
 * 用户权限信息
 */
@Data
public class UserPermissionVo {

    @Schema(description = "用户基本信息")
    private SystemUserVo userVo;


    @Schema(description = "权限标识集合")
    private String[] permissions;


    @Schema(description = "角色标识集合")
    private Long[] roles;
}
