package com.coupon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CouponTypeDto {
    private Long couponTypeId;
    private String roleCode;
    private String uuid;
    private String title;
    private String subTitle;
    private String description;
    private String price;
    private String codeImageGuid;
    private boolean isActive;
    private Integer recommendedType;
    private Integer discountMethod;
    private LocalDateTime validPeriodStart;
    private LocalDateTime validPeriodEnd;

    private LocalDateTime redeemPeriodStart;
    private LocalDateTime redeemPeriodEnd;
    private boolean isLapse;
    private Long sort;
    private long size = 10;
    private long current = 1;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @Schema(description = "网页标题")
    private String ruleTitle;

    @Schema(description = "优惠卷封面图片UUID集合")
    private List<String> fileGuids;
}
