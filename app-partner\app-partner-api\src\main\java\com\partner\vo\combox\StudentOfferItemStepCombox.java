package com.partner.vo.combox;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class StudentOfferItemStepCombox {

    @Schema(description="学生offer步骤ID")
    private Long studentOfferItemStepId;
    @Schema(description="步骤排序")
    private String stepOrder;

    @Schema(description="步骤名称")
    private String stepName;

    @Schema(description="数量")
    private int applayCount;


    public String getStepName() {
        if(stepName!=null && stepName.length()>0 && stepName.indexOf("（")!=-1){
            stepName=stepName.substring(0,stepName.indexOf("（"));
        }
        return stepName;
    }


}
