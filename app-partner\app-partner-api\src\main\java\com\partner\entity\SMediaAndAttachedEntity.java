package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-06 14:44:03
 */

@Data
@TableName("s_media_and_attached")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" s_media_and_attached ")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SMediaAndAttachedEntity extends Model<SMediaAndAttachedEntity>{

  @Schema(description = "媒体附件Id")
  private Long id;
 

  @Schema(description = "文件guid(文档中心)")
  private String fkFileGuid;
 

  @Schema(description = "表名")
  private String fkTableName;
 

  @Schema(description = "表Id")
  private Long fkTableId;
 

  @Schema(description = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
  private String typeKey;
 

  @Schema(description = "索引值(默认从0开始，同一类型下值唯一)")
  private Integer indexKey;
 

  @Schema(description = "链接")
  private String link;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
