package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 结算账单
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("m_settlement_bill")
public class SettlementBill extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "结算代理Id")
    private Long fkAgentId;

    @Schema(description = "代理合同结算账户Id")
    private Long fkAgentContractAccountId;

    @Schema(description = "结算账户币种")
    private String fkCurrencyTypeNum;

    @Schema(description = "对账单总金额（根据账户币种）")
    private BigDecimal amount;
}
