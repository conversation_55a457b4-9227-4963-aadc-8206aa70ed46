package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

@Data
@Schema(description = "返回院校大数据")
public class MInstitutionRankingVo {

    @Schema(description = "年度")
    private Integer year;
    @Schema(description = "排名")
    private Integer rankingMin;

    @Schema(description = "院校名称")
    private String institutionName;


    @Schema(description = "院校名称（中文）")
    private String institutionNameChn;
    @Schema(description = "国家名称")
    private String areaCountryName;
    @Schema(description = "国家名称")
    private String areaCountryNameChn;
    @Schema(description = "学校首页图标")
    private String fileKey;
    @Schema(description = "学校环境")
    private String institutionPic;
    @Schema(description = "学校封面")
    private String fileKeyCover;

    public String getFileKey() {

        if(Strings.isEmpty(fileKey)) {
            fileKey=getInstitutionPic();
        }
        if(Strings.isEmpty(fileKey)) {
            fileKey=getFileKeyCover();
        }
        return fileKey;
    }
}
