package com.insurance.config;

import com.alibaba.fastjson.JSON;
import com.insurance.constant.RedisConstant;
import com.insurance.dto.order.AllianFormData;
import com.insurance.dto.order.NibFormData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedisService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // =============================common============================

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(String.valueOf(CollectionUtils.arrayToList(key)));
            }
        }
    }

    // ============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return
     */
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    // ================================Map=================================

    /**
     * HashGet
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public Object hget(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        redisTemplate.opsForHash().delete(key, item);
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        return redisTemplate.opsForHash().hasKey(key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return
     */
    public double hincr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, by);
    }

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     * @return
     */
    public double hdecr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, -by);
    }

    // ============================set=============================

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return
     */
    public Set<Object> sGet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public boolean sHasKey(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSet(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0)
                expire(key, time);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return
     */
    public long sGetSetSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    // ===============================list=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     * @return
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return
     */
    public long lGetListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     */
    public Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 添加订单号到 ZSET
     *
     * @param key   Redis Key（如 order:progressing）
     * @param value 订单号
     * @param score 分数（如时间戳）
     */
    public void zAdd(String key, String value, double score, long expireSeconds) {
        redisTemplate.opsForZSet().add(key, value, score);
        if (Objects.nonNull(expireSeconds)) {
            redisTemplate.expire(key, Duration.ofSeconds(expireSeconds));
        }
    }

    /**
     * 查询订单号是否存在于 ZSET
     *
     * @param key   Redis Key
     * @param value 订单号
     * @return 是否存在
     */
    public boolean zExists(String key, String value) {
        Double score = redisTemplate.opsForZSet().score(key, value);
        return score != null;
    }

    /**
     * 获取指定时间范围内的订单号（按 score 查询）
     *
     * @param key      Redis Key
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @return 区间内的订单号集合
     */
    public Set<Object> zRangeByScore(String key, double minScore, double maxScore) {
        return redisTemplate.opsForZSet().rangeByScore(key, minScore, maxScore);
    }

    /**
     * 删除指定时间范围内的订单号（清理过期）
     *
     * @param key      Redis Key
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @return 删除数量
     */
    public Long zRemoveRangeByScore(String key, double minScore, double maxScore) {
        return redisTemplate.opsForZSet().removeRangeByScore(key, minScore, maxScore);
    }

    /**
     * 移除指定订单号
     *
     * @param key   Redis Key
     * @param value 订单号
     * @return 删除数量
     */
    public Long zRemove(String key, String value) {
        return redisTemplate.opsForZSet().remove(key, value);
    }

    /**
     * 设置 ZSET 的过期时间（可选）
     *
     * @param key     Redis Key
     * @param timeout 超时时间
     * @param unit    时间单位
     */
    public void setExpire(String key, long timeout, TimeUnit unit) {
        redisTemplate.expire(key, timeout, unit);
    }

    public Double zScore(String key, String member) {
        return redisTemplate.opsForZSet().score(key, member);
    }

    /**
     * 获取指定 ZSet 中元素数量
     *
     * @param key Redis ZSet 的 key
     * @return 元素数量，若 key 不存在则返回 0
     */
    public Long zCard(String key) {
        try {
            return redisTemplate.opsForZSet().zCard(key);
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * 写入 Hash 类型数据，并设置过期时间
     *
     * @param key      Redis key
     * @param map      要写入的字段 map
     * @param timeout  过期时间
     * @param timeUnit 过期时间单位
     */
    public void putAllToHash(String key, Map<String, ?> map, long timeout, TimeUnit timeUnit) {
        redisTemplate.opsForHash().putAll(key, map);
        redisTemplate.expire(key, timeout, timeUnit);
    }


    /**
     * 写入对象数据，并设置过期时间
     *
     * @param key      Redis key
     * @param obj      要写入的对象
     * @param timeout  过期时间
     * @param timeUnit 过期时间单位
     */
    public void setObjectAsJson(String key, Object obj, long timeout, TimeUnit timeUnit) {
        String json = JSON.toJSONString(obj);
        redisTemplate.opsForValue().set(key, json, timeout, timeUnit);
    }

    /**
     * 从 Redis 中读取对象数据
     *
     * @param key   Redis key
     * @param clazz 要读取的对象类型
     * @return 返回读取的对象数据，若读取失败则返回 null
     */
    public <T> T getObjectFromJson(String key, Class<T> clazz) {
        Object value = redisTemplate.opsForValue().get(key);
        if (value == null) {
            return null;
        }
        String json;
        if (value instanceof String) {
            json = (String) value;
        } else {
            // 万一是通过其他方式放进去的对象（比如 Object 类型），尝试序列化成 JSON 再处理
            json = JSON.toJSONString(value);
        }

        return JSON.parseObject(json, clazz);
    }


    /**
     * 读取 Hash 中的某个字段
     */
    public Object getFromHash(String key, String hashKey) {
        return redisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 删除某个 Hash 键
     */
    public void deleteHashKey(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 根据 orderNo 获取对应的 Redis Hash 数据
     *
     * @param orderNo 订单号
     * @return 返回 Redis Hash 中的所有字段和数据
     */
    public Map<Object, Object> getDataByOrderNo(String orderNo) {
        String key = RedisConstant.ORDER_ENCRYPT_CARD + orderNo;
        return redisTemplate.opsForHash().entries(key);
    }

    /** -------------------- 分布式锁 -------------------- **/

    /**
     * 尝试加锁
     *
     * @param key           Redis Key
     * @param value         锁值
     * @param expireSeconds 过期时间（秒）
     * @return 是否加锁成功
     */
    public boolean tryLock(String key, String value, long expireSeconds) {
        try {
            Boolean success = redisTemplate.opsForValue().setIfAbsent(key, value, Duration.ofSeconds(expireSeconds));
            return Boolean.TRUE.equals(success);
        } catch (Exception e) {
            log.error("尝试加锁失败: key={}, error={}", key, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 释放锁
     *
     * @param key
     * @param expectedValue
     */
    public void releaseLock(String key, String expectedValue) {
        try {
            // 使用 Lua 脚本实现原子性释放锁
            String script =
                    "if redis.call('get', KEYS[1]) == ARGV[1] " +
                            "then return redis.call('del', KEYS[1]) else return 0 end";

            DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
            redisScript.setScriptText(script);
            redisScript.setResultType(Long.class);

            Long result = redisTemplate.execute(redisScript, Collections.singletonList(key), expectedValue);
            if (result != null && result == 1L) {
                log.info("释放锁成功: key={}", key);
            } else {
                log.warn("释放锁失败或锁已过期: key={}", key);
            }
        } catch (Exception e) {
            log.error("释放锁异常: key={}, error={}", key, e.getMessage(), e);
        }
    }


    /**
     * 缓存保单表单数据-nib(默认数据)
     *
     * @param key
     * @param data
     */
    public void cacheNibFormData(String key, NibFormData data) {
        redisTemplate.opsForValue().set(key, data);
    }

    /**
     * 获取缓存的保单表单数据-nib(默认数据)
     *
     * @param key
     * @return
     */
    public NibFormData getNibFormData(String key) {
        return (NibFormData) redisTemplate.opsForValue().get(key);
    }

    /**
     * 缓存保单表单数据-allian(默认数据)
     *
     * @param key
     * @param data
     */
    public void cacheAllianFormData(String key, AllianFormData data) {
        redisTemplate.opsForValue().set(key, data, Duration.ofHours(1));
    }

    /**
     * 获取缓存的保单表单数据-allian(默认数据)
     *
     * @param key
     * @return
     */
    public AllianFormData getAllianFormData(String key) {
        return (AllianFormData) redisTemplate.opsForValue().get(key);
    }


    /**
     * 设置缓存并在当天 12:59:59 过期
     *
     * @param key   缓存 key
     * @param value 缓存 value
     */
    public void setWithTodayExpire(String key, Object value) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = now.withHour(12).withMinute(59).withSecond(59);
            Duration duration = Duration.between(now, expireTime);

            long expireSeconds = duration.getSeconds() > 0 ? duration.getSeconds() : 300;

            redisTemplate.opsForValue().set(key, value, expireSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("设置缓存失败", e);
        }
    }

    /**
     * 获取缓存中的对象
     *
     * @param key 缓存 key
     * @param clazz 对象类型
     * @return 缓存值或 null
     */
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) return null;
            return clazz.cast(value);
        } catch (Exception e) {
            log.error("获取缓存失败", e);
            return null;
        }
    }


}

