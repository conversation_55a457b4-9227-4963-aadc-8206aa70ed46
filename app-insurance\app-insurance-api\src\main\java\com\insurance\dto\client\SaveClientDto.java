package com.insurance.dto.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SaveClientDto {

    @Schema(description = "客户ID-编辑传")
    private Long id;

    @Schema(description = "客户类型（枚举：1学生）")
    @NotNull(message = "客户类型不能为空")
    private Integer clientType;

    @Schema(description = "客户名称")
    private String clientName;

    @Schema(description = "前往国家Id")
    private Long fkAreaCountryId;

    @Schema(description = "前往州省Id")
    private Long fkAreaStateId;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    @Schema(description = "Email")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "手机区号")
    private String mobileAreaCode;

    @Schema(description = "移动电话")
    @Pattern(
            regexp = "^\\+?[0-9]{6,20}$",
            message = "手机号码格式不正确"
    )
    private String mobile;

    @Schema(description = "备注")
    private String remark;

}
