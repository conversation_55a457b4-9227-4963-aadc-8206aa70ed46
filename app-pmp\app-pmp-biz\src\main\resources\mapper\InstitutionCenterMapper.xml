<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pmp.mapper.InstitutionCenterMapper">

    <resultMap id="providerTypeNameMap" type="java.util.HashMap">
        <result property="providerId" column="providerId"/>
        <result property="name" column="name"/>
    </resultMap>


    <select id="queryCountryList" resultType="com.pmp.vo.institution.CountryVo">
        SELECT
        c.id AS countryId,
        c.name,
        c.name_chn AS nameChn,
        COUNT(DISTINCT i.id) AS institutionCount
        FROM u_area_country c
        LEFT JOIN m_institution i
        ON c.id = i.fk_area_country_id
        AND i.id IN
        <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
        WHERE c.id IN (
        SELECT DISTINCT mi.fk_area_country_id
        FROM m_institution mi
        WHERE mi.id IN
        <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
        )
        GROUP BY c.id, c.name, c.name_chn
    </select>
    <!--    <select id="queryCountryList" resultType="com.insurance.vo.institution.CountryVo">-->
    <!--        SELECT c.id                 AS countryId,-->
    <!--               c.name,-->
    <!--               c.name_chn           AS nameChn,-->
    <!--               COUNT(DISTINCT i.id) AS institutionCount-->
    <!--        FROM u_area_country c-->
    <!--                 LEFT JOIN m_institution i-->
    <!--                           ON c.id = i.fk_area_country_id-->
    <!--        where c.num not in ('N/A', 'GLB')-->
    <!--        GROUP BY c.id, c.name, c.name_chn-->
    <!--        order by c.view_order desc-->
    <!--    </select>-->


    <select id="institutionList" resultType="com.pmp.vo.institution.InstitutionVo">
        SELECT
        i.id AS institutionId,
        i.name,
        i.name_chn AS nameChn,
        i.name_display AS nameDisplay,
        i.website,
        r.ranking_min AS qsRank
        FROM m_institution i
        LEFT JOIN (
        SELECT r1.*
        FROM m_institution_ranking r1
        INNER JOIN (
        SELECT fk_institution_id, MAX(year) AS max_year
        FROM m_institution_ranking
        WHERE ranking_type = 0
        GROUP BY fk_institution_id
        ) r2 ON r1.fk_institution_id = r2.fk_institution_id
        AND r1.year = r2.max_year
        AND r1.ranking_type = 0
        ) r ON r.fk_institution_id = i.id
        <where>
            i.is_active = 1 and i.id &gt; 0
            <!--            学校id-集合-->
            <if test="institutionIds != null and institutionIds.size() > 0">
                and i.id in
                <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
                    #{institutionId}
                </foreach>
            </if>
            <!--            学校名称-->
            <if test="param.institutionName != null and param.institutionName != ''">
                and
                (i.name like concat('%', #{param.institutionName}, '%')
                    or i.name_chn like concat('%', #{param.institutionName}, '%')
                    or LOWER(i.short_name) like concat('%', LOWER(#{param.institutionName}), '%')
                    or LOWER(i.short_name_chn) like concat('%', LOWER(#{param.institutionName}), '%'))
            </if>
            <!--            国家-->
            <if test="param.countryId != null and param.countryId > 0  ">
                and i.fk_area_country_id =
                #{param.countryId}
            </if>
            <!--            州省-集合-->
            <if test="param.stateIdList != null and param.stateIdList.size() > 0  ">
                and i.fk_area_state_id in
                <foreach collection="param.stateIdList" item="stateId" open="(" separator="," close=")">
                    #{stateId}
                </foreach>
            </if>
            <!--            佣金标题-->
            <if test="param.commissionTitle != null and param.commissionTitle != ''">
                AND EXISTS (SELECT 1 FROM ais_pmp2_center.r_agent_commission_plan_institution cpi  WHERE cpi.fk_institution_id = i.id
                AND EXISTS (
                    SELECT 1
                    FROM ais_pmp2_center.m_agent_commission_plan cp
                    WHERE cp.id = cpi.fk_agent_commission_plan_id
                    AND cp.NAME LIKE concat('%',
                #{param.commissionTitle},
                '%'
                )
                )
                )
            </if>
            <!--            所属集团-集合-->
            <if test="param.institutionGroupIdList != null and param.institutionGroupIdList.size() > 0  ">
                and exists (select 1 from r_institution_provider_institution pi where pi.fk_institution_id = i.id
                and exists (
                select 1 from m_institution_provider p where p.id = pi.fk_institution_provider_id and
                p.fk_institution_group_id in
                <foreach collection="param.institutionGroupIdList" item="institutionGroupId" open="(" separator=","
                         close=")">
                    #{institutionGroupId}
                </foreach>)
                )
            </if>
            <!--            课程等级-集合-->
            <if test="param.majorLevelIdList != null and param.majorLevelIdList.size() > 0  ">
                and exists (select 1 from ais_pmp2_center.r_agent_commission_plan_institution cpi where
                cpi.fk_institution_id = i.id
                and exists (
                select 1 from ais_pmp2_center.m_agent_commission_plan cp where cp.id = cpi.fk_agent_commission_plan_id
                and exists (select 1 from ais_pmp2_center.m_agent_commission mac where mac.fk_agent_commission_plan_id
                = cp.id and exists (select 1 from ais_pmp2_center.r_agent_commission_major_level_custom mcl where
                mcl.fk_agent_commission_id = mac.id and mcl.fk_major_level_custom_id in
                <foreach collection="param.majorLevelIdList" item="majorLevelId" open="(" separator="," close=")">
                    #{majorLevelId}
                </foreach>)
                )
                ))
            </if>
            <!--            学校类型-集合-->
            <if test="param.institutionTypeIdList != null and param.institutionTypeIdList.size() > 0 ">
                and i.fk_institution_type_id in
                <foreach collection="param.institutionTypeIdList" item="institutionTypeId" open="(" separator=","
                         close=")">
                    #{institutionTypeId}
                </foreach>
            </if>
            <!-- keyword 可同时命中 institutionName 和 commissionTitle -->
            <if test="param.keyword != null and param.keyword != ''">
                AND
                (
                    (i.name like concat('%', #{param.keyword}, '%')
                        or i.name_chn like concat('%', #{param.keyword}, '%')
                        or LOWER(i.short_name) like concat('%', LOWER(#{param.keyword}), '%')
                        or LOWER(i.short_name_chn) like concat('%', LOWER(#{param.keyword}), '%'))
                        or EXISTS (
                        SELECT 1 FROM ais_pmp2_center.r_agent_commission_plan_institution cpi
                        WHERE cpi.fk_institution_id = i.id
                        AND EXISTS (
                        SELECT 1
                        FROM ais_pmp2_center.m_agent_commission_plan cp
                        WHERE cp.id = cpi.fk_agent_commission_plan_id
                        AND cp.NAME LIKE concat('%', #{param.keyword}, '%')
                        )))
            </if>
            <if test="(param.applyCountryIdList != null and param.applyCountryIdList.size() > 0)
                    or (param.regionIdList != null and param.regionIdList.size() > 0)">
                AND EXISTS (
                SELECT 1
                FROM ais_pmp2_center.r_agent_commission_plan_institution cpi
                WHERE cpi.fk_institution_id = i.id
                AND cpi.fk_agent_commission_plan_id IN (
                SELECT DISTINCT acp.id
                FROM ais_pmp2_center.m_agent_commission_plan acp
                JOIN ais_pmp2_center.m_institution_provider_commission_plan mcp
                ON acp.fk_institution_provider_commission_plan_id = mcp.id
                LEFT JOIN ais_pmp2_center.r_institution_provider_commission_plan_territory rct
                ON mcp.id = rct.fk_institution_provider_commission_plan_id
                WHERE
                (
                <!-- ====================== 国家匹配逻辑 ====================== -->
                <if test="param.applyCountryIdList != null and param.applyCountryIdList.size() > 0">
                    (
                    <!-- 规则3：is_include = 1 或 2，表示指定包含或 case-by-case 国家 -->
                    (rct.is_include IN (1, 2)
                    AND rct.fk_area_country_id IN
                    <foreach collection="param.applyCountryIdList" item="applyCountryId" open="(" separator=","
                             close=")">
                        #{applyCountryId}
                    </foreach>
                    )

                    <!-- 规则4：is_include = -1，表示排除；该国不在排除列表中才算匹配 -->
                    OR (
                    rct.is_include = -1
                    AND NOT EXISTS (
                    SELECT 1
                    FROM ais_pmp2_center.r_institution_provider_commission_plan_territory rct_sub
                    WHERE rct_sub.fk_institution_provider_commission_plan_id = mcp.id
                    AND rct_sub.fk_area_country_id IN
                    <foreach collection="param.applyCountryIdList" item="applyCountryId" open="(" separator=","
                             close=")">
                        #{applyCountryId}
                    </foreach>
                    )
                    )

                    <!-- 规则2：is_include = 3 或 4，表示全球适用 -->
                    OR (rct.is_include IN (3, 4))

                    <!-- 规则1：无记录（rct.id IS NULL），表示全球适用 -->
                    OR (rct.id IS NULL)
                    )
                </if>

                <!-- 如果同时传了国家和大区，则两套逻辑用 OR 连接 -->
                <if test="(param.applyCountryIdList != null and param.applyCountryIdList.size() > 0)
                          and (param.regionIdList != null and param.regionIdList.size() > 0)">
                    OR
                </if>

                <!-- ====================== 大区匹配逻辑 ====================== -->
                <if test="param.regionIdList != null and param.regionIdList.size() > 0">
                    (
                    <!-- 规则3：is_include = 1 或 2，表示指定包含或 case-by-case 大区 -->
                    (rct.is_include IN (1, 2)
                    AND rct.fk_area_region_id IN
                    <foreach collection="param.regionIdList" item="applyRegionId" open="(" separator="," close=")">
                        #{applyRegionId}
                    </foreach>
                    )

                    <!-- 规则4：is_include = -1，表示排除；该大区不在排除列表中才算匹配 -->
                    OR (
                    rct.is_include = -1
                    AND NOT EXISTS (
                    SELECT 1
                    FROM ais_pmp2_center.r_institution_provider_commission_plan_territory rct_sub
                    WHERE rct_sub.fk_institution_provider_commission_plan_id = mcp.id
                    AND rct_sub.fk_area_region_id IN
                    <foreach collection="param.regionIdList" item="applyRegionId" open="(" separator="," close=")">
                        #{applyRegionId}
                    </foreach>
                    )
                    )

                    <!-- 规则2：is_include = 3 或 4，表示全球适用 -->
                    OR (rct.is_include IN (3, 4))

                    <!-- 规则1：无记录（rct.id IS NULL），表示全球适用 -->
                    OR (rct.id IS NULL)
                    )
                </if>
                )
                )
                )
            </if>

            <!--            适用国家-地区-->
            <!--<if test="param.applyCountryIdList != null and param.applyCountryIdList.size() > 0">
                and exists (
                select 1 from ais_pmp2_center.r_agent_commission_plan_institution cpi where cpi.fk_institution_id = i.id
                and cpi.fk_agent_commission_plan_id in (
                SELECT DISTINCT acp.id FROM ais_pmp2_center.m_agent_commission_plan acp
                JOIN ais_pmp2_center.m_institution_provider_commission_plan mcp ON
                acp.fk_institution_provider_commission_plan_id = mcp.id
                LEFT JOIN ais_pmp2_center.r_institution_provider_commission_plan_territory rct ON mcp.id =
                rct.fk_institution_provider_commission_plan_id
                WHERE(
                rct.is_include IN ( 1, 2 ) AND rct.fk_area_country_id in
                <foreach collection="param.applyCountryIdList" item="applyCountryId" open="(" separator="," close=")">
                    #{applyCountryId}
                </foreach>
                OR(rct.is_include = -1 AND NOT EXISTS( SELECT 1 FROM
                ais_pmp2_center.r_institution_provider_commission_plan_territory rct_sub WHERE
                rct_sub.fk_institution_provider_commission_plan_id = mcp.id AND rct_sub.fk_area_country_id in
                <foreach collection="param.applyCountryIdList" item="applyCountryId" open="(" separator="," close=")">
                    #{applyCountryId}
                </foreach>))
                OR (rct.is_include IN ( 3, 4 ) )
                )
                OR( rct.id is null)
                )
                )
            </if>-->
        </where>
        <choose>
            <!-- sortType == 1 表示按传入的 institutionIds 顺序排序-高佣排序 -->
            <when test="sortType != null and sortType == 1 and institutionIds != null and institutionIds.size() > 0">
                ORDER BY FIELD(i.id,
                <foreach collection="institutionIds" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </when>
            <when test="param.qsSort != null and param.qsSort != ''">
                ORDER BY
                CASE WHEN qsRank IS NULL THEN 1 ELSE 0 END,
                qsRank ${param.qsSort}
            </when>
            <when test="param.institutionSort != null and param.institutionSort != ''">
                order by institutionId ${param.institutionSort}
            </when>
            <otherwise>
                ORDER BY
                CASE WHEN qsRank IS NULL THEN 1 ELSE 0 END,
                qsRank ASC,
                CONVERT(i.name USING gbk) ASC
            </otherwise>
            <!--            order by CONVERT(name USING gbk) ASC-->
        </choose>
    </select>


    <select id="queryInstitutionTypeList" resultType="com.pmp.vo.institution.InstitutionTypeVo">
        select t.id as typeId, t.type_name, t.type_name_chn
        from u_institution_type t
        order by t.view_order desc
    </select>


    <select id="queryGroupList" resultType="com.pmp.vo.institution.GroupVo">
        select g.id as groupId, g.name as groupName, g.name_chn as groupNameChn
        from m_institution_group g where g.id in
        (select p.fk_institution_group_id from m_institution_provider p
        where p.id in
        <foreach collection="providerIds" item="providerId" open="(" separator="," close=")">
            #{providerId}
        </foreach>
        )
    </select>

    <select id="institutionDetail" resultType="com.pmp.vo.institution.InstitutionVo">
        SELECT i.id                                   AS institutionId,
               i.name,
               i.name_chn                             AS nameChn,
               i.name_display                         AS nameDisplay,
               i.website,
               IFNULL(r1.ranking_min, r2.ranking_min) AS qsRank,
               c.name                                 as country,
               c.name_chn                             as countryChn,
               t.type_name                            as type,
               t.type_name_chn                        as typeChn,
               s.name                                 as areaState,
               s.name_chn                             as areaStateChn
        FROM m_institution i
                 LEFT JOIN m_institution_ranking r1
                           ON r1.fk_institution_id = i.id AND r1.ranking_type = 0 AND r1.year = #{year}
                 LEFT JOIN m_institution_ranking r2 ON r2.fk_institution_id = i.id AND r2.ranking_type = 0 AND r2.year =
                                                                                                               (SELECT MAX(year)
                                                                                                                FROM m_institution_ranking
                                                                                                                WHERE fk_institution_id = i.id)
                 left join u_area_country c on c.id = i.fk_area_country_id
                 left join u_institution_type t on t.id = i.fk_institution_type_id
                 left join u_area_state s on s.id = i.fk_area_state_id
        WHERE i.id = #{institutionId}
    </select>


    <select id="countryNameList" resultType="java.lang.String">
        SELECT GROUP_CONCAT(name SEPARATOR ', ') from u_area_country
        where id in
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
    </select>

    <select id="providerGroupMap" resultType="com.pmp.vo.institution.ProviderGroupVo">
        SELECT mip.id AS providerId,
        mig.name as name,
        mig.name_chn as nameChn
        FROM m_institution_provider mip
        inner JOIN m_institution_group mig
        ON mip.fk_institution_group_id = mig.id
        <where>
            mip.id in
            <foreach collection="providerIds" item="providerId" open="(" separator="," close=")">
                #{providerId}
            </foreach>
        </where>
    </select>

    <select id="queryCountryListByIds" resultType="com.pmp.vo.institution.CountryVo">
        SELECT
        c.id AS countryId,
        c.name,
        c.name_chn AS nameChn
        FROM u_area_country c
        where c.id in
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
    </select>

    <select id="queryAreaStateList" resultType="com.pmp.vo.institution.AreaStateVo">
        select s.id as areaStateId, s.name, s.name_chn
        from u_area_state s
        <where>
            <if test="countryIds != null and countryIds.size() > 0">
                s.fk_area_country_id in
                <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
                    #{countryId}
                </foreach>
            </if>
        </where>
        order by s.view_order desc
    </select>


    <select id="territoryList" resultType="com.pmp.vo.institution.CountryVo">
        SELECT c.id AS countryId,
        c.name,
        c.name_chn AS nameChn,
        COUNT(DISTINCT i.id) AS institutionCount
        FROM u_area_country c
        LEFT JOIN m_institution i
        ON c.id = i.fk_area_country_id
        where c.num not in ('N/A', 'GLB')
        <if test="countryIds != null and countryIds.size() > 0">
            and c.id in
            <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        GROUP BY c.id, c.name, c.name_chn
        order by c.view_order desc
    </select>

    <select id="selectHighCommissionInstitution" resultType="com.pmp.vo.institution.InstitutionVo">
        SELECT hc.fk_institution_id AS institutionId,
        hc.recommend_info AS highCommissionDescription,
        pl.file_key AS highCommissionPic
        FROM ais_platform_center.m_institution_high_commission hc
        LEFT JOIN ais_platform_center.s_media_and_attached att ON hc.id = att.fk_table_id
        AND att.fk_table_name = 'm_institution_high_commission'
        LEFT JOIN ais_file_center.m_file_platform pl ON att.fk_file_guid = pl.file_guid
        WHERE STATUS = 1
        AND hc.fk_platform_code = #{platformCode}
        AND (
        (is_timeless = 1 AND DATE(start_time) &lt;= DATE(#{date}))
        OR (is_timeless = 0 AND DATE(#{date}) BETWEEN DATE(start_time) AND DATE(end_time))
        )
        AND hc.fk_institution_id in
        <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
        GROUP BY hc.fk_institution_id
        order by hc.weight desc
    </select>


    <select id="queryRegionListByIds" resultType="com.pmp.vo.institution.RegionVo">
        select id as regionId,
        name,
        name_chn,
        num
        from ais_institution_center.u_area_region
        where fk_area_country_id = 34 and fk_company_id = -1
        and id in
        <foreach item="item" collection="regionIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        order by view_order desc
    </select>

    <select id="regionList" resultType="com.pmp.vo.institution.RegionVo">
        select id as regionId,
        name,
        name_chn,
        num
        from ais_institution_center.u_area_region
        where fk_area_country_id = 34
        and fk_company_id = -1
        <if test="regionIds != null and regionIds.size() > 0">
            and id in
            <foreach item="item" collection="regionIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        order by view_order desc
    </select>

    <select id="queryCountryIdsByRegionIds" resultType="java.lang.Long">
        SELECT id
        FROM ais_institution_center.u_area_country
        WHERE fk_area_region_ids IS NOT NULL
        <if test="regionIds != null and regionIds.size() > 0">
            AND (
            <foreach collection="regionIds" item="regionId" separator=" OR ">
                FIND_IN_SET(#{regionId}, fk_area_region_ids) > 0
            </foreach>
            )
        </if>
    </select>

    <select id="selectAreaRegionIdsByCountryIds" resultType="java.lang.String">
        select fk_area_region_ids
        from ais_institution_center.u_area_country
        where id in
        <foreach item="item" collection="countryIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

</mapper>