package com.partner.service;

import com.partner.dto.MFilePartnerDto;
import com.partner.entity.MFilePartnerEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

public interface FileService {
    /**
     * 多文件上传附件
     *
     * @param files
     * @param isPub
     * @return
     */
    List<MFilePartnerEntity> uploadAppendix(MultipartFile[] files, Boolean isPub,Boolean isPartnerPath);
    /**
     * 多文件上传附件
     * @param files
     * @param isPub
     * @return
     */
    List<MFilePartnerEntity> uploadAppendix(MultipartFile[] files, Boolean isPub,Boolean isPartnerPath,String fileName);



    /**
     * 单文件上传附件 -接口用
     * @return
     */
    MFilePartnerEntity uploadAppendix(MultipartFile file, Boolean isPub);

    /**
     * 单文件上传附件AIS
     * @return
     */
    MFilePartnerEntity uploadAisfileAppendix(MultipartFile file, Boolean isPub);


    /**
     * 单文件上传附件 临时文件上传
     * @return
     */
    MFilePartnerEntity uploadFileAppendix(File file, Boolean isPub);
}
