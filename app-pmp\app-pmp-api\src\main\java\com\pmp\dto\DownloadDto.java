package com.pmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/9/1
 * @Version 1.0
 * @apiNote:
 */
@Data
public class DownloadDto {

    @Schema(description = "文件名")
    @NotBlank(message = "文件名不能为空")
    private String fileNameOrc;

    @Schema(description = "文件Key")
    @NotBlank(message = "文件Key不能为空")
    private String fileKey;
}
