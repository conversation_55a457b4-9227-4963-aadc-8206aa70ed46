package com.insurance.util;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 保险日期计算工具类
 */
public class InsuranceDateUtil {

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    public static class InsuranceDateRange {

        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private final Date insuranceStartDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private final Date insuranceEndDate;

        public InsuranceDateRange(Date insuranceStartDate, Date insuranceEndDate) {
            this.insuranceStartDate = insuranceStartDate;
            this.insuranceEndDate = insuranceEndDate;
        }

        public Date getInsuranceStartDate() {
            return insuranceStartDate;
        }

        public Date getInsuranceEndDate() {
            return insuranceEndDate;
        }

        public static InsuranceDateRange calculateInsuranceRange(Date courseStartDate, Date courseEndDate) {
            if (courseStartDate == null || courseEndDate == null || courseStartDate.after(courseEndDate)) {
                throw new IllegalArgumentException("课程开始/结束日期无效");
            }

            // 1. 保险开始日期 = 课程开始 -14 天
            Calendar insuranceStart = Calendar.getInstance();
            insuranceStart.setTime(courseStartDate);
            insuranceStart.add(Calendar.DAY_OF_MONTH, -14);

            // 如果保险开始日期 < 今天，则取今天
            Date today = new Date();
            if (insuranceStart.getTime().before(today)) {
                insuranceStart.setTime(today);
            }

            // 2. 课程月数差
            Calendar start = Calendar.getInstance();
            start.setTime(courseStartDate);

            Calendar end = Calendar.getInstance();
            end.setTime(courseEndDate);

            int monthsBetween = (end.get(Calendar.YEAR) - start.get(Calendar.YEAR)) * 12 +
                    (end.get(Calendar.MONTH) - start.get(Calendar.MONTH));

            Date insuranceEndDate;
            if (monthsBetween < 10) {
                // 情况 1：课程小于10个月 → 课程结束 +1个月
                end.add(Calendar.MONTH, 1);
                insuranceEndDate = end.getTime();
            } else {
                int endMonth = end.get(Calendar.MONTH) + 1; // Calendar.MONTH 是从0开始的

                if (endMonth == 11 || endMonth == 12) {
                    // 情况 2：课程结束是11或12月 → 次年3月15日
                    Calendar march15 = Calendar.getInstance();
                    march15.setTime(courseEndDate);
                    march15.add(Calendar.YEAR, 1);
                    march15.set(Calendar.MONTH, Calendar.MARCH);
                    march15.set(Calendar.DAY_OF_MONTH, 15);
                    insuranceEndDate = march15.getTime();
                } else {
                    // 情况 3：课程结束是1-10月 → 课程结束 +2个月
                    end.add(Calendar.MONTH, 2);
                    insuranceEndDate = end.getTime();
                }
            }

            return new InsuranceDateRange(insuranceStart.getTime(), insuranceEndDate);
        }


        // 可选：支持传入字符串
        public static InsuranceDateRange calculateInsuranceRange(String startStr, String endStr) throws ParseException {
            Date start = sdf.parse(startStr);
            Date end = sdf.parse(endStr);
            return calculateInsuranceRange(start, end);
        }
    }
}
