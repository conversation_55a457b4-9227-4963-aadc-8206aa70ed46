package com.partner.controller;

import com.common.core.util.R;
import com.partner.dto.role.SavePartnerRoleDto;
import com.partner.dto.role.SaveUserDto;
import com.partner.entity.PartnerRole;
import com.partner.service.PartnerRoleService;
import com.partner.vo.TeamMemberTreeVo;
import com.partner.vo.TeamMemberVo;
import com.partner.vo.role.MenuTreeVo;
import com.partner.vo.role.PartnerUserDetailVo;
import com.partner.vo.role.TeamUserVo;
import com.partner.vo.role.UserRolePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
@RestController
@Tag(description = "partnerRole", name = "伙伴角色与用户管理")
@RequestMapping("/partnerRole")
public class PartnerRoleController {

    @Autowired
    private PartnerRoleService partnerRoleService;

    @Operation(summary = "菜单树", description = "菜单树")
    @GetMapping("/menuTree")
    public R<List<MenuTreeVo>> menuTree() {
        return R.ok(partnerRoleService.menuTree());
    }

    @Operation(summary = "角色列表", description = "角色列表")
    @GetMapping("/roleList")
    public R<List<PartnerRole>> roleList() {
        return R.ok(partnerRoleService.roleList());
    }

    @Operation(summary = "角色详情", description = "角色详情")
    @GetMapping("/roleDetail/{id}")
    public R<PartnerRole> roleDetail(@PathVariable("id") Long id) {
        return R.ok(partnerRoleService.roleDetail(id));
    }

    @Operation(summary = "获取用户权限", description = "获取当前用户权限")
    @GetMapping("/getUserRolePermission")
    public R<UserRolePermission> getUserRolePermission() {
        return R.ok(partnerRoleService.getUserRolePermission());
    }

    @Operation(summary = "获取伙伴用户详情", description = "获取伙伴用户详情")
    @GetMapping("/getUserDetail")
    public R<PartnerUserDetailVo> getUserDetail(Long partnerUserId) {
        return R.ok(partnerRoleService.getUserDetail(partnerUserId));
    }

    @Operation(summary = "新增/编辑角色", description = "新增编辑角色")
    @PostMapping("/saveRole")
    public R saveRole(@RequestBody @Valid SavePartnerRoleDto roleDto) {
        partnerRoleService.saveRole(roleDto);
        return R.ok("保存成功");
    }

    @Operation(summary = "新增/编辑团队成员", description = "我的团队-新增/编辑团队成员")
    @PostMapping("/savePartnerUser")
    public R savePartnerUser(@RequestBody @Valid SaveUserDto saveUserDto) {
        partnerRoleService.savePartnerUser(saveUserDto);
        return R.ok("保存成功");
    }

    @Operation(summary = "团队成员列表", description = "团队成员列表")
    @GetMapping("/teamMemberList")
    public R<List<TeamMemberVo>> teamMemberList(Integer year) {
        return R.ok(partnerRoleService.teamMemberList(year));
    }

    @Operation(summary = "删除角色", description = "删除角色")
    @DeleteMapping("/delRole/{id}")
    public R<String> delRole(@PathVariable("id") Long id) {
        partnerRoleService.delRole(id);
        return R.ok("保存成功");
    }

    @Operation(summary = "上司列表", description = "上司列表")
    @GetMapping("/getSuperiorList")
    public R<List<TeamMemberVo>> getSuperiorList() {
        return R.ok(partnerRoleService.getSuperiorList());
    }

    @Operation(summary = "团队架构树", description = "团队架构树")
    @GetMapping("/teamMemberTree")
    public R<List<TeamUserVo>> teamMemberTree(Integer year) {
        return R.ok(partnerRoleService.teamMemberTree(year));
    }
}
