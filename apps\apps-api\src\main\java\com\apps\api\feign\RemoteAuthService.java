/*
 *    Copyright (c) 2018-2025, <PERSON><PERSON> All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the fzh developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: <PERSON><PERSON> (<EMAIL>)
 */

package com.apps.api.feign;

import com.apps.api.vo.TokenVo;
import com.common.core.constant.ServiceNameConstants;
import com.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;


/**
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteAuthService", value = ServiceNameConstants.AUTH_SERVICE)
public interface RemoteAuthService {

    /**
     * 登录
     * @param params
     * @return
     */
    @NoToken
    @PostMapping(value = "/oauth2/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    TokenVo accountLogin(@RequestParam Map<String, String> params,
                         @RequestHeader Map<String, Object> headers);

}
