package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-18 11:40:29
 */

@Data
@TableName("m_student_offer_item")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_student_offer_item ")
public class MStudentOfferItemEntity extends Model<MStudentOfferItemEntity>{

  @Schema(description = "学生申请方案项目Id")
  private Long id;
 

  @Schema(description = "学生申请方案项目父Id")
  private Long fkParentStudentOfferItemId;
 

  @Schema(description = "学生Id")
  private Long fkStudentId;
 

  @Schema(description = "代理Id（业绩绑定）")
  private Long fkAgentId;
 

  @Schema(description = "员工Id（业绩绑定，BD）")
  private Long fkStaffId;
 

  @Schema(description = "学生申请方案Id")
  private Long fkStudentOfferId;
 

  @Schema(description = "国家Id")
  private Long fkAreaCountryId;
 

  @Schema(description = "学校Id")
  private Long fkInstitutionId;
 

  @Schema(description = "学校学院Id")
  private Long fkInstitutionFacultyId;
 

  @Schema(description = "学校校区Id")
  private Long fkInstitutionZoneId;
 

  @Schema(description = "课程Id")
  private Long fkInstitutionCourseId;
 

  @Schema(description = "自定义课程Id（存储系统匹配的课程id）")
  private Long fkInstitutionCourseCustomId;
 

  @Schema(description = "学校提供商Id")
  private Long fkInstitutionProviderId;
 

  @Schema(description = "渠道来源Id")
  private Long fkInstitutionChannelId;
 

  @Schema(description = "学生申请方案项目状态步骤Id")
  private Long fkStudentOfferItemStepId;
 

  @Schema(description = "学生申请方案项目状态步骤时间")
  private LocalDateTime studentOfferItemStepTime;
 

  @Schema(description = "rpa order id（一键申请对应的Order id）")
  private Long fkIssueRpaOrderId;
 

  @Schema(description = "课程等级对应的ids，多个用逗号分隔")
  private String fkInstitutionCourseMajorLevelIds;
 

  @Schema(description = "课程类型对应的ids，多个用逗号分隔")
  private String fkInstitutionCourseTypeIds;
 

  @Schema(description = "课程类型组别对应的ids，多个用逗号分隔")
  private String fkInstitutionCourseTypeGroupIds;
 

  @Schema(description = "申请方案项目编号")
  private String num;
 

  @Schema(description = "学生ID（Offer ID）")
  private String studentId;
 

  @Schema(description = "课程长度类型(0周、1学期、2年)")
  private Integer durationType;
 

  @Schema(description = "课程长度")
  private BigDecimal duration;
 

  @Schema(description = "开学时间")
  private LocalDate openingTime;
 

  @Schema(description = "延迟入学时间（最终开学时间）")
  private LocalDate deferOpeningTime;
 

  @Schema(description = "结束时间")
  private LocalDate closingTime;
 

  @Schema(description = "币种编号")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "学费金额")
  private BigDecimal tuitionAmount;
 

  @Schema(description = "学费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
  private Integer tuitionStatus;
 

  @Schema(description = "申请费币种")
  private String fkAppFeeCurrencyTypeNum;
 

  @Schema(description = "申请费金额")
  private BigDecimal appFeeAmount;
 

  @Schema(description = "申请费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
  private Integer appFeeStatus;
 

  @Schema(description = "课程官网URL")
  private String courseWebsite;
 

  @Schema(description = "课程开放时间")
  private LocalDate courseOpenTime;
 

  @Schema(description = "是否主课程：0否/1是（一套方案，只有一个主课程）")
  private Boolean isMain;
 

  @Schema(description = "是否后续课程：0否/1是")
  private Boolean isFollow;
 

  @Schema(description = "是否财务专用：0否/1是")
  private Boolean isFollowHidden;
 

  @Schema(description = "是否减免学分：0否/1是")
  private Boolean isCreditExemption;
 

  @Schema(description = "是否加申，0否/1是")
  private Boolean isAddApp;
 

  @Schema(description = "是否步骤更随主课，0否/1是")
  private Boolean isStepFollow;
 

  @Schema(description = "是否无佣金：0否/1是")
  private Boolean isNoCommission;
 

  @Schema(description = "新申请状态：枚举：0缺资料/1未开放")
  private Integer newAppStatus;
 

  @Schema(description = "新申请状态操作时间")
  private LocalDateTime newAppOptTime;
 

  @Schema(description = "申请方式：0网申/1扫描/2原件邮递/3其他")
  private Integer appMethod;
 

  @Schema(description = "申请备注（网申信息）")
  private String appRemark;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
  private String conditionType;
 

  @Schema(description = "入学失败原因Id")
  private Long fkEnrolFailureReasonId;
 

  @Schema(description = "其他入学失败原因")
  private String otherFailureReason;
 

  @Schema(description = "是否延迟入学标记：0否/1是")
  private Boolean isDeferEntrance;
 

  @Schema(description = "学习模式：枚举定义：0未定/1面授/2网课")
  private Integer learningMode;
 

  @Schema(description = "提交申请时间")
  private LocalDate submitAppTime;
 

  @Schema(description = "支付押金时间")
  private LocalDate depositTime;
 

  @Schema(description = "支付押金截止时间")
  private LocalDate depositDeadline;
 

  @Schema(description = "接受Offer截止时间")
  private LocalDate acceptOfferDeadline;
 

  @Schema(description = "支付学费时间")
  private LocalDate tuitionTime;
 

  @Schema(description = "保险购买方式枚举：0买学校保险/1通过Hti买保险/2通过其他机构买保险")
  private Integer insuranceBuyMethod;
 

  @Schema(description = "押金支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
  private Integer depositPaymentMethod;
 

  @Schema(description = "学费支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
  private Integer tuitionPaymentMethod;
 

  @Schema(description = "状态：0关闭/1打开")
  private Integer status;
 

  @Schema(description = "一键操作时间")
  private LocalDateTime rpaOptTime;
 

  @Schema(description = "一键完成时间")
  private LocalDateTime rpaFinishTime;
 

  @Schema(description = "一键备注")
  private String rpaRemark;
 

  @Schema(description = "一键处理状态：枚举")
  private String statusRpa;
 

  @Schema(description = "来自平台类型：get_mso")
  private String fkPlatformType;
 

  @Schema(description = "旧系统学校名称")
  private String oldInstitutionName;
 

  @Schema(description = "旧系统学校全称")
  private String oldInstitutionFullName;
 

  @Schema(description = "旧系统课程名称")
  private String oldCourseCustomName;
 

  @Schema(description = "旧系统课程专业等级名称")
  private String oldCourseMajorLevelName;
 

  @Schema(description = "旧系统课程类型名称")
  private String oldCourseTypeName;
 

  @Schema(description = "ISSUE课程输入标记：input/select")
  private String issueCourseInputFlag;
 

  @Schema(description = "旧数据财务id(gea)")
  private String idGeaFinance;
 

  @Schema(description = "旧数据id(issue)")
  private String idIssue;
 

  @Schema(description = "旧数据id(gea)")
  private String idGea;
 

  @Schema(description = "旧数据id(iae)")
  private String idIae;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
