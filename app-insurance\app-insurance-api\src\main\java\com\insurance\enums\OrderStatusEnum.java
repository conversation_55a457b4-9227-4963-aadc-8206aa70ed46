package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 订单状态枚举类
 */

@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    PENDING(0, "未发送(待请求)" ),
    PROGRESSING(1, "请求中" ),
    SUCCESS(2, "下单成功" ),
    FAIL(-2, "下单失败" ),
//    REFUND(4, "已退款" ),
//    SETTLED(1, "已结算" ),
    ;


    private Integer code;

    private String msg;


    public static OrderStatusEnum getEnumByCode(Integer code) {
        for (OrderStatusEnum value : OrderStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
