package com.partner.vo.student;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Schema(description = "Partner学生信息导出")
public class MStudentExportVo {

    @Schema(description = "最终申请状态")
    private String endStepName;
    @Schema(description = "最初申请状态")
    private String startStepName;

    @Schema(description = "其他入学失败原因")
    private String otherFailureReason;
    @Schema(description = "姓名(中)")
    private String name;


    @Schema(description = "姓（英/拼音）")
    private String lastName;
    @Schema(description = "名（英/拼音）")
    private String firstName;

    @Schema(description = "国家/地区 名称")
    private String countryName;
    @Schema(description = "国家/地区 中文名称")
    private String countryNameChn;

    @Schema(description = "学校 中文名")
    private String institutionNameChn;
    @Schema(description = "学校 名")
    private String institutionName;
    @Schema(description = "课程 名")
    private String courseName;


    @Schema(description = "成功入读国家")
    private String countryNameSuccess;
    @Schema(description = "国家/地区 中文名称")
    private String countryNameChnSuccess;

    @Schema(description = "学校 中文名")
    private String institutionNameChnSuccess;
    @Schema(description = "学校 名")
    private String institutionNameSuccess;
    @Schema(description = "课程 名")
    private String courseNameSuccess;





    @Schema(description = "开学时间")
    private LocalDate openingTime;
    @Schema(description = "性别")
    private String gender;


    @Schema(description = "学生现居所在国家名称")
    private String fkAreaCountryName;
    @Schema(description = "学生现居所在州省名称")
    private String fkAreaStateName;
    @Schema(description = "学生现居所在城市名称")
    private String fkAreaCityName;


    @Schema(description = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private LocalDate birthday;


    @Schema(description = "移动电话")
    private String mobile;
    @Schema(description = "Email")
    private String email;

    @Schema(description = "学生绑定代理")
    private String agentName;

    @Schema(description = "学生申请资料收取时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private LocalDate receivedApplicationDataDate;




}
