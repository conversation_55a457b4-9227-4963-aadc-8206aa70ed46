package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RAgentContractSignatureEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【r_agent_contract_signature】的数据库操作Mapper
* @createDate 2025-06-28 15:10:38
* @Entity com.partner.entity.RAgentContractSignature
*/
@Mapper
@DS("saledb")
public interface RAgentContractSignatureMapper extends BaseMapper<RAgentContractSignatureEntity> {


}




