package com.partner.controller;


import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.offeritem.MAppStudentOfferItemDeleteDto;
import com.partner.dto.offeritem.MAppStudentOfferItemUpdateDto;
import com.partner.dto.student.MAppStudentAddApplyDto;
import com.partner.service.MStudentOfferItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@Tag(description = "mStudentOfferItem" , name = "小程序-申请计划管理" )
@RestController
@RequestMapping("/mStudentOfferItem")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MStudentOfferItemController {

    private final MStudentOfferItemService mStudentOfferItemService;

    @Operation(summary = "学生详情-加划" , description = "学生详情-加划" )
    @SysLog("学生详情-加划" )
    @PostMapping("/addOfferItemStudents" )
    public R addOfferItemStudents(@RequestBody @Valid MAppStudentAddApplyDto dto){

        return R.ok(mStudentOfferItemService.addOfferItemStudents(dto));
    }

    @Operation(summary = "学生详情-修划" , description = "学生详情-修划" )
    @SysLog("学生详情-修划" )
    @PostMapping("/updateOfferItemStudents" )
    public R updateOfferItemStudents(@RequestBody @Valid MAppStudentOfferItemUpdateDto params){
        return R.ok(mStudentOfferItemService.updateOfferItemStudents(params));
    }


    @Operation(summary = "通过id删除划" , description = "通过id删" )
    @SysLog("通过id删除草稿" )
    @PostMapping("/removeById" )
    public R removeById(@RequestBody @Valid MAppStudentOfferItemDeleteDto params) {
        return R.ok(mStudentOfferItemService.removeById(params));
    }





}
