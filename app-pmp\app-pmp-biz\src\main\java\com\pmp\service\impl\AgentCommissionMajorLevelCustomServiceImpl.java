package com.pmp.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pmp.entity.AgentCommissionMajorLevelCustom;
import com.pmp.entity.MajorLevelCustom;
import com.pmp.mapper.AgentCommissionMajorLevelCustomMapper;
import com.pmp.mapper.MajorLevelCustomMapper;
import com.pmp.service.AgentCommissionMajorLevelCustomService;
import com.pmp.vo.commission.CommissionMajorLevelVo;
import com.pmp.vo.commission.MajorLevelTreeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/3/5  17:38
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionMajorLevelCustomServiceImpl extends ServiceImpl<AgentCommissionMajorLevelCustomMapper, AgentCommissionMajorLevelCustom> implements AgentCommissionMajorLevelCustomService {

    @Autowired
    private AgentCommissionMajorLevelCustomMapper agentCommissionMajorLevelCustomMapper;
    @Autowired
    private MajorLevelCustomMapper customMapper;

    @Override
    public List<CommissionMajorLevelVo> getCommissionMajorLevelList(List<Long> commissionIds) {
        List<CommissionMajorLevelVo> list = new ArrayList<>();
        commissionIds.stream().forEach(commissionId -> {
            List<AgentCommissionMajorLevelCustom> levelCustoms = agentCommissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                    .eq(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commissionId));
            if (CollectionUtils.isEmpty(levelCustoms)) {
                return;
            }
            List<MajorLevelCustom> majorLevelCustoms = customMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                    .in(MajorLevelCustom::getId, levelCustoms.stream()
                            .map(AgentCommissionMajorLevelCustom::getFkMajorLevelCustomId).collect(Collectors.toList())));
            if (CollectionUtils.isNotEmpty(majorLevelCustoms)) {
                List<CommissionMajorLevelVo> levelVoList = majorLevelCustoms.stream().map(majorLevelCustom -> {
                    CommissionMajorLevelVo vo = new CommissionMajorLevelVo();
                    vo.setCustomName(majorLevelCustom.getCustomName());
                    vo.setCustomNameChn(majorLevelCustom.getCustomNameChn());
                    vo.setLevelId(majorLevelCustom.getId());
                    vo.setCommissionId(commissionId);
                    return vo;
                }).collect(Collectors.toList());
                list.addAll(levelVoList);
            }
        });
        return list;
    }

    @Override
    public List<MajorLevelTreeVo> getMajorLevelTree() {
        //先找父级
        List<MajorLevelCustom> majorLevelCustoms = customMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                .eq(MajorLevelCustom::getIsActive, 1)
                .eq(MajorLevelCustom::getFkMajorLevelCustomIdParent, 0)
                .orderByDesc(MajorLevelCustom::getViewOrder)
                .orderByDesc(MajorLevelCustom::getGmtCreate));
        return majorLevelCustoms.stream().map(majorLevelCustom -> {
            MajorLevelTreeVo treeVo = new MajorLevelTreeVo();
            treeVo.setLevelId(majorLevelCustom.getId());
            treeVo.setLevelName(majorLevelCustom.getCustomName());
            treeVo.setLevelNameChn(majorLevelCustom.getCustomNameChn());
            treeVo.setIsGeneral(majorLevelCustom.getIsGeneral() ? 1 : 0);
            treeVo.setViewOrder(majorLevelCustom.getViewOrder());
            treeVo.setChildren(getChildren(majorLevelCustom.getId()));
            return treeVo;
        }).collect(Collectors.toList());
    }

    private List<MajorLevelTreeVo> getChildren(Long parentId) {
        List<MajorLevelCustom> majorLevelCustoms = customMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                .eq(MajorLevelCustom::getIsActive, 1)
                .eq(MajorLevelCustom::getFkMajorLevelCustomIdParent, parentId)
                .orderByDesc(MajorLevelCustom::getViewOrder)
                .orderByDesc(MajorLevelCustom::getGmtCreate));
        if (CollectionUtils.isEmpty(majorLevelCustoms)) {
            return new ArrayList<>();
        }
        return majorLevelCustoms.stream().map(majorLevelCustom -> {
            MajorLevelTreeVo treeVo = new MajorLevelTreeVo();
            treeVo.setLevelId(majorLevelCustom.getId());
            treeVo.setLevelName(majorLevelCustom.getCustomName());
            treeVo.setLevelNameChn(majorLevelCustom.getCustomNameChn());
            treeVo.setIsGeneral(majorLevelCustom.getIsGeneral() ? 1 : 0);
            treeVo.setViewOrder(majorLevelCustom.getViewOrder());
            treeVo.setChildren(new ArrayList<>());
            return treeVo;
        }).collect(Collectors.toList());
    }
}
