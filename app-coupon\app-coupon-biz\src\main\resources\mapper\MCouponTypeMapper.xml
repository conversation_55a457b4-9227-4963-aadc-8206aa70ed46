<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coupon.mapper.MCouponTypeMapper">
    <select id="getCouponTypePage" resultType="com.coupon.vo.GetAllCouponTypeVo">
        SELECT
        couponType.id AS couponTypeId,
        couponType.uuid AS uuid,
        couponType.title AS title,
        couponType.sub_title AS subTitle,
        couponType.description AS description,
        couponType.price AS price,
        couponType.is_active AS isActive,
        couponType.recommended_type AS recommendedType,
        couponTYpe.discount_method AS discountMethod,
        couponType.valid_period_start AS validPeriodStart,
        couponType.valid_period_end AS validPeriodEnd,
        couponType.redeem_period_start AS redeemPeriodStart,
        couponType.redeem_period_end AS redeemPeriodEnd,
        couponType.view_order AS sort,
        couponType.code_image AS codeImageGuid,
        couponType.view_order AS viewOrder,
        couponType.rule_title AS ruleTitle,
        mediaAttached.fk_file_guid AS imageGuid
        FROM m_coupon_type AS couponType
        LEFT JOIN (
        SELECT *
        FROM s_media_and_attached
        WHERE fk_table_name = 'm_coupon_type'
        GROUP BY fk_table_id
        ) AS mediaAttached ON couponType.id = mediaAttached.fk_table_id
        <where>
            <if test="couponTypeDto.couponTypeId != null and couponTypeDto.couponTypeId != '' ">
                AND couponType.id = #{couponTypeDto.couponTypeId}
            </if>
            <if test="couponTypeDto.uuid != null and couponTypeDto.uuid != '' ">
                AND couponType.uuid = #{couponTypeDto.uuid}
            </if>
            <if test="couponTypeDto.title != null and couponTypeDto.title != '' ">
                AND couponType.title LIKE concat ('%',#{couponTypeDto.title},'%')
            </if>

            <if test="couponTypeDto.subTitle != null and couponTypeDto.subTitle != '' ">
                AND couponType.subTitle LIKE concat ('%',#{couponTypeDto.subTitle},'%')
            </if>

            <if test="couponTypeDto.description != null and couponTypeDto.description != '' ">
                AND couponType.description LIKE concat ('%',#{couponTypeDto.description},'%')
            </if>

            <if test="couponTypeDto.price != null and couponTypeDto.price != '' ">
                AND couponType.price = #{couponTypeDto.price}
            </if>

            <if test="couponTypeDto.isActive != null and couponTypeDto.isActive != '' ">
                AND couponType.is_active = #{couponTypeDto.isActive}
            </if>

            <if test="couponTypeDto.recommendedType != null">
                AND couponType.recommended_type = #{couponTypeDto.recommendedType}
            </if>

            <if test="couponTypeDto.validPeriodStart != null and couponTypeDto.validPeriodStart != '' ">
                AND couponType.valid_period_start &gt; #{couponTypeDto.validPeriodStart}
            </if>

            <if test="couponTypeDto.validPeriodEnd != null and couponTypeDto.validPeriodEnd != '' ">
                AND couponType.valid_period_end &lt; #{couponTypeDto.validPeriodEnd}
            </if>

            <if test="couponTypeDto.redeemPeriodStart != null and couponTypeDto.redeemPeriodStart != '' ">
                AND couponType.redeem_period_start &gt; #{couponTypeDto.redeemPeriodStart}
            </if>
            <if test="couponTypeDto.redeemPeriodEnd != null and couponTypeDto.redeemPeriodEnd != '' ">
                AND couponType.redeem_period_end &lt; #{couponTypeDto.redeemPeriodEnd}
            </if>

            <if test="couponTypeDto.discountMethod != null and couponTypeDto.discountMethod != '' ">
                AND couponType.discount_method &lt; #{couponTypeDto.discountMethod}
            </if>

            <if test="couponTypeDto.sort != null and couponTypeDto.sort != '' ">
                AND couponType.view_order = #{couponTypeDto.sort}
            </if>
        </where>
        ORDER BY couponType.view_order desc,couponType.gmt_create desc
    </select>
</mapper>