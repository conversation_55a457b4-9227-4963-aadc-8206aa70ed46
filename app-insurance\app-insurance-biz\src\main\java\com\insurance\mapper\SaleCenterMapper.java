package com.insurance.mapper;

import com.insurance.vo.settlement.AgentAccountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface SaleCenterMapper {

    /**
     * 获取代理商账户列表
     * @param agentId
     * @return
     */
    List<AgentAccountVo> getAgentAccountList(@Param("agentId") Long agentId);


    /**
     * 获取代理商账户详情
     * @param id
     * @return
     */
    AgentAccountVo getAgentAccount(@Param("id") Long id);
}
