package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-06-28 15:09:38
 */

@Data
@TableName("r_agent_contract_signature")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_agent_contract_signature ")
public class RAgentContractSignatureEntity extends Model<RAgentContractSignatureEntity>{

  @Schema(description = "代理合同签名关系Id")
  private Long id;
 

  @Schema(description = "学生代理合同Id")
  private Long fkAgentContractId;
 

  @Schema(description = "签名文件")
  private String signature;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
