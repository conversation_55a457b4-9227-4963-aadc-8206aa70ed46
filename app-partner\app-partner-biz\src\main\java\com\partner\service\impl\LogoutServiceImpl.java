package com.partner.service.impl;

import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.service.LogoutService;
import com.partner.util.UserInfoParamsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LogoutServiceImpl implements LogoutService {


    @Override
    public void deleteCache() {
        FzhUser fzhUser= SecurityUtils.getUser();
        Boolean userFlag= UserInfoParamsUtils.deleteUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        if(userFlag){
            log.error("注销清空当前用户缓存");
        }
    }
}
