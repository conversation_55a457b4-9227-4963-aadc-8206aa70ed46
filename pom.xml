<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright (c) 2024 fzh Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fzh</groupId>
    <artifactId>fzh</artifactId>
    <name>${project.artifactId}</name>
    <version>3.8.1</version>
    <packaging>pom</packaging>

    <properties>
        <!-- 项目版本号 -->
        <revision>3.8.1</revision>
        <base-revision>3.8.2</base-revision>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.6.1</spring-cloud-alibaba.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring-boot-admin.version>2.7.15</spring-boot-admin.version>
        <spring.authorization.version>0.4.5</spring.authorization.version>
        <captcha.version>2.2.3</captcha.version>
        <screw.version>0.0.1</screw.version>
        <lombok.version>1.18.30</lombok.version>
        <velocity.version>2.3</velocity.version>
        <velocity.tool.version>3.1</velocity.tool.version>
        <configuration.version>1.10</configuration.version>
        <jasypt.version>3.0.5</jasypt.version>
        <knife4j.version>3.0.5</knife4j.version>
        <springdoc.version>1.6.9</springdoc.version>
        <xxl-job.version>2.4.0</xxl-job.version>
        <docker.plugin.version>0.32.0</docker.plugin.version>
        <docker.host>http://*************:2375</docker.host>
        <docker.registry>*************</docker.registry>
        <docker.namespace>fzh</docker.namespace>
        <docker.username>username</docker.username>
        <docker.password>password</docker.password>
        <git.commit.plugin>4.9.9</git.commit.plugin>
        <spring.checkstyle.plugin>0.0.39</spring.checkstyle.plugin>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <mybatis-revision>3.8.3</mybatis-revision>
        <security-revision>3.8.4</security-revision>
        <swagger-revision>3.8.3</swagger-revision>
    </properties>

    <!-- 以下依赖 全局所有的模块都会引入  -->
    <dependencies>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--配置文件加解密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--监控客户端-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <modules>
        <module>gateway</module>
        <module>auth</module>
		<module>app-pmp</module>
        <module>upms</module>
        <module>app-partner</module>
        <module>app-coupon</module>
        <module>apps</module>
        <module>app-job</module>
        <module>app-insurance</module>
        <module>app-payment</module>
    </modules>

    <dependencyManagement>
        <dependencies>
			<dependency>
				<groupId>com.fzh</groupId>
				<artifactId>base-framework</artifactId>
				<version>3.8.2</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

            <!-- spring boot 依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud 依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud alibaba 依赖 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
			<dependency>
				<groupId>com.fzh</groupId>
				<artifactId>upms-api</artifactId>
				<version>3.8.1</version>
				<type>pom</type>
			</dependency>
            <!--文件管理-->
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-oss</artifactId>
                <version>${base-revision}</version>
            </dependency>
            <!--feign 调用-->
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-feign</artifactId>
                <version>${base-revision}</version>
            </dependency>
            <!--安全模块-->
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-security</artifactId>
                <version>${security-revision}</version>
            </dependency>
            <!--日志处理-->
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-log</artifactId>
                <version>${base-revision}</version>
            </dependency>
            <!--接口文档-->
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-swagger</artifactId>
                <version>${swagger-revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-core</artifactId>
                <version>${base-revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-datasource</artifactId>
                <version>${base-revision}</version>
            </dependency>
            <dependency>
                <groupId>com.fzh</groupId>
                <artifactId>common-mybatis</artifactId>
                <version>${mybatis-revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                        <layers>
                            <enabled>true</enabled>
                        </layers>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <configuration>
                        <!-- Docker Remote Api-->
                        <dockerHost>${docker.host}</dockerHost>
                        <!-- Docker 镜像私服-->
                        <registry>${docker.registry}</registry>
                        <!-- 认证信息-->
                        <authConfig>
                            <push>
                                <username>${docker.username}</username>
                                <password>${docker.password}</password>
                            </push>
                        </authConfig>
                        <images>
                            <image>
                                <name>${docker.registry}/${docker.namespace}/${project.name}:${project.version}</name>
                                <build>
                                    <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- 统一 revision 版本 -->
            <!--<plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>-->
            <!--打包jar 与git commit 关联插件-->
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>${git.commit.plugin}</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>initialize</phase>
                    </execution>
                </executions>
                <configuration>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!--因为项目定制了jackson的日期时间序列化/反序列化格式，因此这里要进行配置,不然通过management.info.git.mode=full进行完整git信息监控时会存在问题-->
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.(id|message|time).*$</includeOnlyProperty>
                    </includeOnlyProperties>
                </configuration>
            </plugin>
            <!--
            	代码格式插件，默认使用spring 规则，可运行命令进行项目格式化：./mvnw spring-javaformat:apply 或 mvn spring-javaformat:apply，可在IDEA中安装插件以下插件进行自动格式化：
            	https://repo1.maven.org/maven2/io/spring/javaformat/spring-javaformat-intellij-idea-plugin
            -->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring.checkstyle.plugin}</version>
            </plugin>
        </plugins>
    </build>

	<distributionManagement>
<!--		&lt;!&ndash;Release类型的托管资源库&ndash;&gt;-->
<!--		<repository>-->
<!--			<id>hti</id>-->
<!--			<name>Releases</name>-->
<!--			<url>http://192.168.2.29:8282/repository/maven-releases/</url>-->
<!--		</repository>-->

<!--		<snapshotRepository>-->
<!--			<id>hti</id>-->
<!--			<name>Snapshot</name>-->
<!--			<url>http://192.168.2.29:8282/repository/maven-snapshots/</url>-->
<!--		</snapshotRepository>-->
        <!--Release类型的托管资源库-->
        <repository>
            <!--id对应nexus仓库的id-->
            <id>hti</id>
            <!--自定义名称-->
            <name>apps</name>
            <!--仓库对应的URL地址-->
            <url>http://192.168.2.29:8282/repository/maven-apps/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
	</distributionManagement>

    <profiles>
        <profile>
            <id>cloud</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
				<profiles.namespace>local-dev</profiles.namespace>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
<!--                <profiles.active>prod</profiles.active>-->
<!--                <profiles.namespace>local-dev</profiles.namespace>-->
<!--                <nacos.username>fzh</nacos.username>-->
<!--                <nacos.password>FVs;123?.lg</nacos.password>-->
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>
</project>
