package com.pmp.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pmp.dto.AreaStateDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.entity.AgentCommissionPlan;
import com.pmp.entity.AgentCommissionPlanInstitution;
import com.pmp.entity.InstitutionCommissionLabel;
import com.pmp.entity.InstitutionProviderCommissionPlanTerritory;
import com.pmp.mapper.*;
import com.pmp.service.AgentCommissionPlanService;
import com.pmp.service.InstitutionCenterService;
import com.pmp.util.DateTimeUtils;
import com.pmp.util.UserInfoUtils;
import com.pmp.vo.institution.*;
import com.pmp.vo.partner.PartnerUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  10:11
 * @Version 1.0
 */
@Service
@Slf4j
public class InstitutionCenterServiceImpl implements InstitutionCenterService {

    @Autowired
    private AgentCommissionPlanService commissionPlanService;
    @Autowired
    private AgentCommissionPlanInstitutionMapper planInstitutionMapper;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private AgentCommissionPlanMapper commissionPlanMapper;
    @Autowired
    private InstitutionProviderCommissionPlanTerritoryMapper planTerritoryMapper;
    @Autowired
    private UserInfoUtils userInfoUtils;
    @Autowired
    private InstitutionCommissionLabelMapper institutionCommissionLabelMapper;
    @Autowired
    @Lazy
    private InstitutionCenterService institutionCenterService;

    @Override
    public List<CountryVo> countryList(DateDto dto) {
        List<Long> institutionIds = getInstitutionIds(dto);
        if (CollectionUtils.isEmpty(institutionIds)) {
            return Collections.emptyList();
        }
        return institutionCenterMapper.queryCountryList(institutionIds);
    }

    @Override
    public IPage<InstitutionVo> institutionList(InstitutionDto institutionDto) {
        log.info("查询学校列表,参数:{}", JSONObject.toJSONString(institutionDto));
        Page<InstitutionVo> page = new Page(institutionDto.getCurrent(), institutionDto.getPageSize());
        List<Long> institutionIds = getInstitutionIds(
                DateDto.builder()
                        .agentId(institutionDto.getAgentId())
                        .companyId(institutionDto.getCompanyId())
                        .startDate(institutionDto.getStartDate())
                        .build());
        if (CollectionUtils.isEmpty(institutionIds)) {
            PartnerUserVo partnerUser = userInfoUtils.getPartnerUser();
            log.error("获取到的基础学校为空,institutionIds is empty,当前代理ID:{},分公司ID:{}",
                    Objects.isNull(partnerUser) ? institutionDto.getAgentId() : partnerUser.getAgentId(),
                    Objects.isNull(partnerUser) ? institutionDto.getCompanyId() : partnerUser.getCompanyId());
            return page;
        }
        int year = DateTimeUtils.getYearFromDate(new Date());
        if (Objects.nonNull(institutionDto.getStartDate())) {
            year = DateTimeUtils.getYearFromDate(institutionDto.getStartDate());
        }
        //查询高佣学校
        DateDto dateDto = DateDto.builder()
                .agentId(institutionDto.getAgentId())
                .companyId(institutionDto.getCompanyId())
                .startDate(institutionDto.getStartDate()).build();
        //高佣学校ID
        List<Long> highCommissionIds = institutionCenterService.highCommissionList(Collections.emptyList(), institutionDto.getCompanyId(), dateDto, institutionDto.getHighCommissionCode())
                .stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
        if (Objects.nonNull(institutionDto.getIsHighCommission()) && institutionDto.getIsHighCommission()) {
            if (CollectionUtils.isEmpty(highCommissionIds)) {
                return page;
            }
            institutionIds = highCommissionIds;
        }
        //判断大区
        if (CollectionUtils.isNotEmpty(institutionDto.getApplyCountryIdList())) {
            List<String> regionIds = institutionCenterMapper.selectAreaRegionIdsByCountryIds(institutionDto.getApplyCountryIdList());
            List<Long> regionIdList = regionIds.stream()
                    .filter(Objects::nonNull)
                    .flatMap(s -> Arrays.stream(s.split(",")))
                    .map(String::trim)
                    .filter(str -> !str.isEmpty())
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            // 给国家列表加 0（全选标识）-不选国家会填充为0
            if (!institutionDto.getApplyCountryIdList().contains(0L)) {
                institutionDto.getApplyCountryIdList().add(0L);
            }

            // 给大区列表加 0（全选标识）-不选大区会填充为0
            if (!regionIdList.contains(0L)) {
                regionIdList.add(0L);
            }
            institutionDto.setRegionIdList(regionIdList);
        }
        IPage<InstitutionVo> list = institutionCenterMapper.institutionList(page, institutionDto,
                institutionIds, year,
                Objects.nonNull(institutionDto.getIsHighCommission()) && institutionDto.getIsHighCommission() ? 1 : null);
        //填充学校标签
        PartnerUserVo partnerUser = userInfoUtils.getPartnerUser();
        list.getRecords().stream().forEach(institution -> {
            List<Long> labelIds = institutionCommissionLabelMapper.selectList(new LambdaQueryWrapper<InstitutionCommissionLabel>()
                            .eq(InstitutionCommissionLabel::getFkCompanyId,
                                    Objects.isNull(partnerUser) ? institutionDto.getCompanyId() : partnerUser.getCompanyId())
                            .eq(InstitutionCommissionLabel::getFkInstitutionId, institution.getInstitutionId()))
                    .stream().map(InstitutionCommissionLabel::getFkLabelId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(labelIds)) {
                institution.setLabelList(institutionCommissionLabelMapper.selectCustomizeLabel(labelIds));
            }
            //填充高佣
            if (highCommissionIds.contains(institution.getInstitutionId())) {
                institution.setIsHighCommission(true);
            }
        });
        return list;
    }

    @Override
    public List<InstitutionTypeVo> institutionTypeList() {
        return institutionCenterMapper.queryInstitutionTypeList();
    }

    @Override
    public List<GroupVo> groupList(DateDto dto) {
        List<Long> planIds = commissionPlanService.getPlanIds(dto.getAgentId(), dto.getCompanyId(), dto.getStartDate());
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        List<Long> providerIds = commissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                        .in(AgentCommissionPlan::getId, planIds))
                .stream().map(AgentCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(providerIds)) {
            return Collections.emptyList();
        }
        return institutionCenterMapper.queryGroupList(providerIds);
    }


    private List<Long> getInstitutionIds(DateDto dateDto) {
        List<Long> planIds = commissionPlanService.getPlanIds(dateDto.getAgentId(), dateDto.getCompanyId(), dateDto.getStartDate());
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        List<Long> institutionIds = planInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planIds))
                .stream().map(AgentCommissionPlanInstitution::getFkInstitutionId).distinct().collect(Collectors.toList());
        return institutionIds;
    }

    @Override
    public InstitutionVo institutionDetail(Long institutionId, Integer year) {
        return institutionCenterMapper.institutionDetail(institutionId, year);
    }

    @Override
    public List<AreaStateVo> areaStateList(AreaStateDto dto) {
        return institutionCenterMapper.queryAreaStateList(Objects.isNull(dto.getCountryId()) ?
                null : Arrays.asList(dto.getCountryId()));
    }


    @Override
    public List<CountryVo> territoryList(DateDto dto) {
        List<Long> planIds = commissionPlanService.getPlanIds(dto.getAgentId(), dto.getCompanyId(), dto.getStartDate());
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        List<Long> providerPlanIds = commissionPlanService.list(new LambdaQueryWrapper<AgentCommissionPlan>()
                        .in(AgentCommissionPlan::getId, planIds))
                .stream().map(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId).distinct().collect(Collectors.toList());
        List<Long> territoryIds = planTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .in(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerPlanIds))
                .stream().map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(territoryIds)) {
            return Collections.emptyList();
        }
        return institutionCenterMapper.territoryList(territoryIds);
    }

    @Override
    public List<RegionVo> regionList(DateDto dto) {
        List<Long> planIds = commissionPlanService.getPlanIds(dto.getAgentId(), dto.getCompanyId(), dto.getStartDate());
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        List<Long> providerPlanIds = commissionPlanService.list(new LambdaQueryWrapper<AgentCommissionPlan>()
                        .in(AgentCommissionPlan::getId, planIds))
                .stream().map(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId).distinct().collect(Collectors.toList());
        List<Long> regionIds = planTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .in(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerPlanIds))
                .stream().map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(regionIds)) {
            return Collections.emptyList();
        }
        return institutionCenterMapper.regionList(regionIds);
    }

    @Override
    public List<InstitutionVo> highCommissionList(List<Long> institutionIds, Long companyId, DateDto dto, String highCommissionCode) {
        if (CollectionUtils.isEmpty(institutionIds)) {
            institutionIds = getInstitutionIds(dto);
        }
        if (CollectionUtils.isEmpty(institutionIds)) {
            return Collections.emptyList();
        }
        String dateStr = DateTimeUtils.format(new Date(), DateTimeUtils.YYYY_MM_DD);
        if (Objects.nonNull(dto.getStartDate())) {
            dateStr = DateTimeUtils.format(dto.getStartDate(), DateTimeUtils.YYYY_MM_DD);
        }
        //获取高佣列表-默认查询pmp平台的
        String platformCode = "PMP";
        if (StringUtils.isBlank(highCommissionCode)) {
            if (Objects.nonNull(companyId) && companyId.equals(3L)) {
                //国内
                platformCode = "PMPCHINA";
            }
            if (Objects.nonNull(companyId) && companyId.equals(32L)) {
                //越南
                platformCode = "PMPVIM";
            }
        } else {
            platformCode = highCommissionCode;
        }
        return institutionCenterMapper.selectHighCommissionInstitution(dateStr, institutionIds, platformCode);
    }
}
