package com.partner.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.partner.constant.DataSourceConstant;
import com.partner.entity.MReleaseInfoEntity;
import com.partner.enums.ReleaseInfoStatusEnum;
import com.partner.mapper.MReleaseInfoMapper;
import com.partner.service.MReleaseInfoService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.MReleaseInfoVo;
import com.partner.vo.base.CurrentUserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS(DataSourceConstant.PLATFORM)
public class MReleaseInfoServiceImpl extends ServiceImpl<MReleaseInfoMapper, MReleaseInfoEntity> implements MReleaseInfoService {

    private final MReleaseInfoMapper mReleaseInfoMapper;

    /**
     * 获取已发布的发版信息列表
     *
     * @return 已发布的发版信息列表
     */
    @Override
    public List<MReleaseInfoVo> findReleasedList() {
        // 获取当前登录信息上下文
        CurrentUserContext userContext = UserInfoParamsUtils.getCurrentUserContextWithValidation();

        LambdaQueryWrapper<MReleaseInfoEntity> mReleaseInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<MReleaseInfoEntity>()
                .eq(MReleaseInfoEntity::getStatus, ReleaseInfoStatusEnum.PUBLISHED.getCode())
                .eq(MReleaseInfoEntity::getFkPlatformId, userContext.getPlatformId())
                .orderByDesc(MReleaseInfoEntity :: getVersionNum)
                ;
        List<MReleaseInfoEntity> releaseInfoEntities = this.list(mReleaseInfoEntityLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(releaseInfoEntities)) {
            return Collections.emptyList();
        }
        return releaseInfoEntities.stream()
                .map(mReleaseInfoEntity -> BeanCopyUtils.objClone(mReleaseInfoEntity, MReleaseInfoVo::new))
                .collect(Collectors.toList());
    }

}
 