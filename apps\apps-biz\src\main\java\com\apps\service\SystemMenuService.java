package com.apps.service;

import com.apps.api.entity.SystemMenuEntity;
import com.apps.api.vo.system.MenuTreeVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemMenuService extends IService<SystemMenuEntity> {

    /**
     * 根据角色id获取菜单树
     *
     * @param menuIdList 菜单id列表
     * @return
     * @param menuIdList
     * @return
     */
    List<MenuTreeVo> getMenuTree(List<Long> menuIdList);

    /**
     * 保存菜单
     *
     * @param menu
     */
    void saveMenu(SystemMenuEntity menu);

    /**
     * 删除菜单
     * @param menuId
     */
    void delMenu(List<Long> menuId);
}
