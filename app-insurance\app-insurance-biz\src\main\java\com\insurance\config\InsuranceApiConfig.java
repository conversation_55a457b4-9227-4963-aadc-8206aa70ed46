package com.insurance.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 自动化下单API配置
 */
@Component
@Slf4j
@RefreshScope
@ConfigurationProperties(prefix = "insurance-api")
@Data
public class InsuranceApiConfig {

    private Map<String, String> endpoints = new HashMap<>();

    public String getApiUrl(String code) {
        return endpoints.get(code);
    }

    @PostConstruct
    public void printConfig() {
        log.info("Loaded endpoints: {}", endpoints);
    }
}