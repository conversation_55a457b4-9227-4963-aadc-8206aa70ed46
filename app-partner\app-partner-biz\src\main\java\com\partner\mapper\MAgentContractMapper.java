package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.MAgentContractDto;
import com.partner.entity.MAgentContractEntity;
import com.partner.vo.contract.AgentContractDetailVo;
import com.partner.vo.contract.AgentContractInfoVo;
import com.partner.vo.contract.AgentContractVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【m_agent_contract】的数据库操作Mapper
* @createDate 2025-01-08 11:21:12
* @Entity com.partner.entity.MAgentContract
*/
@Mapper
@DS("saledb")
public interface MAgentContractMapper extends BaseMapper<MAgentContractEntity> {

    IPage<AgentContractVo> getContractPage(Page page, @Param("query") MAgentContractDto dto);



    AgentContractVo selectByAgentId(MAgentContractDto params);


    /**
     * 根据合同ID查询合同信息
     * @param contractId
     * @return
     */
    MAgentContractEntity selectByContractId(@Param("contractId") Long contractId);


    /**
     * 查询合同列表
     * @param page
     * @param params
     * @return
     */
    IPage<AgentContractInfoVo> selectContractPage(Page page, @Param("params") MAgentContractDto params);

    /**
     * 查询合同详情
     * @param contractId
     * @return
     */
    AgentContractDetailVo selectContractDetail(@Param("contractId") Long contractId);

    /**
     * 获取代理最新合同状态
     * @param agentId
     * @return
     */
    AgentContractInfoVo getLatestAgentContractStatus(@Param("agentId") Long agentId);
}




