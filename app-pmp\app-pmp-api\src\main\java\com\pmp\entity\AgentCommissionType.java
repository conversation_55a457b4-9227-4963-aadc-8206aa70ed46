package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("u_agent_commission_type")
public class AgentCommissionType extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "公司Id（每个区域分公司都有自己的分类及命名）")
    private Long fkCompanyId;

    @Schema(description = "分类名称，如：1级代理，2级代理")
    private String typeName;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @Schema(description = "是否激活：0否/1是，当设置屏蔽后，其绑定对应的佣金模板也会失效")
    private Integer isActive;
}
