package com.apps.api.vo.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 菜单树
 *
 * <AUTHOR>
 */
@Data
public class MenuTreeVo {
    @Schema(description = "菜单ID")
    private Long id;

    @Schema(description = "菜单ID")
    private Long fkPlatformId;

    @Schema(description = "菜单ID")
    private String fkPlatformCode;

    @Schema(description = "父菜单Id")
    private Long fkParentMenuId;

    @Schema(description = "菜单类型：0目录，1菜单，2按钮，3自定义")
    private Integer menuType;

    @Schema(description = "权限标识")
    private String permissionKey;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单名称（英文）")
    private String nameEn;

    @Schema(description = "路由路径")
    private String path;

    @Schema(description = "是否可见，0隐藏，1显示")
    private Integer isVisible;

    @Schema(description = "是否缓存，0否，1是")
    private Integer isKeepAlive;

    @Schema(description = "是否内嵌，0否，1是")
    private Integer isEmbedded;

    @Schema(description = "排序值，越小越靠前")
    private Integer viewOrder;

    @Schema(description = "子菜单")
    private List<MenuTreeVo> childTree;

}
