package com.pmp.vo.commission;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  15:36
 * @Version 1.0
 * 附件
 */
@Data
public class MediaVo {

    @Schema(description = "媒体附件Id")
    private Long id;

    @Schema(description = "目标文件名")
    private String fileName;

    @Schema(description = "文件guid")
    private String fileGuid;

    @Schema(description = "源文件类型")
    private String fileTypeOrc;

    @Schema(description = "源文件名")
    private String fileNameOrc;

    @Schema(description = "目标文件路径")
    private String filePath;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "表Id")
    private String tableId;

    @Schema(description = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;

    @Schema(description = "链接")
    private String link;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "上传时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

}
