package com.partner.dto.ais;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/7/23
 * @Version 1.0
 * @apiNote:生成pdf请求类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateContractPdfDto {

    @Schema(description = "加密密钥")
    private String secret;

    @Schema(description = "加密后的合同Id")
    private String encryptContractId;

    @Schema(description = "加密后的代理商Id")
    private String encryptAgentId;

    @Schema(description = "合同版本")
    private Integer contractVsion;

    @Schema(description = "合同模板模式")
    private Integer contractTemplateMode;
}
