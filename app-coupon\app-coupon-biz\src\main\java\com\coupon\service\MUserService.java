package com.coupon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.common.core.util.R;
import com.coupon.dto.UserDto;
import com.coupon.entity.MUserEntity;
import com.coupon.vo.AreaRegionSelectVo;
import com.coupon.vo.BdVo;

import java.util.List;

public interface MUserService extends IService<MUserEntity> {
    List<AreaRegionSelectVo> getAreaRegionSelect(Long fkCompanyId);

    Boolean userExists(UserDto userDto);

    List<BdVo> getBDByAreaRegion(Long fkAreaRegionId);

    R getDetail();

    R updateUser(UserDto userDto);
}
