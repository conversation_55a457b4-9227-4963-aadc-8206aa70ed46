package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payment.entity.InsuranceOrderMpPayment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 微信支付订单 Mapper 接口
 */
@Mapper
public interface InsuranceOrderMpPaymentMapper extends BaseMapper<InsuranceOrderMpPayment> {

    /**
     * 根据订单编号查询订单ID
     *
     * @param orderNo 订单编号
     * @return 订单ID
     */
    Long selectInsuranceOrderIdByOrderNo(@Param("orderNo") String orderNo);


    /**
     * 根据订单编号更新订单ID
     *
     * @param orderNo 订单编号
     * @param mpPaymentStatus 支付状态
     */
    void updateInsuranceOrderIdByOrderNo(@Param("orderNo") String orderNo, @Param("mpPaymentStatus") Integer mpPaymentStatus);
}
