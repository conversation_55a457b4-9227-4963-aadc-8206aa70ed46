package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.MLiveParamsDto;
import com.partner.dto.MLivecalendarParamsDto;
import com.partner.dto.RLiveUserAppointmentDto;
import com.partner.service.MLiveService;
import com.partner.service.RLiveUserAppointmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(description = "rLiveUserAppointment" , name = "小程序-热门培训-登录调用接口" )
@RestController
@RequestMapping("/rLiveUserAppointment")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class RLiveUserAppointmentController {

    private final RLiveUserAppointmentService apointmentService;

    private final MLiveService mLiveService;

    @Operation(summary = "直播分页查询" , description = "最新直播-往期培训-培训日历" )
    @SysLog("直播分页查询" )
    @GetMapping("/getMLiveListPage" )
    public R getMLiveListPage(Page page, @ParameterObject @Valid MLiveParamsDto dto) {
        dto.setLoginType("1");//已登录
        return R.ok(mLiveService.getMLiveListPage(page, dto));
    }



    @Operation(summary = "首页最新直播查询" , description = "首页最新直播查询" )
    @SysLog("首页最新直播查询" )
    @GetMapping("/getHomeMLiveList" )
    public R getHomeMLiveList() {
        String loginType="1";//已登录
        return R.ok(mLiveService.getHomeMLiveList(loginType));
    }



    @Operation(summary = "直播预约" , description = "直播预约" )
    @SysLog("直播预约" )
    @PostMapping("/userAppointment" )
    public R userAppointment(@RequestBody  @Validated RLiveUserAppointmentDto appointmentEntity) {
        return R.ok(apointmentService.userAppointment(appointmentEntity));
    }

    @Operation(summary = "直播-取消预约" , description = "直播预约" )
    @SysLog("直播预约" )
    @PostMapping("/cancelAppointment" )
    public R cancelAppointment(@RequestBody  @Validated RLiveUserAppointmentDto appointmentEntity) {
        return R.ok(apointmentService.cancelAppointment(appointmentEntity));
    }


    @Operation(summary = "直播预约-列表" , description = "直播预约-列表" )
    @SysLog("直播预约-列表" )
    @GetMapping("/getAppointmentList/{liveId}" )
    public R getAppointmentList(@PathVariable("liveId") Long liveId){

        return R.ok(apointmentService.getAppointmentList(liveId));
    }

    @Operation(summary = "直播-日历有数据状态" , description = "直播-日历有数据状态" )
    @SysLog("直播预约-日历有数据状态" )
    @PostMapping("/getAppointment/getStatus" )
    public R getStatus(@RequestBody  @Valid MLivecalendarParamsDto params){

        return R.ok(mLiveService.getStatus(params));
    }



}
