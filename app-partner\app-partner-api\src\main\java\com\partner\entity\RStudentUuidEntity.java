package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-24 18:27:18
 */

@Data
@TableName("r_student_uuid")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_student_uuid ")
public class RStudentUuidEntity extends Model<RStudentUuidEntity>{

  @Schema(description = "学生UUID关系Id")
  private Long id;
 

  @Schema(description = "学生Id")
  private Long fkStudentId;
 

  @Schema(description = "学生UUID")
  private String fkStudentUuid;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
