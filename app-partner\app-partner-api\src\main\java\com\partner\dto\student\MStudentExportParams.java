package com.partner.dto.student;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@Schema(description = "学生导出查询参数")
public class MStudentExportParams extends UserInfoParams {


    @Schema(description = "学生姓名")
    private String studentName;


    @Schema(description = "代理Id")
    private Long fkAgentId;

    @Schema(description = "开始时间")
    private LocalDate gmtCreateStart;

    @Schema(description = "结束时间")
    private LocalDate gmtCreateEnd;

    @Schema(description = "权限控制标志 - 无权限时为true")
    private Boolean studentFlag = false;

    @Schema(description = "个人权限标志 - true表示只能查看个人相关数据")
    private Boolean roleTypeFlag = false;



}
