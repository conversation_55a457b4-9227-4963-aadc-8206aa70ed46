<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MPartnerUserMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MPartnerUserEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="fkTenantId" column="fk_tenant_id" jdbcType="BIGINT"/>
        <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
        <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
        <result property="fkUserId" column="fk_user_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="nameEn" column="name_en" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
        <result property="mobileAreaCode" column="mobile_area_code" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="telAreaCode" column="tel_area_code" jdbcType="VARCHAR"/>
        <result property="tel" column="tel" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="company" column="company" jdbcType="VARCHAR"/>
        <result property="department" column="department" jdbcType="VARCHAR"/>
        <result property="position" column="position" jdbcType="VARCHAR"/>
        <result property="qq" column="qq" jdbcType="VARCHAR"/>
        <result property="whatsapp" column="whatsapp" jdbcType="VARCHAR"/>
        <result property="wechat" column="wechat" jdbcType="VARCHAR"/>
        <result property="wechatNickname" column="wechat_nickname" jdbcType="VARCHAR"/>
        <result property="wechatIconUrl" column="wechat_icon_url" jdbcType="VARCHAR"/>
        <result property="isIdentityChecked" column="is_identity_checked" jdbcType="BIT"/>
        <result property="isModifiedPs" column="is_modified_ps" jdbcType="BIT"/>
        <result property="isActive" column="is_active" jdbcType="BIT"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getApportionPartnerUser" resultType="com.partner.vo.student.StudentApportionVo">
        /*有学生查看权限的用户都可以分派-STUDENT_VIEW,STUDENT_VIEW_PERSON,STUDENT_VIEW_ALL*/
        SELECT u.id as partnerUserId,
               u.name,
               u.email,
               GROUP_CONCAT(DISTINCT r.role_name ORDER BY r.role_name SEPARATOR ',') AS userType
        FROM app_partner_center.m_partner_user u
                 JOIN app_partner_center.r_partner_user_partner_role ur
                      ON u.id = ur.fk_partner_user_id
                 JOIN app_partner_center.m_partner_role r
                      ON ur.fk_partner_role_id = r.id
                 JOIN app_partner_center.m_partner_role_menu rm
                      ON r.id = rm.fk_partner_role_id
                 JOIN app_system_center.system_menu m
                      ON rm.fk_menu_id = m.id
        WHERE u.fk_agent_id = #{agentId}
          AND m.permission_key in ('STUDENT_VIEW','STUDENT_VIEW_PERSON','STUDENT_VIEW_ALL')
          AND (
            r.fk_agent_id = #{agentId}
                OR r.fk_agent_id IS NULL
                OR r.fk_agent_id = 0
            )
        GROUP BY u.id, u.name, u.email
    </select>


    <select id="getTeamPeopleCount" resultType="java.lang.Integer">
        SELECT count(*) FROM m_partner_user WHERE fk_agent_id=#{agentId} AND is_active=1
        <include refid="com.partner.mapper.PermissionSqlMapper.partNerUserPermissionSql"/>

        <if test="year!=null and year != 0 ">
            AND YEAR(gmt_create) &lt;=
            #{year}
        </if>
    </select>

    <select id="getAllAmount" resultType="java.math.BigDecimal">
        SELECT sum(mPaymentFormItem.amount_rmb) AS allAmount FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionSql"/>
        INNER JOIN ais_sale_center.m_payable_plan mPayablePlan
        ON mPayablePlan.fk_type_target_id = offerItem.id AND mPayablePlan.fk_type_key = 'm_student_offer_item'
        INNER JOIN ais_finance_center.m_payment_form_item mPaymentFormItem ON
        mPaymentFormItem.fk_payable_plan_id=mPayablePlan.id
        WHERE 1=1
        <if test="year!=null and year != 0 ">
            AND YEAR(offerItem.gmt_create)=
            #{year}
        </if>
        <!-- WHERE  offerItem.fk_agent_id=#{agentId} AND year(mPaymentFormItem.gmt_create)=#{year}-->

    </select>

    <select id="getAgentMPayablePlan" resultType="com.partner.vo.my.MPayablePlanMyDetailVo">
        SELECT mPayablePlan.id,mPayablePlan.fk_currency_type_num,mPayablePlan.payable_amount FROM
        (
        SELECT offerItem.id AS itemId,min(rStudentOfferItemStep.gmt_create) AS gmt_create FROM
        ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionSql"/>
        INNER JOIN ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON
        offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
        WHERE rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10) GROUP BY offerItem.id
        ) a
        INNER JOIN ais_sale_center.m_payable_plan mPayablePlan
        ON mPayablePlan.fk_type_target_id = a.itemId AND mPayablePlan.fk_type_key = 'm_student_offer_item'
        WHERE 1=1
        <if test="year!=null and year != 0 ">
            AND YEAR(a.gmt_create)=
            #{year}
        </if>
    </select>


    <select id="getLevelAmount" resultType="java.math.BigDecimal">

        SELECT sum(mPaymentFormItem.amount_rmb) AS amount_rmb FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionSql"/>
        INNER JOIN ais_sale_center.m_payable_plan mPayablePlan
        ON mPayablePlan.fk_type_target_id = offerItem.id AND mPayablePlan.fk_type_key = 'm_student_offer_item'
        INNER JOIN ais_finance_center.m_payment_form_item mPaymentFormItem ON
        mPaymentFormItem.fk_payable_plan_id=mPayablePlan.id
        WHERE offerItem.fk_agent_id=#{agentId}
    </select>


    <select id="getSelfAmount" resultType="com.partner.vo.my.MPayablePlanMyDetailVo">
        SELECT mPayablePlan.id,
        mPayablePlan.fk_currency_type_num,
        mPayablePlan.payable_amount,
        (SELECT count(*) FROM app_partner_center.r_partner_user_student rPpartnerUserStudent WHERE
        rPpartnerUserStudent.fk_student_id=offerItem.fk_student_id
        AND rPpartnerUserStudent.is_active=1) AS fenpeiNum
        FROM
        ais_sale_center.m_student_offer_item offerItem INNER JOIN
        (
        SELECT offerItem.id AS itemId,min(rStudentOfferItemStep.gmt_create) AS gmt_create FROM
        ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionSql"/>
        INNER JOIN ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON
        offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
        WHERE rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10) GROUP BY offerItem.id
        ) a ON offerItem.id=a.itemId
        INNER JOIN ais_sale_center.m_payable_plan mPayablePlan
        ON mPayablePlan.fk_type_target_id = a.itemId AND mPayablePlan.fk_type_key = 'm_student_offer_item'
        WHERE 1=1
        <if test="year!=null and year != 0 ">
            AND YEAR(a.gmt_create)=
            #{year}
        </if>
    </select>

    <select id="selectPartnerUserAgentList" resultType="com.partner.vo.agent.PartnerUserAgentVo">
        select pu.id          as partnerUserId,
               pu.name        as partnerUserName,
               pu.fk_agent_id as agentId,
               ma.name        as agentName
        from app_partner_center.m_partner_user pu
                 left join ais_sale_center.m_agent ma on pu.fk_agent_id = ma.id
        where pu.fk_user_id = #{userId}
        order by pu.id desc
    </select>


</mapper>
