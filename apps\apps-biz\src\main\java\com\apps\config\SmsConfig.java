package com.apps.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * 腾讯云短信配置类
 */
@Configuration
@ConfigurationProperties(prefix = "sms")
@RefreshScope
@Data
public class SmsConfig {
    private String secretId;
    private String secretKey;
    private String endpoint;
    private String signMethod;
    private String region;
    private String sdkAppId;
    private String signName;
    private String templateId;
}
