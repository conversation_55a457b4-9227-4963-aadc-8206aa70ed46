package com.partner.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.partner.wechat.Template.BaseValue;
import com.partner.wechat.Template.LiveMessageTemplate;
import com.partner.wechat.params.SendMessageParams;
import com.partner.wechat.result.AccessTokenResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

public class HttpClientUtils {
    private static final Logger log = LoggerFactory.getLogger(HttpClientUtils.class);

    /**
     *
     * @param url
     * @param headerParams
     * @param sendMessageParams
     * @desc 订阅模板发送信息方法
     * @return
     */
    public static String sendPostRequest(String url,Map<String, String> headerParams,
                                         SendMessageParams sendMessageParams) {
        RestTemplate client = new RestTemplate();
        //设置请求发送方式HttpMethod.GET、HttpMethod.DELETE等
        HttpMethod method = HttpMethod.POST;

        HttpHeaders headers = new HttpHeaders();
        //给请求头设置参数
        for (String key : headerParams.keySet()) {
            headers.add(key, headerParams.get(key));
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        //请求参数
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(sendMessageParams), headers);

        //执行HTTP请求，将返回的结构使用String 类格式化（可设置为对应返回值格式的类）
        ResponseEntity<String> response = client.exchange(url, method, requestEntity, String.class);
        log.info("推送模板消息参数:{};推送模板消息结果:{}",JSON.toJSONString(sendMessageParams), response.getBody());
        //返回类型也可以自动填充到实体类当中去，比如我自己创建了User类，当然字段名称要和返回字段一致
        return response.getBody();
    }




    public static void main(String[] args) {
       /* String url="https://api.weixin.qq.com/cgi-bin/stable_token";

        Map<String, String> headerParams = new HashMap<>();
        Map<String, String> bodyParams = new HashMap<>();
        bodyParams.put("grant_type", "client_credential");

        bodyParams.put("appid","wx13fec1a5be977e0c");
        bodyParams.put("secret","ec27fb0f8d845ac0b99364fd8d64374a");
        String tokenResult=sendGetRequest(url, headerParams, bodyParams);
        System.out.println(tokenResult);
        AccessTokenResult accessTokenResult = JSON.parseObject(tokenResult, AccessTokenResult.class);*/


        String tmp_token="90_GWDq06_VP3X1V67Y6Vz6NDYtaABkBJrp21g-wRSTPGSCwyvOHuBs-l8BSpc7gpnK6hcYOhRaFywBbyrc6ZVO44ZBq2ZXeshWIQS-LrhawXvnq7QKV7rGD9EvLMUNGDhAJAHAX";

        String sendMessageUrl="https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token="+tmp_token;
        String template_id="DV1ohVx1oYPUuWGQVnWUheInL5lAzk7dKSbsM5zWGAw";


        /*Map<String, Object> bodyMessageParams = new HashMap<>();
        bodyMessageParams.put("template_id", template_id);
        bodyMessageParams.put("touser", "oWpUY7fdXkAxyg5WBTFo0vcgkG7g");*/
        Map headerParams = new HashMap();
        headerParams.put("Content-Type", "application/json");
        SendMessageParams sendMessageParams = new SendMessageParams();
        sendMessageParams.setTemplate_id(template_id);
        sendMessageParams.setTouser("oWpUY7fdXkAxyg5WBTFo0vcgkG7g");


        LiveMessageTemplate messageTemplate = new LiveMessageTemplate();

        BaseValue thingValue = new BaseValue();
        thingValue.setValue("华通伙伴直播等你来！");
        messageTemplate.setThing1(thingValue);
        BaseValue timeValue = new BaseValue();
        timeValue.setValue("2025-03-24");
        messageTemplate.setTime2(timeValue);

        /*bodyMessageParams.put("data", JSONObject.toJSON(messageTemplate));
        System.out.println(JSONObject.toJSON(bodyMessageParams));*/
        sendMessageParams.setData(messageTemplate);
        System.out.println(JSONObject.toJSON(sendMessageParams));

        //订阅消息推送
        System.out.println(sendPostRequest(sendMessageUrl,headerParams, sendMessageParams));


        //获取类目
        String leimuUrl="https://api.weixin.qq.com/wxaapi/newtmpl/getcategory?access_token="+tmp_token;
        Map<String, String> headerMessageParams1 = new HashMap<>();
        Map<String, String> bodyMessageParams1 = new HashMap<>();
        //System.out.println(sendGetRequest(leimuUrl,headerMessageParams1,bodyMessageParams1));

        String pubtempUrl="https://api.weixin.qq.com/wxaapi/newtmpl/getpubtemplatetitles?access_token"+tmp_token+"&ids=590";


    }




}
