package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RAgentUuidEntity;
import com.partner.entity.RStudentOfferItemUuidEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【r_agent_uuid】的数据库操作Mapper
* @createDate 2025-01-03 11:30:13
* @Entity com.partner.entity.RAgentUuid
*/
@Mapper
@DS("saledb")
public interface RAgentUuidMapper extends BaseMapper<RAgentUuidEntity> {
    RAgentUuidEntity selectByUUID(@Param("uuid") String uuid);
}




