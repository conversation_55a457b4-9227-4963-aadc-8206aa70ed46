
package com.insurance.controller;


import com.common.core.util.R;
import com.insurance.dto.order.InsurancePlanDTO;
import com.insurance.service.ProductTypeYearAmountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;

@RestController
@Slf4j
@RequestMapping("/productTypeYearAmount")
@RequiredArgsConstructor
@Tag(description = "保险金额配置", name = "保险金额配置")
public class ProductTypeYearAmountController {

    private final ProductTypeYearAmountService productTypeYearAmountService;

    @Operation(summary = "根据开始结束日期计算保单预估金额")
    @PostMapping("/calculateInsuranceEstimated")
    public R<BigDecimal> calculateInsuranceEstimatedAmount(@Valid @RequestBody InsurancePlanDTO insurancePlanDTO) {
        return R.ok(this.productTypeYearAmountService.calculateInsuranceEstimatedAmount(insurancePlanDTO));
    }

}
