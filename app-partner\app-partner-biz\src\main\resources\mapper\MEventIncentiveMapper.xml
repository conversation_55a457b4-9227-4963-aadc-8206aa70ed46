<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.MEventIncentiveMapper">

    <select id="getMEventIncentivePage"
            resultType="com.partner.vo.MEventIncentiveVo">
        SELECT eventIncentive.id                                          AS eventIncentiveId,
               eventIncentive.event_title                                 AS eventIncentiveTitle,
               institutionProvider.`name`                                      AS institutionProviderName,
               a.area_country_names                          AS areaCountryNames,
               IFNULL(eventIncentive.actual_publicity_time, eventIncentive.gmt_create) AS releaseTime,
               eventIncentive.`status`                                    AS status,
               b.files                                       AS files
        FROM ais_sale_center.m_event_incentive eventIncentive
                 LEFT JOIN ais_institution_center.m_institution_provider institutionProvider ON eventIncentive.fk_institution_provider_id = institutionProvider.id
                 LEFT JOIN (SELECT rAreaCountry.fk_event_incentive_id,
                                   GROUP_CONCAT(uAreaCountry.name_chn ORDER BY uAreaCountry.view_order DESC) area_country_names,
                                   GROUP_CONCAT(uAreaCountry.id ORDER BY uAreaCountry.view_order DESC)       area_country_ids
                            FROM ais_sale_center.r_event_incentive_area_country rAreaCountry
                            LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON rAreaCountry.fk_area_country_id = uAreaCountry.id
                            GROUP BY rAreaCountry.fk_event_incentive_id) a
                 ON eventIncentive.id = a.fk_event_incentive_id
                 LEFT JOIN (SELECT sAttached.fk_table_id,
                                   GROUP_CONCAT(#{query.mMageAddress}, salefile.file_key) files
                            FROM ais_sale_center.s_media_and_attached sAttached
                            INNER JOIN ais_file_center.m_file_sale salefile ON sAttached.fk_file_guid = salefile.file_guid
                            WHERE sAttached.fk_table_name = 'm_event_incentive'
                            GROUP BY sAttached.fk_table_id) b
                 ON eventIncentive.id = b.fk_table_id

        WHERE fk_company_id = #{query.companyId} AND
            FIND_IN_SET(13, eventIncentive.public_level)


        <if test="query.areaCountryId != null  ">
            AND FIND_IN_SET(#{query.areaCountryId}, a.area_country_ids)
        </if>
        <if test="query.eventIncentiveTitle != null   and query.eventIncentiveTitle  != '' ">
            AND (eventIncentive.event_title   like CONCAT('%', #{query.eventIncentiveTitle} , '%')
                OR institutionProvider.name   like CONCAT('%', #{query.eventIncentiveTitle} , '%')
                )
        </if>



        ORDER BY IFNULL(eventIncentive.actual_publicity_time, eventIncentive.gmt_create) DESC, eventIncentive.id
    </select>

    <select id="getCountryCombox" resultType="com.partner.vo.combox.CountryCombox">
        SELECT DISTINCT uAreaCountry.id AS areaCountryId,
                        uAreaCountry.name_chn AS areaCountryName
        FROM ais_sale_center.m_event_incentive eventIncentive
                 INNER JOIN ais_sale_center.r_event_incentive_area_country rAreaCountry ON eventIncentive.id = rAreaCountry.fk_event_incentive_id
                 LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON rAreaCountry.fk_area_country_id = uAreaCountry.id
        WHERE eventIncentive.fk_company_id = #{companyId} AND eventIncentive.public_level = 1
        ORDER BY uAreaCountry.view_order DESC  limit 100
    </select>
    <select id="getMeventDetail" resultType="com.partner.vo.MEventIncentiveVo">
        SELECT eventIncentive.id                                          AS eventIncentiveId,
               eventIncentive.event_title                                 AS eventIncentiveTitle,
               institutionProvider.`name`                                      AS institutionProviderName,
               a.area_country_names                          AS areaCountryNames,
               IFNULL(eventIncentive.actual_publicity_time, eventIncentive.gmt_create) AS releaseTime,
               eventIncentive.`status`                                    AS status,
               b.files                                       AS files
        FROM ais_sale_center.m_event_incentive eventIncentive
                 LEFT JOIN ais_institution_center.m_institution_provider institutionProvider ON eventIncentive.fk_institution_provider_id = institutionProvider.id
                 LEFT JOIN (SELECT rAreaCountry.fk_event_incentive_id,
                                   GROUP_CONCAT(uAreaCountry.name_chn ORDER BY uAreaCountry.view_order DESC) area_country_names,
                                   GROUP_CONCAT(uAreaCountry.id ORDER BY uAreaCountry.view_order DESC)       area_country_ids
                            FROM ais_sale_center.r_event_incentive_area_country rAreaCountry
                                     LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON rAreaCountry.fk_area_country_id = uAreaCountry.id
                            GROUP BY rAreaCountry.fk_event_incentive_id) a
                           ON eventIncentive.id = a.fk_event_incentive_id
                 LEFT JOIN (SELECT sAttached.fk_table_id,
                                   GROUP_CONCAT(#{mMageAddress}, salefile.file_key) files
                            FROM ais_sale_center.s_media_and_attached sAttached
                                     INNER JOIN ais_file_center.m_file_sale salefile ON sAttached.fk_file_guid = salefile.file_guid
                            WHERE sAttached.fk_table_name = 'm_event_incentive'
                            GROUP BY sAttached.fk_table_id) b
                           ON eventIncentive.id = b.fk_table_id

        WHERE  eventIncentive.id=#{eventIncentiveId}




    </select>

</mapper>