package com.apps.service.logic.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.api.vo.InviteTemplateVo;
import com.apps.api.vo.SmsResp;
import com.apps.config.SmsConfig;
import com.apps.constant.redis.RedisConstant;
import com.apps.exception.AppsGlobalException;
import com.apps.mapper.SystemUserMapper;
import com.apps.service.logic.SmsService;
import com.apps.util.RedisUtil;
import com.common.core.constant.SecurityConstants;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/1/14  09:55
 * @Version 1.0
 */
@Service
@Slf4j
public class SmsServiceImpl implements SmsService {

    @Autowired
    private SmsConfig smsConfig;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private JavaMailSender javaMailSender;

    @Value("${spring.mail.username:<EMAIL>}")
    private String from;
    @Value("${enableEmail:true}")
    private boolean enableEmail;
    @Autowired
    private SystemUserMapper systemUserMapper;
    @Value("${qrCodeUrl:https://partner-demo.fanzhihua.com.cn/partner-center/wx/feign/getLinkAndQrCode}")
    private String qrCodeUrl;

    @SneakyThrows
    @Override
    public void sendSmsCode(String mobile) {

        SmsClient client = getSmsClient();
        SendSmsRequest req = new SendSmsRequest();
        req.setSmsSdkAppId(smsConfig.getSdkAppId());
        req.setSignName(smsConfig.getSignName());
        req.setTemplateId(smsConfig.getTemplateId());
        String code = RandomUtil.randomNumbers(Integer.parseInt(SecurityConstants.CODE_SIZE));
        String[] templateParamSet = {code, "3"};
        req.setTemplateParamSet(templateParamSet);
        String[] phoneNumberSet = {"+86" + mobile};
        req.setPhoneNumberSet(phoneNumberSet);
        // 发送短信
        SendSmsResponse res = client.SendSms(req);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SmsResp smsResponse = objectMapper.readValue(SendSmsResponse.toJsonString(res), SmsResp.class);
        log.error("短信发送结果：{}", JSONObject.toJSONString(smsResponse));
        if (!smsResponse.getSendStatusSet().get(0).getMessage().contains("send success")) {
            log.error("短信发送失败：{}，详细信息：{}", SendSmsResponse.toJsonString(res), smsResponse.getSendStatusSet().get(0).getMessage());
            throw new AppsGlobalException(GlobExceptionEnum.SMS_CODE_SEND_ERROR);
        }
        log.info("短信发送成功：{}，内容是：{}", SendSmsResponse.toJsonString(res), code);
        redisUtil.set(RedisConstant.SMS_CODE_KEY_PREFIX + mobile, code, RedisConstant.SMS_CODE_EXPIRE_TIME);
    }

    @NotNull
    private SmsClient getSmsClient() {
        Credential cred = new Credential(smsConfig.getSecretId(), smsConfig.getSecretKey());

        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setReqMethod("GET");
        httpProfile.setConnTimeout(10);
        httpProfile.setWriteTimeout(10);
        httpProfile.setReadTimeout(10);

        /* 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com ，也支持指定地域域名访问，例如广州地域的域名为 sms.ap-guangzhou.tencentcloudapi.com */
        httpProfile.setEndpoint(smsConfig.getEndpoint());

        /* 非必要步骤:
         * 实例化一个客户端配置对象，可以指定超时时间等配置 */
        ClientProfile clientProfile = new ClientProfile();
        /* SDK默认用TC3-HMAC-SHA256进行签名
         * 非必要请不要修改这个字段 */
        clientProfile.setSignMethod(smsConfig.getSignMethod());
        clientProfile.setHttpProfile(httpProfile);

        SmsClient client = new SmsClient(cred, smsConfig.getRegion(), clientProfile);
        return client;
    }

    @Override
    public void sendMail(String subject, String content, String toEmail, Integer type) {

//        String url="http://192.168.2.31:30001/partner-center/wx/feign/getLinkAndQrCode";
        String url=qrCodeUrl;
        JSONObject jsonObject=new  JSONObject();
        jsonObject.put("path","pages/index/index");
        jsonObject.put("width",430);
        HttpResponse response = HttpRequest.post(url)
                .body(JSON.toJSONString(jsonObject), "application/json")
                .execute();

        String body = response.body();
        JSONObject result = JSONObject.parseObject(body);
        log.info("获取小程序二维码结果：{}", result);
        if (Objects.isNull( result.getJSONObject("data"))){
            log.error("获取小程序二维码失败：{}", result);
            throw new AppsGlobalException(GlobExceptionEnum.REMOTE_SERVICE_NO_RESULT);
        }
        JSONObject data = result.getJSONObject("data");

        String emailContent = "您的账号已注册成功，账号：" + toEmail + "，密码：" + content;
        InviteTemplateVo inviteTemplate = systemUserMapper.selectTemplateByType(type);
        if (Objects.nonNull(inviteTemplate)) {
            Map replacements = new HashMap();
            replacements.put("account", toEmail);
            replacements.put("password", content);
            replacements.put("qrCode", StringUtils.isNotBlank(data.getString("qrCode")) ? data.getString("qrCode") : "");
            replacements.put("shortLink", StringUtils.isNotBlank(data.getString("url")) ? data.getString("url") : "");
            if (type.equals(3)){
                replacements.put("name", content);
            }
            emailContent = replaceTemplate(inviteTemplate.getTemplate(), replacements);
            subject = inviteTemplate.getTitle();
        } else {
            log.error("邮件模板为空，无法发送邮件");
        }
        try {
            if (!enableEmail) {
                log.info("邮件发送失败，邮件发送功能未开启");
                return;
            }
            MimeMessage mail = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mail);

            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setFrom(from);
            helper.setText(emailContent, true);
            javaMailSender.send(mail);
            log.info("邮件发送成功===》邮件：{},内容：{}", toEmail, content);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("邮件发送失败》邮件：{},内容：{}", toEmail, content);
            log.error("邮件发送出现异常：{}", e.getMessage());
        }
    }

    private String replaceTemplate(String template, Map<String, String> replacements) {
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            template = template.replace("#{" + entry.getKey() + "}", entry.getValue());
        }
        return template;
    }
}
