package com.insurance.vo.settlement;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/18
 * @Version 1.0
 * @apiNote:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RateDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "原币种")
    private String from;

    @Schema(description = "目标币种")
    private String to;

    @Schema(description = "原币种名称")
    private String fromname;

    @Schema(description = "目标币种名称")
    private String toname;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date updatetime;

    @Schema(description = "汇率")
    private BigDecimal rate;

    @Schema(description = "目标币种转换后的金额")
    private BigDecimal camount;
}
