package com.insurance.constant;

/**
 * @Author:<PERSON>
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:
 */
public class RedisConstant {

    /**
     * 全局缓存键前缀
     */
    public static final String REDIS_KEY_PREFIX = "insurance:";

    /**
     * 下单中订单缓存键
     */
    public static final String ORDER_PROGRESSING_KEY = REDIS_KEY_PREFIX + "order:progressing";

    /**
     * 下单失败订单缓存键
     */
    public static final String ORDER_FAILED_KEY = REDIS_KEY_PREFIX + "order:failed:";

    /**
     * 下单锁缓存键
     */
    public static final String ORDER_LOCK_KEY = REDIS_KEY_PREFIX + "order:lock";

    /**
     * 下单锁过期时间（秒）
     */
    public static final int LOCK_EXPIRE_SECONDS = 10;

    /**
     * 订单信用卡加密缓存键
     */
    public static final String ORDER_ENCRYPT_CARD = REDIS_KEY_PREFIX + "order:encrypt:card:";

    /**
     * 伙伴用户信息缓存键
     */
    public static final String PARTNER_USER = REDIS_KEY_PREFIX + "partner:user";

    /**
     * 汇率缓存键
     */
    public static final String RATE = REDIS_KEY_PREFIX + "rate:";

    /**
     * 订单信息缓存键
     */
    public static final String ORDER_INFO = REDIS_KEY_PREFIX + "placeOrder:info:";

    /**
     * 流水号前缀
     */
    public static final String TRADE_NO = REDIS_KEY_PREFIX + "tradeNo:";

    /**
     * 订单关闭
     */
    public static final String CLOSE_ORDER = REDIS_KEY_PREFIX + "close:";

    /**
     * 订单关闭时间（秒）
     */
    public static final int CLOSE_ORDER_SECOND = 600;

}
