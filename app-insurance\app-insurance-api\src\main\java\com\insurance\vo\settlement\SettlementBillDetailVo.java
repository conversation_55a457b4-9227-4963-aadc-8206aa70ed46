package com.insurance.vo.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:对账单详细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementBillDetailVo {

    @Schema(description = "对账单Id")
    private Long settlementBillId;

    @Schema(description = "结算币种")
    private String settlementCurrencyTypeNum;

    @Schema(description = "总计结算金额-结算币种")
    private BigDecimal totalSettlementAmount;

    @Schema(description = "订单币种")
    private String orderCurrencyTypeNum;

    @Schema(description = "总计结算金额-订单币种")
    private BigDecimal originTotalSettlementAmount;

    @Schema(description = "汇率")
    private BigDecimal rate;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "订单明细")
    private List<SettlementBillItemVo> billItems;

    @Schema(description = "代理账户")
    private AgentAccountVo agentAccount;
}
