package com.coupon.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.apps.api.dto.coupon.SaveCouponUserDto;
import com.apps.api.enums.LoginTypeEnum;
import com.apps.api.enums.RoleCodeEnum;
import com.apps.api.feign.RemoteAppsService;
import com.apps.api.feign.RemoteAuthService;
import com.apps.api.vo.TokenVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.common.core.util.R;
import com.coupon.dto.EnrollUserDto;
import com.coupon.entity.MUserEntity;
import com.coupon.mapper.MUserMapper;
import com.coupon.service.ILoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class LoginServiceImpl implements ILoginService {
    @Resource
    private MUserMapper mUserMapper;
    @Autowired
    private RemoteAppsService appsService;
    @Autowired
    private RemoteAuthService remoteAuthService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R enrollUser(EnrollUserDto enrollUserDto) throws Exception {

        MUserEntity currentUser = mUserMapper.selectOne(new LambdaQueryWrapper<MUserEntity>().eq(MUserEntity::getMobile, enrollUserDto.getMobile()));
        if (Objects.nonNull(currentUser)) {
            throw new Exception("用户已注册");
        }
        String roleCode = "";
        if (StringUtils.isNotBlank(enrollUserDto.getRole())) {
            roleCode = enrollUserDto.getRole().equals("1") ? RoleCodeEnum.STUDENT.getCode() :
                    enrollUserDto.getRole().equals("2") ? RoleCodeEnum.AGENT.getCode() : RoleCodeEnum.STAFF.getCode();
        }
        SaveCouponUserDto couponUserDto = SaveCouponUserDto.builder()
                .platformCode(enrollUserDto.getPlatformCode())
                .platformId(enrollUserDto.getPlatformId())
                .name(enrollUserDto.getNickName())
                .nickName(enrollUserDto.getNickName())
                .mobile(enrollUserDto.getMobile())
                .gender(enrollUserDto.getGender())
                .mobileAreaCode(enrollUserDto.getMobileAreaCode())
                .company(enrollUserDto.getCompany())
                .wechat(enrollUserDto.getWechat())
                .roleCode(roleCode)
                .build();
        log.info("调用apps注册用户:{}", JSONObject.toJSONString(couponUserDto));
        R<Long> result = appsService.saveCouponUser(couponUserDto);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            log.error("调用apps注册用户失败:{}", JSONObject.toJSONString(result));
            throw new Exception("调用apps注册用户失败");
        }
        Long userId = result.getData();
        MUserEntity userEntity = new MUserEntity();
        BeanUtils.copyProperties(enrollUserDto, userEntity);
        userEntity.setIsDelFlag(false);
        userEntity.setFkUserId(userId);
        userEntity.setNickname(enrollUserDto.getNickName());
        userEntity.setGmtCreate(LocalDateTime.now());
        userEntity.setGmtModified(LocalDateTime.now());
        userEntity.setGmtCreateUser(enrollUserDto.getMobile());
        userEntity.setGmtModifiedUser(enrollUserDto.getMobile());
        mUserMapper.insert(userEntity);

        Map<String, String> map = new HashMap<>();
        map.put("grant_type", "password");
        map.put("username", enrollUserDto.getMobile());
        map.put("scope", "server");
        map.put("password", "password");

        Map<String, Object> headers = new HashMap<>();
        headers.put("loginType", LoginTypeEnum.VCODE.getCode());
        headers.put("formPlatformCode", enrollUserDto.getPlatformCode());
        headers.put("certificate", enrollUserDto.getPlatformCode());
        headers.put("formPlatformId", enrollUserDto.getPlatformId());
        headers.put("Authorization", "Basic dGVzdDp0ZXN0");

        TokenVo tokenVo = remoteAuthService.accountLogin(map, headers);
        log.info("调用oauth2登录接口返回结果:{}", tokenVo);
        return R.restResult(tokenVo, 0, "注册成功");
    }

    @Override
    public R login(EnrollUserDto enrollUserDto) throws Exception {
        QueryWrapper<MUserEntity> mUserEntityQueryWrapper = new QueryWrapper<>();
        mUserEntityQueryWrapper.eq("mobile", enrollUserDto.getMobile());
        MUserEntity mUserEntity = mUserMapper.selectOne(mUserEntityQueryWrapper);
//        if ("9999".equals(enrollUserDto.getCaptcha())) {
        if (mUserEntity == null) {
            return R.restResult(null, 30005, "手机号与验证码不匹配");
        }
//            return R.restResult(mUserEntity, 0, "登录成功");
//        }
        Object codeObj = redisTemplate.opsForValue().get(enrollUserDto.getMobile());
        if (mUserEntity == null) {
            if (codeObj != null && codeObj.toString().equals(enrollUserDto.getCaptcha())) {
                return R.restResult(null, 30005, "手机号与验证码不匹配");
            }
            return R.restResult(null, 30003, "用户不存在，请先注册");
        }
        if (codeObj == null) {
            return R.restResult(null, 30004, "未获取验证码");
        }
        if (!codeObj.toString().equals(enrollUserDto.getCaptcha())) {
            return R.restResult(null, 30001, "验证码错误");
        }
        return R.restResult(mUserEntity, 0, "登录成功");
    }
}
