package com.insurance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.insurance.entity.CreditCard;
import com.insurance.entity.CreditCardStatement;
import com.insurance.entity.InsuranceOrder;
import com.insurance.mapper.AppSystemCenterMapper;
import com.insurance.mapper.CreditCardStatementMapper;
import com.insurance.service.CreditCardStatementService;
import com.insurance.util.ExchangeRateUtils;
import com.insurance.util.TradeNumberGenerator;
import com.insurance.vo.settlement.RateDetail;
import com.insurance.vo.system.SystemConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@Service
@Slf4j
public class CreditCardStatementServiceImpl extends ServiceImpl<CreditCardStatementMapper, CreditCardStatement> implements CreditCardStatementService {

    @Autowired
    private CreditCardStatementMapper creditCardStatementMapper;
    @Autowired
    private ExchangeRateUtils exchangeRateUtils;
    @Autowired
    private TradeNumberGenerator tradeNumberGenerator;
    @Autowired
    private AppSystemCenterMapper systemCenterMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCreditCardStatement(CreditCard creditCard, InsuranceOrder order, Integer businessType, String targetKey, Long targetId, Integer status) {
        SystemConfig systemConfig = systemCenterMapper.selectAxbSystemConfig();
        //人名币计算公式：（澳币金额*（澳币转美金汇率）*1.015*（美金转人民币汇率）*1.015+100）*1.006
        //澳币转美金汇率
        RateDetail audToUsdRate = exchangeRateUtils.getRateDetail("AUD", "USD");
        //美金转人民币汇率
        RateDetail usdToCnyRate = exchangeRateUtils.getRateDetail("USD", "CNY");
        BigDecimal amount = order.getInsuranceAmount().multiply(audToUsdRate.getRate())
                .multiply(systemConfig.getValue1Decimal())
                .multiply(usdToCnyRate.getRate())
                .add(systemConfig.getValue2Decimal());
        BigDecimal cnyAmount = amount.multiply(systemConfig.getValue3Decimal()).setScale(2, RoundingMode.HALF_UP);
        CreditCardStatement cardStatement = CreditCardStatement.builder()
                .fkCreditCardId(creditCard.getId())
                .num(tradeNumberGenerator.generateTradeNumber())
                .businessType(businessType)
                .relationTargetKey(targetKey)
                .relationTargetId(targetId)
                //下单时的币种
                .fkCurrencyTypeNum(order.getFkCurrencyTypeNum())
                //下单时币种金额
                .amount(order.getInsuranceAmount())
                //公式汇率:（澳币转美金汇率）*（美金转人民币汇率）
                .exchangeRateFormula(audToUsdRate.getRate().multiply(usdToCnyRate.getRate()))
                //公式汇率系数：1.015*1.015    -对应system_config的澳小保参数的value1
                .exchangeRateFactorFormula(systemConfig.getValue1Decimal())
                //公式手续费：100  -对应system_config的澳小保参数的value2
                .serviceFeeFormula(systemConfig.getValue2Decimal())
                //公式手续费系数 ：1.006  -对应system_config的澳小保参数的value3
                .serviceFeeFactorFormula(systemConfig.getValue3Decimal())
                //公式字符串：（澳币金额*（澳币转美金汇率）*1.015*（美金转人民币汇率）*1.015+100）*1.006
                .noteFormula("(" + order.getInsuranceAmount()
                        + "*" + audToUsdRate.getRate()
                        + "*" + systemConfig.getValue1Decimal()
                        + "*" + usdToCnyRate.getRate()
                        + "+" + systemConfig.getValue2Decimal() + ")"
                        + "*" + systemConfig.getValue3Decimal())
                //公式金额：微信支付的实际费用（人名币）
                .amountRmbFormula(cnyAmount)
                //exchange_rate_rmb和amount_rmb均是后台录入的
                .status(status)
                .build();
        cardStatement.setGmtCreate(new Date());
        cardStatement.setGmtModified(new Date());
        cardStatement.setGmtCreateUser(order.getGmtCreateUser());
        cardStatement.setGmtModifiedUser(order.getGmtModifiedUser());
        creditCardStatementMapper.insert(cardStatement);
    }
}
