package com.partner.dto.student;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.partner.entity.MAppStudentEntity;
import com.partner.entity.MAppStudentOfferItemEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "添加学生参数")
public class MStudentSubAndAddOrEditDto extends MAppStudentEntity {

    @Schema(description = "学生UUID")
    @NotBlank(message = "学生UUID", groups = {MStudentAddOrEditDto.Update.class})
    private String studentUUID;





    @Schema(description = "学生姓名（中）")
    @NotBlank(message = "学生姓名不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})
    private String name;



    @Schema(description = "名（英/拼音）")
   /* @NotBlank(message = "名（英/拼音）不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})*/
    private String firstName;


    @Schema(description = "性别")
    @NotBlank(message = "性别不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})
    private String gender;

    @Schema(description = "生日")
    @NotNull(message = "生日不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private LocalDate birthday;


    @Schema(description = "学生国籍所在国家Id")
    @NotNull(message = "学生国籍所在国家Id不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})
    private Long fkAreaCountryIdNationality;

    @Schema(description = "手机区号")
    @NotBlank(message = "手机区号不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})
    private String mobileAreaCode;


    @Schema(description = "移动电话")
    @NotBlank(message = "移动电话不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})
    private String mobile;

    @Schema(description = "Email")
    @NotBlank(message = "Email不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})
    private String email;


    @Schema(description = "学历等级类型：高中/大学/本科/研究生/硕士/博士在读/博士/博士后")
   /* @NotBlank(message = "学历等级类型不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})*/
    private String educationLevelType;
    @Schema(description = "毕业院校Id")
    private Long fkInstitutionIdEducation;


    @Schema(description = "学历等级类型（国际）：高中/大学/本科/研究生/硕士/博士在读/博士/博士后")
    /*@NotBlank(message = "学历等级类型（国际）不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})*/
    private String educationLevelType2;
    @Schema(description = "毕业院校名称")
    /*@NotBlank(message = "毕业院校名称不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})*/
    private String fkInstitutionNameEducation;
    @Schema(description = "毕业院校名称（国际）")
    /*@NotBlank(message = "毕业院校名称不能为空", groups = {MStudentAddOrEditDto.Add.class, MStudentAddOrEditDto.Update.class})*/
    private String fkInstitutionNameEducation2;


    @Schema(description = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    private Integer educationProject;

    @Schema(description = "申请计划")
    private List<MAppStudentOfferItemEntity> list;

    @Schema(description = "学生多附件")
    private String[] fileGuidArray;


    public interface Update {
    }

    public interface Add {
    }


}
