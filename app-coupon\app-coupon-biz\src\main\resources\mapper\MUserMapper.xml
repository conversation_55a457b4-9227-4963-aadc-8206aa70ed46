<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.coupon.mapper.MUserMapper">


    <select id="getAreaRegionSelect" resultType="com.coupon.vo.AreaRegionSelectVo">
        SELECT uar.id,concat(uac.name,'【',uac.name_chn,'】','-',uar.name,'（',uar.name_chn,'）') AS name FROM  ais_institution_center.u_area_region AS uar LEFT JOIN  ais_institution_center.u_area_country AS uac
        ON uar.fk_area_country_id = uac.id
        <where>
            <if test="fkCompanyId !=null and fkCompanyId !=''">
                AND uar.fk_company_id = #{fkCompanyId}
            </if>
             AND  uar.num != 'RG000031'
        </where>
        ORDER BY uar.view_order DESC
    </select>
    <select id="getBDByAreaRegion" resultType="com.coupon.vo.BdVo">
        SELECT staffBdCode.bd_code AS bdCode,staffBdCode.fk_staff_id AS staffId,concat(staff.name,'（',staff.name_en,'）') AS staffName
        FROM ais_sale_center.r_staff_bd_code AS staffBdCode INNER JOIN ais_permission_center.m_staff AS staff ON staffBdCode.fk_staff_id = staff.id
        <where>
            <if test="fkAreaRegionId !=null and fkAreaRegionId !=''">
                AND staffBdCode.fk_area_region_id = #{fkAreaRegionId}
            </if>
            AND staff.is_active = 1 AND staff.is_on_duty =1 AND staffBdCode.bd_code not in (0305,0306,0325,0335,1704,1708,1002,2401) AND staffBdCode.bd_code NOT REGEXP '^(O|T|S)'
            AND staff.name NOT REGEXP 'B$'  AND staff.name NOT REGEXP '测试'
        </where>

    </select>

    <select id="checkUserExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM ais_permission_center.m_staff
        <where>
            <if test="userDto.mobile!=null and userDto.mobile !='' ">
                AND mobile = #{userDto.mobile}
            </if>
            AND is_active = 1
        </where>
    </select>

    <select id="getDetail" resultType="com.coupon.vo.UserInfoVo">
        SELECT  mUser.id,
                mUser.name,
               mUser.nickName,
               mUser.gender,
               mUser.mobile,
               mUser.wechat,
               mUser.role,
               mUser.company,
               mUser.mobile_area_code AS mobileAreaCode,
               mUser.bd_code AS bdCode,
               mUser.fk_area_region_ids AS fkAreaRegionIds,
               staff.name AS staffName,
                concat(region.name,'（',region.name_chn,'）') AS fkAreaRegionName
            FROM m_coupon_user AS mUser
                LEFT JOIN  ais_sale_center.r_staff_bd_code AS staffBdCode ON mUser.bd_code = staffBdCode.bd_code
                LEFT JOIN ais_permission_center.m_staff AS staff ON staffBdCode.fk_staff_id = staff.id AND staff.is_active = 1
                LEFT JOIN ais_institution_center.u_area_region AS region  ON mUser.fk_area_region_ids = region.id
        <where>
            <if test="userDto.mobile!=null and userDto.mobile !='' ">
                AND mUser.mobile = #{userDto.mobile}
            </if>
            <if test="userDto.id!=null and userDto.id !=''">
                AND mUser.id = #{userDto.id}
            </if>
        </where>
    </select>


</mapper>