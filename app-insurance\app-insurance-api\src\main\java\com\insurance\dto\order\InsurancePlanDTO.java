package com.insurance.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 保险计划DTO
 *
 * <AUTHOR>
 * @Date 2025/6/13 上午10:41
 * @Version 1.0
 */
@Data
public class InsurancePlanDTO {

    @Schema(description = "开始日期")
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date startStr;

    @Schema(description = "结束日期")
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date endStr;

    @Schema(description = "保险单类型")
    @NotBlank(message = "保险单类型不能为空")
    private String insuranceType;

    @Schema(description = "保险产品类型")
    @NotBlank(message = "保险产品类型不能为空")
    private String productType;

}
