<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fzh.job.mapper.MLiveMapper">


    <select id="selectDetail" resultType="com.partner.entity.MLiveEntity">
        SELECT DISTINCT mLive.* FROM ais_platform_center.m_live mLive
                       INNER JOIN app_partner_center.r_live_partner_user_appointment rLivePartnerUserAppointment ON mLive.id=rLivePartnerUserAppointment.fk_live_id
                       WHERE mLive.fk_live_type_id=2 AND mLive.fk_platform_code='PARTNER'
                       AND mLive.live_time_start BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 15 MINUTE)
                       AND rLivePartnerUserAppointment.status =0
                       ORDER BY mLive.live_time_start

        LIMIT 5;


    </select>
    <select id="selectDetailList" resultType="com.partner.vo.job.UserAppointmentVo">
            SELECT rLivePartnerUserAppointment.*,
                   systemUserPlatformLogin.mini_program_openid AS touser FROM
                app_partner_center.r_live_partner_user_appointment rLivePartnerUserAppointment
                    INNER JOIN app_partner_center.m_partner_user mPartnerUser ON rLivePartnerUserAppointment.fk_partner_user_id=mPartnerUser.id
            INNER JOIN app_system_center.system_user_platform_login systemUserPlatformLogin
                ON mPartnerUser.fk_user_id =systemUserPlatformLogin.fk_user_id
            WHERE  rLivePartnerUserAppointment.fk_live_id=#{liveId} AND rLivePartnerUserAppointment.status=0


    </select>
</mapper>