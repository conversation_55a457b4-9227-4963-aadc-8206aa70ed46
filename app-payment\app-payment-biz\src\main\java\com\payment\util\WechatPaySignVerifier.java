package com.payment.util;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.*;
import java.util.Base64;
import java.io.InputStream;
import java.security.interfaces.RSAPrivateKey;

public class WechatPaySignVerifier {

    /**
     * 生成支付签名 paySign
     * @param privateKey 私钥
     * @param timeStamp 时间戳（秒）
     * @param nonceStr 随机字符串
     * @param prepayId 微信返回的prepay_id
     * @return Base64编码的签名字符串
     */
    public static String generatePaySign(PrivateKey privateKey, String timeStamp, String nonceStr, String prepayId) throws Exception {
        String message = timeStamp + "\n" +
                         nonceStr + "\n" +
                         "prepay_id=" + prepayId + "\n";

        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(message.getBytes(StandardCharsets.UTF_8));
        byte[] signBytes = signature.sign();

        return Base64.getEncoder().encodeToString(signBytes);
    }

    /**
     * 从 PEM 文件中加载 RSA 私钥
     */
    public static PrivateKey loadPrivateKey(InputStream inputStream) throws Exception {
        StringBuilder pem = new StringBuilder();
        try (java.util.Scanner scanner = new java.util.Scanner(inputStream, "UTF-8")) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine();
                if (!line.contains("BEGIN") && !line.contains("END")) {
                    pem.append(line.trim());
                }
            }
        }

        byte[] keyBytes = Base64.getDecoder().decode(pem.toString());
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(keySpec);
    }

    /**
     * 用法示例
     */
    public static void main(String[] args) throws Exception {
        // 加载私钥
        InputStream keyStream = WechatPaySignVerifier.class.getClassLoader().getResourceAsStream("cert/apiclient_key.pem");
        if (keyStream == null) {
            throw new RuntimeException("找不到私钥文件");
        }
        PrivateKey privateKey = loadPrivateKey(keyStream);

        // 示例参数
        String timeStamp = "1752572475";
        String nonceStr = "52fb29d81264419ebefd7152f1f5b2a3";
        String prepayId = "wx15174115785959411450ceada0c4ba0001";

        String paySign = generatePaySign(privateKey, timeStamp, nonceStr, prepayId);
        System.out.println("paySign: " + paySign);
    }
}
