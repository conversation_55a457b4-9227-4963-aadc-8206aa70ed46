package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.MEventParamsDto;
import com.partner.dto.TableNameDto;
import com.partner.entity.EventEntity;
import com.partner.vo.AppointmentInfo;
import com.partner.vo.EventPageVo;
import com.partner.vo.MEventRegistrationAgentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface EventMapper extends BaseMapper<EventEntity> {

    IPage<EventPageVo> getEnvenListPage(Page page, @Param("query") MEventParamsDto params);

    List<AppointmentInfo>  getUserInfo(EventPageVo eventPageVo);

    IPage<MEventRegistrationAgentVo> searchRegistration(Page page, @Param("query") MEventParamsDto params);


    List<Map<String,String>> getTableName(TableNameDto params);
}
