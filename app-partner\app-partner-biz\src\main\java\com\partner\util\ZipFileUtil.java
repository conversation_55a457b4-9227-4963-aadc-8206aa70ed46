package com.partner.util;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.COSObjectInputStream;
import com.qcloud.cos.model.GetObjectRequest;
import com.qcloud.cos.region.Region;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipFileUtil {
    /**
     * 将多个 COSObjectInputStream 打包成 ZIP 文件
     *
     * @param inputStreams 文件流列表（每个流对应一个文件）
     * @param fileNames    文件名列表（与流顺序一致）
     * @param zipFilePath  输出的 ZIP 文件路径（如：/tmp/files.zip）
     * @throws IOException 如果读写失败
     */
    public static void packToZip(
            List<COSObjectInputStream> inputStreams,
            List<String> fileNames,
            String zipFilePath
    ) throws IOException {
        if (inputStreams.size() != fileNames.size()) {
            throw new IllegalArgumentException("文件流数量和文件名数量不匹配！");
        }

        try (FileOutputStream fos = new FileOutputStream(zipFilePath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            for (int i = 0; i < inputStreams.size(); i++) {
                COSObjectInputStream cosStream = inputStreams.get(i);
                String fileName = fileNames.get(i);

                // 创建 ZIP 条目（即 ZIP 里的一个文件）
                ZipEntry zipEntry = new ZipEntry(fileName);
                zos.putNextEntry(zipEntry);

                // 将 COS 文件流写入 ZIP
                IOUtils.copy(cosStream, zos);

                // 关闭当前条目
                zos.closeEntry();
                cosStream.close(); // 关闭 COS 流
            }
        }
    }



}
