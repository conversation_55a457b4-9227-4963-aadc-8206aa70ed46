package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.MInstitutionRankingParamsDto;
import com.partner.entity.MInstitutionRankingEntity;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.CourseTypeGroupCombox;
import com.partner.vo.combox.InstitutionTypeCombox;
import com.partner.vo.combox.RankingTypeCombox;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_institution_ranking】的数据库操作Service
* @createDate 2024-12-23 19:15:17
*/
public interface MInstitutionRankingService extends IService<MInstitutionRankingEntity> {
    List<CountryCombox> getCountryCombox(MInstitutionRankingParamsDto dto);

    List<InstitutionTypeCombox> getInstitutionTypeCombox(MInstitutionRankingParamsDto dto);

    List<CourseTypeGroupCombox> getCourseTypeGroupCombox(MInstitutionRankingParamsDto dto);



    List<RankingTypeCombox> getRankingCombox(MInstitutionRankingParamsDto dto);

    IPage getPageInstitutionRanking(Page page,MInstitutionRankingParamsDto dto);
}
