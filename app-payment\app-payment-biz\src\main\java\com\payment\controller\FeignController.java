package com.payment.controller;

import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.payment.dto.common.PayRequest;
import com.payment.dto.common.RefundRequestDto;
import com.payment.enums.PayTypeEnum;
import com.payment.service.PayService;
import com.payment.service.wx.WxPayRefundService;
import com.payment.service.wx.WxPayService;
import com.payment.vo.PayResponse;
import com.wechat.pay.java.service.refund.model.Refund;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:
 */
@RestController
@Slf4j
@RequestMapping("/feign")
@Tag(description = "openFeign接口管理", name = "openFeign接口管理")
public class FeignController {

    @Autowired
    private PayService payService;
    @Operation(summary = "下单")
    @PostMapping("/order/unifiedOrder")
    @Inner(false)
    public R<PayResponse> unifiedOrder(@RequestBody PayRequest request) {
        return R.ok(payService.unifiedOrder(request));
    }

}
