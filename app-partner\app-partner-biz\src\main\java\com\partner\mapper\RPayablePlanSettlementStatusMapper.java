package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RPayablePlanSettlementStatusEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【r_payable_plan_settlement_status】的数据库操作Mapper
* @createDate 2025-01-10 17:13:06
* @Entity com.partner.entity.RPayablePlanSettlementStatus
*/
@Mapper
@DS("financedb")
public interface RPayablePlanSettlementStatusMapper extends BaseMapper<RPayablePlanSettlementStatusEntity> {



}




