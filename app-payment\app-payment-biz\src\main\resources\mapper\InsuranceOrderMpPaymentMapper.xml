<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.payment.mapper.InsuranceOrderMpPaymentMapper">
    <update id="updateInsuranceOrderIdByOrderNo">
        update m_insurance_order
        set mp_payment_status = #{mpPaymentStatus}
        where order_num = #{orderNo}
    </update>


    <select id="selectInsuranceOrderIdByOrderNo" resultType="java.lang.Long">
        select id
        from m_insurance_order
        where order_num = #{orderNo}
        limit 1
    </select>

</mapper>