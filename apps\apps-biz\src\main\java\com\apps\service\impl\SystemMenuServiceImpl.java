package com.apps.service.impl;

import com.apps.api.entity.SystemMenuEntity;
import com.apps.api.entity.SystemRoleMenuEntity;
import com.apps.api.entity.SystemUserMenuEntity;
import com.apps.api.vo.system.MenuTreeVo;
import com.apps.mapper.SystemMenuMapper;
import com.apps.mapper.SystemRoleMenuMapper;
import com.apps.mapper.SystemUserMenuMapper;
import com.apps.service.SystemMenuService;
import com.apps.service.SystemUserMenuService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.constant.CommonConstants;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SystemMenuServiceImpl extends ServiceImpl<SystemMenuMapper, SystemMenuEntity> implements SystemMenuService {

    private final SystemMenuMapper systemMenuMapper;
    private final SystemRoleMenuMapper systemRoleMenuMapper;
    private final SystemUserMenuService systemUserMenuService;
    private final SystemUserMenuMapper systemUserMenuMapper;

    @Override
    public List<MenuTreeVo> getMenuTree(List<Long> menuIdList) {
        return buildMenuTree(systemMenuMapper.selectBatchIds(menuIdList.stream()
                .distinct()
                .collect(Collectors.toList())));
    }

    @Override
    public void saveMenu(SystemMenuEntity menu) {
        if (Objects.isNull(menu.getId())) {
            SystemMenuEntity systemMenuEntity = new SystemMenuEntity();
            BeanUtils.copyProperties(menu, systemMenuEntity);
            systemMenuEntity.setGmtCreate(LocalDateTime.now());
            systemMenuEntity.setGmtModified(LocalDateTime.now());
            systemMenuEntity.setIsDelFlag(Integer.parseInt(CommonConstants.STATUS_NORMAL));
            systemMenuMapper.insert(systemMenuEntity);
            return;
        }
        SystemMenuEntity systemMenuEntity = systemMenuMapper.selectById(menu.getId());
        BeanUtils.copyProperties(menu, systemMenuEntity);
        systemMenuEntity.setGmtModified(LocalDateTime.now());
        systemMenuMapper.updateById(systemMenuEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMenu(List<Long> menuId) {
        List<SystemMenuEntity> systemMenuEntities = systemMenuMapper.selectBatchIds(menuId);
        for (SystemMenuEntity menuEntity : systemMenuEntities) {
            menuEntity.setIsDelFlag(Integer.parseInt(CommonConstants.STATUS_DEL));
            menuEntity.setGmtModified(LocalDateTime.now());
        }
        this.updateBatchById(systemMenuEntities);
        //删除角色权限
        List<Long> sysRoleIds = systemRoleMenuMapper.selectList(new LambdaQueryWrapper<SystemRoleMenuEntity>()
                .in(SystemRoleMenuEntity::getFkMenuId, menuId)).stream().map(SystemRoleMenuEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sysRoleIds)) {
            systemRoleMenuMapper.deleteByIds(sysRoleIds);
        }
        //删除用户权限
        List<Long> userMenuIds = systemUserMenuMapper.selectList(new LambdaQueryWrapper<SystemUserMenuEntity>()
                .in(SystemUserMenuEntity::getFkMenuId, menuId)).stream().map(SystemUserMenuEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userMenuIds)) {
            systemUserMenuService.removeByIds(userMenuIds);
        }
    }

    /**
     * 构建菜单树形结构
     *
     * @param menus
     * @return
     */
    private List<MenuTreeVo> buildMenuTree(List<SystemMenuEntity> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return Collections.emptyList();
        }
        // 先将菜单转换成 MenuTreeVo 对象
        List<MenuTreeVo> menuTreeVos = menus.stream()
                .map(menu -> {
                    MenuTreeVo treeVo = new MenuTreeVo();
                    BeanUtils.copyProperties(menu, treeVo);
                    return treeVo;
                }).collect(Collectors.toList());

        // 构建树形结构
        Map<Long, MenuTreeVo> menuMap = menuTreeVos.stream()
                .collect(Collectors.toMap(MenuTreeVo::getId, menu -> menu));
        // 根菜单列表
        List<MenuTreeVo> rootMenuList = new ArrayList<>();
        for (MenuTreeVo menu : menuTreeVos) {
            // 如果没有父菜单ID，认为是根菜单
            if (Objects.isNull(menu.getFkParentMenuId()) || menu.getFkParentMenuId().equals(0L)) {
                rootMenuList.add(menu);
            } else {
                MenuTreeVo parentMenu = menuMap.get(menu.getFkParentMenuId());
                if (Objects.nonNull(parentMenu)) {
                    if (Objects.isNull(parentMenu.getChildTree())) {
                        parentMenu.setChildTree(new ArrayList<>());
                    }
                    parentMenu.getChildTree().add(menu);
                }
            }
        }

        return rootMenuList;
    }
}
