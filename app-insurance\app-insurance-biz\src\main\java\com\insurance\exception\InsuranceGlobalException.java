package com.insurance.exception;

import com.apps.api.enums.GlobExceptionEnum;

/**
 * <AUTHOR>
 */
public class InsuranceGlobalException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private int errorCode;
    private String errorMessage;


    public InsuranceGlobalException(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public InsuranceGlobalException(GlobExceptionEnum codeEnum) {
        this.errorCode = codeEnum.getCode();
        this.errorMessage = codeEnum.getMsg();
    }

    public InsuranceGlobalException(String errorMessage) {
        this.errorCode = 500;
        this.errorMessage = errorMessage;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

}
