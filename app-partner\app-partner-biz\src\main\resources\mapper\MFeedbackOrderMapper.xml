<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MFeedbackOrderMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MFeedbackOrderEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
            <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
            <result property="fkPlatformCreateUserId" column="fk_platform_create_user_id" jdbcType="BIGINT"/>
            <result property="fkResourceKey" column="fk_resource_key" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="fkFeedbackOrderTypeId" column="fk_feedback_order_type_id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="message" column="message" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="closeTime" column="close_time" jdbcType="TIMESTAMP"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectListPage" resultType="com.partner.vo.work.MFeedbackOrderVo">
        SELECT mFeedbackOrder.*,
               uFeedbackOrderType.type_name
               FROM ais_platform_center.m_feedback_order mFeedbackOrder
        LEFT JOIN ais_platform_center.u_feedback_order_type uFeedbackOrderType ON mFeedbackOrder.fk_feedback_order_type_id=uFeedbackOrderType.id

        WHERE mFeedbackOrder.fk_platform_code='PARTNER' AND mFeedbackOrder.fk_platform_create_user_id=#{query.partnerUserId}

        <if test="query.status!=null" >
            AND mFeedbackOrder.status = #{query.status}
        </if>
        <if test="query.orderTimeType!=null and query.orderTimeType==0 " >
            AND mFeedbackOrder.gmt_create   >=  DATE_ADD(NOW(), INTERVAL -3 DAY )
        </if>

        <if test="query.orderTimeType!=null and query.orderTimeType==1" >
            AND mFeedbackOrder.gmt_create   >=  DATE_ADD(NOW(), INTERVAL -7 DAY )
        </if>

        <if test="query.orderTimeType!=null and query.orderTimeType==2" >
            AND mFeedbackOrder.gmt_create   >=  DATE_ADD(NOW(), INTERVAL -1 MONTH )
        </if>
        <if test="query.orderTimeType!=null and query.orderTimeType==3" >
            AND mFeedbackOrder.gmt_create   >=  DATE_ADD(NOW(), INTERVAL -3 MONTH )
        </if>
        <if test="query.orderTimeType!=null and query.orderTimeType==4" >
            AND mFeedbackOrder.gmt_create   >=  DATE_ADD(NOW(), INTERVAL -6 MONTH )
        </if>
        <if test="query.orderTimeType!=null and query.orderTimeType==5" >
            AND mFeedbackOrder.gmt_create   >=  DATE_ADD(NOW(), INTERVAL -1 YEAR )
        </if>
    </select>
    <select id="getMFeedbackDetail" resultType="com.partner.vo.work.MFeedbackOrderDetailVo">
        SELECT mFeedbackOrder.*,
               uFeedbackOrderType.type_name
        FROM ais_platform_center.m_feedback_order mFeedbackOrder
                 LEFT JOIN ais_platform_center.u_feedback_order_type uFeedbackOrderType ON mFeedbackOrder.fk_feedback_order_type_id=uFeedbackOrderType.id
        WHERE mFeedbackOrder.id=#{id}


    </select>
    <select id="getUFeedbackOrderType" resultType="com.partner.entity.UFeedbackOrderTypeEntity">
        SELECT uFeedbackOrderType.* from ais_platform_center.u_feedback_order_type uFeedbackOrderType WHERE uFeedbackOrderType.fk_platform_code='PARTNER'
            ORDER BY uFeedbackOrderType.view_order
    </select>
</mapper>
