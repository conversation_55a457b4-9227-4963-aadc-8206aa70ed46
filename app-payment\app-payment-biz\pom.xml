<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 fzh Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fzh</groupId>
        <artifactId>app-payment</artifactId>
        <version>3.8.1</version>
    </parent>

    <artifactId>app-payment-biz</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>app-payment-api</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-datasource</artifactId>
            <version>3.8.2</version>
        </dependency>
        <!--文件管理-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-oss</artifactId>
            <version>3.8.2</version>
        </dependency>
        <!--feign 调用-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-security</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>
        <!-- orm 模块-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- 阿里云短信下发 -->
        <dependency>
            <groupId>io.springboot.sms</groupId>
            <artifactId>aliyun-sms-spring-boot-starter</artifactId>
        </dependency>
        <!--xss 过滤-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-xss</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--rocketmq-->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.1</version>
        </dependency>
        <!--PDF-->
        <dependency>
        <groupId>com.itextpdf</groupId>
        <artifactId>itextpdf</artifactId>
        <version>5.5.13.2</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-transcoder</artifactId>
            <version>1.14</version>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-codec</artifactId>
            <version>1.14</version>
        </dependency>

        <!-- QCloudCOS -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.24</version>
        </dependency>

        <!--微信支付-->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>0.2.17</version>
        </dependency>
        <!-- 微信支付中用到的工具包 -->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java-core</artifactId>
            <version>0.2.17</version>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>apps-api</artifactId>
            <version>3.8.1</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>boot</id>
        </profile>
        <profile>
            <id>cloud</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xls</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>

                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>

                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.payment.PaymentApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
