package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.MReleaseInfoItemParamsDto;
import com.partner.entity.MReleaseInfoItemEntity;
import com.partner.vo.MReleaseInfoItemVo;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface MReleaseInfoItemService extends IService<MReleaseInfoItemEntity> {

    /**
     * 根据发版信息ID获取子项列表（带权限过滤）
     * <p>
     * 查询逻辑：
     * 1. permission_type = 0（全局）：直接返回
     * 2. permission_type = 1（角色权限）：检查fk_resource_keys中的权限是否在当前用户权限列表中
     * </p>
     *
     * @param releaseInfoId 发版信息ID
     * @return 过滤后的发版信息子项列表
     */
    List<MReleaseInfoItemVo> findByReleaseInfoIdWithPermission(Long releaseInfoId);

}
 