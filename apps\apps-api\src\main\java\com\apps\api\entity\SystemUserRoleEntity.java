package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_user_role")
@Schema(description = "用户角色")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SystemUserRoleEntity extends Model<SystemUserRoleEntity> {
    private static final long serialVersionUID = 1L;


    /**
     * 用户和角色关系Id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="用户和角色关系Id")
    private Long id;

    /**
     * 租户Id
     */
    @Schema(description="租户Id")
    private Long fkTenantId;

    /**
     * 用户Id
     */
    @Schema(description="用户Id")
    private Long fkUserId;

    /**
     * 角色Id
     */
    @Schema(description="角色Id")
    private Long fkRoleId;

    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}