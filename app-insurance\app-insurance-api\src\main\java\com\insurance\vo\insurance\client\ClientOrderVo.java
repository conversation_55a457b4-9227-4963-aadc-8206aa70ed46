package com.insurance.vo.insurance.client;

import com.insurance.entity.InsuranceOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:
 */
@Data
public class ClientOrderVo extends InsuranceOrder {

    @Schema(description = "可结佣金币种")
    private String settlementCurrencyTypeNum;

    @Schema(description = "计算比例")
    private BigDecimal rate;

    @Schema(description = "可结佣金金额")
    private BigDecimal settlementAmount;

    @Schema(description = "应付计划Id")
    private Long payablePlanId;
}
