package com.insurance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.insurance.entity.CreditCardReminder;
import com.insurance.entity.CreditCardReminderNotifier;
import com.insurance.mapper.CreditCardReminderMapper;
import com.insurance.mapper.CreditCardReminderNotifierMapper;
import com.insurance.service.CreditCardReminderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class CreditCardReminderServiceImpl extends ServiceImpl<CreditCardReminderMapper, CreditCardReminder> implements CreditCardReminderService {

    @Autowired
    private CreditCardReminderMapper creditCardReminderMapper;
    @Autowired
    private CreditCardReminderNotifierMapper creditCardReminderNotifierMapper;

    @Override
    public CreditCardReminder getCreditCardReminderByCreditCard(Long creditCardId) {
        CreditCardReminder creditCardReminder = creditCardReminderMapper.selectOne(new LambdaQueryWrapper<CreditCardReminder>()
                .eq(CreditCardReminder::getFkCreditCardId, creditCardId)
                .orderByDesc(CreditCardReminder::getId), false);
        if (Objects.nonNull(creditCardReminder)){
            List<CreditCardReminderNotifier> creditCardReminderNotifiers = creditCardReminderNotifierMapper.selectList(new LambdaQueryWrapper<CreditCardReminderNotifier>()
                    .eq(CreditCardReminderNotifier::getFkCreditCardReminderId, creditCardReminder.getId()));
            creditCardReminder.setCreditCardReminderNotifierList(creditCardReminderNotifiers);
        }
        return creditCardReminder;
    }
}
