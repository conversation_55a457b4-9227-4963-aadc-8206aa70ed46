package com.apps.util;

/**
 * @Author:<PERSON>
 * @Date: 2025/1/22  09:54
 * @Version 1.0
 * code生成器工具类
 */
public class CodeGeneratorUtil {

    /**
     * 根据平台ID和用户ID生成用户编号
     *
     * @param platformId 平台ID
     * @param userId     用户ID
     * @return 生成的用户编号
     */
    public static String generateUserCode(Long platformId, Long userId) {
        // 格式化平台ID为三位，不足补0
        String formattedPlatformId = String.format("%03d", platformId);
        // 格式化用户ID为八位，不足补0
        String formattedUserId = String.format("%08d", userId);
        // 拼接生成用户编号
        return formattedPlatformId + formattedUserId;
    }

}
