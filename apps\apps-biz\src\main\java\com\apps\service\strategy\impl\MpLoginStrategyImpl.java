package com.apps.service.strategy.impl;

import com.apps.api.dto.AppLoginDto;
import com.apps.api.vo.system.UserPermissionVo;
import com.apps.service.strategy.AppLoginVerifyStrategy;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("MP")
@Slf4j
@AllArgsConstructor
public class MpLoginStrategyImpl implements AppLoginVerifyStrategy {

//    @Autowired
//    private AppConfig appConfig;
//    private final SystemUserPlatformLoginService userPlatformLoginService;
//    private final SystemUserService systemUserService;
//    private final TokenService tokenService;
    @Override
    public UserPermissionVo appLoginVerify(AppLoginDto loginDto){

        //查询对应的app secret
//        AppCredential credential = appConfig.getCredential(loginDto.getAppId());
//        if (Objects.isNull(credential)) {
//            log.error("未找到对应的appSecret,appId:{}", loginDto.getAppId());
//            throw new AppsGlobalException(GlobExceptionEnum.MP_SECRET_NOT_EXISTS);
//        }
//
//        log.info("微信登录参数:{}", loginDto);
//        Map<String, String> urlParamsMap = new HashMap<>();
//        urlParamsMap.put("appid", loginDto.getAppId());
//        urlParamsMap.put("secret", credential.getAppSecret());
//        urlParamsMap.put("js_code", loginDto.getCertificate());
//        urlParamsMap.put("grant_type", "authorization_code");
//
//        WxLoginVo wxLoginVo = WxUtils.doRequest(
//                WxConstant.WX_LOGIN_URL,
//                urlParamsMap,
//                new TypeReference<WxLoginVo>() {
//                }
//        );
//        log.info("微信登录返回结果:{}", JSONObject.toJSONString(wxLoginVo));
//        if (Objects.isNull(wxLoginVo) || Objects.isNull(wxLoginVo.getOpenid())) {
//            log.error("微信登录失败,返回结果:{}", wxLoginVo);
//            throw new AppsGlobalException(GlobExceptionEnum.MP_LOGIN_ERROR);
//        }
//        SystemUserPlatformLoginEntity platformLoginUser = userPlatformLoginService.getUserByOpenId(loginDto.getFormPlatformId(), loginDto.getFormPlatformCode(), wxLoginVo.getOpenid());
//        if (Objects.nonNull(platformLoginUser)) {
//            log.info("用户已存在,用户信息:{}", platformLoginUser);
//            systemUserService.checkLoginUser(platformLoginUser.getFkUserId());
//        }
//        if (Objects.isNull(platformLoginUser)) {
//            SaveUserDto saveUserDto = new SaveUserDto();
//            //解密用户信息
//            if (StringUtils.isNoneBlank(loginDto.getEncryptedData()) && StringUtils.isNoneBlank(loginDto.getIv())) {
//                JSONObject userInfo = WxUtils.getUserInfo(wxLoginVo.getSessionKey(), loginDto.getEncryptedData(), loginDto.getIv());
//                log.info("解密用户信息:{}", userInfo);
//                if (Objects.isNull(userInfo)) {
//                    log.error("解密用户信息失败");
//                    throw new AppsGlobalException(GlobExceptionEnum.MP_USER_DECRYPT_ERROR);
//                }
//                saveUserDto.setWechatNickname(userInfo.containsKey("nickName") ? userInfo.getString("nickName") : null);
//                saveUserDto.setWechatIconUrl(userInfo.containsKey("avatarUrl") ? userInfo.getString("avatarUrl") : null);
//                saveUserDto.setGender(userInfo.containsKey("gender") ? Integer.parseInt(userInfo.getString("gender")) : null);
//            }
//            saveUserDto.setFkFromPlatformId(loginDto.getFormPlatformId());
//            saveUserDto.setFkFromPlatformCode(loginDto.getFormPlatformCode());
//            Long userId = systemUserService.saveUser(saveUserDto);
//            if (userId < 1L) {
//                log.error("创建用户失败，用户信息dto:{}",JSONObject.toJSONString(saveUserDto));
//                throw new AppsGlobalException(GlobExceptionEnum.SYS_CREATE_USER_ERROR);
//            }
//            //创建用户登录记录
//            SaveUserPlatformLoginDto platformLoginDto = new SaveUserPlatformLoginDto();
//            platformLoginDto.setFkPlatformId(loginDto.getFormPlatformId());
//            platformLoginDto.setFkPlatformCode(loginDto.getFormPlatformCode());
//            platformLoginDto.setFkUserId(userId);
//            platformLoginDto.setMiniProgramOpenid(wxLoginVo.getOpenid());
//            platformLoginDto.setMiniProgramUid(wxLoginVo.getUnionid());
//            Boolean saved = userPlatformLoginService.saveUserPlatformLogin(platformLoginDto);
//            if (!saved) {
//                log.error("创建用户登录信息失败，用户登录信息dto:{}",JSONObject.toJSONString(platformLoginDto));
//                throw new AppsGlobalException(GlobExceptionEnum.SYS_CREATE_USER_LOGIN_INFO_ERROR);
//            }
//            return tokenService.createToken(null,userId);
//        }
//        return tokenService.createToken(null,platformLoginUser.getFkUserId());
        return null;
    }
}
