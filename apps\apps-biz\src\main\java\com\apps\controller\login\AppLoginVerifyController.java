/*
 *
 *      Copyright (c) 2018-2025, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the fzh developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 *
 */

package com.apps.controller.login;


import com.apps.api.dto.AppLoginDto;
import com.apps.api.dto.VerifyUserDto;
import com.apps.api.vo.system.UserPermissionVo;
import com.apps.service.SystemUserService;
import com.apps.service.factory.AppLoginVerifyStrategyFactory;
import com.apps.service.logic.SmsService;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-05-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/verify")
@Inner(value = false)
@Tag(description = "apps登录校验模块", name = "apps登录校验模块")
public class AppLoginVerifyController {

    private final AppLoginVerifyStrategyFactory verifyStrategyFactory;
    private final SmsService smsService;
    private final SystemUserService systemUserService;

    @Operation(summary = "用户校验", description = "用户校验")
    @Inner(value = false)
    @PostMapping("/verifyLoginUser")
    public R<UserPermissionVo> verifyLoginUser(@RequestBody @Valid AppLoginDto loginDto) {
        return R.ok(verifyStrategyFactory.getLoginVerifyStrategy(loginDto.getLoginType()).appLoginVerify(loginDto));
    }

    @Operation(summary = "发送验证码", description = "发送验证码")
    @Inner(value = false)
    @PostMapping("/send/smsCode")
    public R<String> sendSmsCode(String mobile) {
        smsService.sendSmsCode(mobile);
        return R.ok("发送成功");
    }

    @Operation(summary = "校验用户是否存在", description = "校验用户是否存在")
    @Inner(value = false)
    @GetMapping("/verifyUserSmsCode")
    public R<Integer> verifyUser(String mobile, String platformCode, Long platformId, String captcha) {
        return R.ok(systemUserService.verifyUserSmsCode(mobile, platformCode, platformId, captcha));
    }

    @Operation(summary = "用户登录前校验", description = "用户登录前校验,返回true表示当前用户可以登录,不能登录会返回相关信息")
    @Inner(value = false)
    @PostMapping("/verifyUserBeforeLogin")
    public R<Boolean> verifyUserBeforeLogin(@RequestBody @Valid VerifyUserDto verifyUserDto) {
        return R.ok(systemUserService.verifyUserBeforeLogin(verifyUserDto));
    }

}
