package com.payment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:支付响应
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayResponse {

    /**
     * 业务是否成功（下单是否成功，不等于支付成功）
     */
    @Schema(description = "业务是否成功")
    private boolean success;

    /**
     * 返回码说明（如下单失败原因）
     */
    @Schema(description = "返回码说明")
    private String code;

    /**
     * 提示信息或异常信息
     */
    @Schema(description = "提示信息或异常信息")
    private String message;

    /**
     * 支付平台（如：WECHAT_MP、ALIPAY、CORP_WECHAT）
     */
    @Schema(description = "支付类型")
    private String payType;

    /**
     * 原始支付平台订单号（如：微信 prepay_id、支付宝 trade_no）
     */
    @Schema(description = "原始支付平台订单号")
    private String orderNo;

    /**
     * 三方 SDK 所需参数（结构化字段，避免 payInfo 是个 JSON 字符串）
     */
    @Schema(description = "三方 SDK 所需参数")
    private Map<String, String> credential;

    /**
     * 拓展字段，如二维码链接、H5支付链接等
     */
    @Schema(description = "拓展字段")
    private Map<String, String> extra;


    /**
     * 失败
     *
     * @param code
     * @param message
     * @return
     */
    public static PayResponse fail(String code, String message) {
        return PayResponse.builder()
                .success(false)
                .code(code)
                .message(message)
                .build();
    }


    /**
     * 成功
     *
     * @param type
     * @param orderNo
     * @param credential
     * @return
     */
    public static PayResponse success(String type, String orderNo, Map<String, String> credential) {
        return PayResponse.builder()
                .success(true)
                .code("SUCCESS")
                .message("下单成功")
                .payType(type)
                .orderNo(orderNo)
                .credential(credential)
                .build();
    }
}
