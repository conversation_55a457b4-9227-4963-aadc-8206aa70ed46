package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 支付类型枚举类
 */

@Getter
@AllArgsConstructor
public enum PaymentTypeEnum {

    CREDIT_CARD(1, "信用卡支付"),
    WX(2, "微信支付"),
    ;


    private Integer code;

    private String msg;


    public static PaymentTypeEnum getEnumByCode(Integer code) {
        for (PaymentTypeEnum value : PaymentTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
