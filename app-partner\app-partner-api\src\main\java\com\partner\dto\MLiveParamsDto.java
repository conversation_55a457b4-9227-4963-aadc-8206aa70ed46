package com.partner.dto;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;


@Data
@Schema(description = "直播")
public class MLiveParamsDto extends UserInfoParams {

    @Schema(description = "直播列表 0最新直播/1往期直播")
    @NotNull(message = "直播列表 liveType 类型不能为空")
    private Integer liveType;
    @Schema(description = "直播日期")
    private LocalDate liveDate;


    @Schema(description = "后端使用(前端不需要处理)")
    private String mMageAddress;

    @Schema(description = "登录接口来源：0未登录 1已登录")
    private String loginType;

    @Schema(description = "关键词")
    private String title;

}
