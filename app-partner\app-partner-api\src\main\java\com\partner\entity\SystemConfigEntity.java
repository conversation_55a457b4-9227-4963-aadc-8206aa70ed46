package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-23 17:21:48
 */

@Data
@TableName("system_config")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" system_config ")
public class SystemConfigEntity extends Model<SystemConfigEntity>{

  @Schema(description = "系统参数Id")
  private Long id;
 

  @Schema(description = "平台应用Id")
  private Long fkPlatformId;
 

  @Schema(description = "平台应用CODE")
  private String fkPlatformCode;
 

  @Schema(description = "配置分类")
  private String configGroup;
 

  @Schema(description = "配置主键（唯一）")
  private String configKey;
 

  @Schema(description = "描述")
  private String description;
 

  @Schema(description = "数值一")
  private String value1;
 

  @Schema(description = "数值二")
  private String value2;
 

  @Schema(description = "数值三")
  private String value3;
 

  @Schema(description = "数值四")
  private String value4;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
