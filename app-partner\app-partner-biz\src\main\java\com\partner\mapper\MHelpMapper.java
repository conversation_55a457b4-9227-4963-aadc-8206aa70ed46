package com.partner.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.MHelpEntity;
import com.partner.vo.HelpVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_help】的数据库操作Mapper
* @createDate 2025-04-30 10:08:14
* @Entity com.partner.entity.MHelp
*/
@Mapper
@DS("aishelpdb")
public interface MHelpMapper extends BaseMapper<MHelpEntity> {

    List<HelpVo> getDetail();


}




