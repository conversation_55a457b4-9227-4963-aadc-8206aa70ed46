server:
  port: 6012

spring:
  application:
    name: apps-biz
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:************}:${NACOS_PORT:8852}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml

#配置日志
logging:
  config: classpath:log/<EMAIL>@.xml

rocketmq:
  name-server: ************:9876
  producer:
    group: user_offline_topic
    send-message-timeout: 3000
    retry-times-when-send-failed: 3