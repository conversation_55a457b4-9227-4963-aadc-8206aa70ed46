<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MAgentMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MAgentEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkParentAgentId" column="fk_parent_agent_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkAreaStateId" column="fk_area_state_id" jdbcType="BIGINT"/>
            <result property="fkAreaCityId" column="fk_area_city_id" jdbcType="BIGINT"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameNote" column="name_note" jdbcType="VARCHAR"/>
            <result property="personalName" column="personal_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
            <result property="nature" column="nature" jdbcType="VARCHAR"/>
            <result property="natureNote" column="nature_note" jdbcType="VARCHAR"/>
            <result property="legalPerson" column="legal_person" jdbcType="VARCHAR"/>
            <result property="taxCode" column="tax_code" jdbcType="VARCHAR"/>
            <result property="idCardNum" column="id_card_num" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="invitationCode" column="invitation_code" jdbcType="VARCHAR"/>
            <result property="isSettlementPort" column="is_settlement_port" jdbcType="BIT"/>
            <result property="isKeyAgent" column="is_key_agent" jdbcType="BIT"/>
            <result property="keyAgentFailureTime" column="key_agent_failure_time" jdbcType="TIMESTAMP"/>
            <result property="isRejectEmail" column="is_reject_email" jdbcType="BIT"/>
            <result property="isCustomerChannel" column="is_customer_channel" jdbcType="BIT"/>
            <result property="isActive" column="is_active" jdbcType="BIT"/>
            <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
            <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
