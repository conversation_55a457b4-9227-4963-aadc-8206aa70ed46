package com.apps.config;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * @Author:Oliver
 * @Date: 2025/1/15  17:22
 * @Version 1.0
 */
@Configuration
public class NacosConfig {

    @Value("${spring.cloud.nacos.discovery.server-addr}")
    private String serverAddr;

    @Value("${spring.cloud.nacos.password}")
    private String password;

    @Value("${spring.cloud.nacos.username}")
    private String username;

    @Bean
    public NamingService namingService() throws NacosException {
        Properties properties = new Properties();

        properties.put("serverAddr", serverAddr);

        // 配置 Nacos 用户名和密码（如果启用了认证）
        properties.put("username", username);
        properties.put("password", password);
        return NacosFactory.createNamingService(properties);
    }
}
