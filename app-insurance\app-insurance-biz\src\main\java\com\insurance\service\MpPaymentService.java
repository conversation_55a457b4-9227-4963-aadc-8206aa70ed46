package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payment.entity.InsuranceOrderMpPayment;

/**
 * @Author:Oliver
 * @Date: 2025/7/17
 * @Version 1.0
 * @apiNote:
 */
public interface MpPaymentService extends IService<InsuranceOrderMpPayment> {

    /**
     * 创建微信支付订单
     *
     * @param mpPayment
     */
    void createMpPayment(InsuranceOrderMpPayment mpPayment);


    /**
     * 更新微信支付订单
     *
     * @param mpPayment
     */
    void updateMpPayment(InsuranceOrderMpPayment mpPayment);
}
