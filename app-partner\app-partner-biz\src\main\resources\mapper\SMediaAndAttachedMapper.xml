<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.SMediaAndAttachedMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.SMediaAndAttachedEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkFileGuid" column="fk_file_guid" jdbcType="VARCHAR"/>
            <result property="fkTableName" column="fk_table_name" jdbcType="VARCHAR"/>
            <result property="fkTableId" column="fk_table_id" jdbcType="BIGINT"/>
            <result property="typeKey" column="type_key" jdbcType="VARCHAR"/>
            <result property="indexKey" column="index_key" jdbcType="INTEGER"/>
            <result property="link" column="link" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_file_guid,fk_table_name,
        fk_table_id,type_key,index_key,
        link,remark,gmt_create,
        gmt_create_user,gmt_modified,gmt_modified_user
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.partner.entity.SMediaAndAttachedEntity" useGeneratedKeys="true">
        insert into ais_sale_center.s_media_and_attached
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fkFileGuid != null">fk_file_guid,</if>
            <if test="fkTableName != null">fk_table_name,</if>
            <if test="fkTableId != null">fk_table_id,</if>
            <if test="typeKey != null">type_key,</if>
            <if test="indexKey != null">index_key,</if>
            <if test="link != null">link,</if>
            <if test="remark != null">remark,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtCreateUser != null">gmt_create_user,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="gmtModifiedUser != null">gmt_modified_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="fkFileGuid != null">#{fkFileGuid,jdbcType=VARCHAR},</if>
            <if test="fkTableName != null">#{fkTableName,jdbcType=VARCHAR},</if>
            <if test="fkTableId != null">#{fkTableId,jdbcType=BIGINT},</if>
            <if test="typeKey != null">#{typeKey,jdbcType=VARCHAR},</if>
            <if test="indexKey != null">#{indexKey,jdbcType=INTEGER},</if>
            <if test="link != null">#{link,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="gmtCreate != null">#{gmtCreate,jdbcType=TIMESTAMP},</if>
            <if test="gmtCreateUser != null">#{gmtCreateUser,jdbcType=VARCHAR},</if>
            <if test="gmtModified != null">#{gmtModified,jdbcType=TIMESTAMP},</if>
            <if test="gmtModifiedUser != null">#{gmtModifiedUser,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <select id="selectInstitutionFile" resultType="com.partner.vo.SMediaAndAttachedVo">
        SELECT
            sAttached.*,
            institutionFile.file_key,
            institutionFile.file_name_orc
        FROM ais_file_center.m_file_institution institutionFile
                 INNER JOIN ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name = #{fkTableName}   AND sAttached.type_key = #{typeKey} AND  sAttached.fk_table_id=#{fkTableId}
        LIMIT 100
    </select>
    <select id="selectPublicFileArrays" resultType="com.partner.vo.FileArray">
        SELECT
            mFilePartner.id,
            mFilePartner.file_guid AS fileGuid,
            CONCAT(#{mMageAddress}, mFilePartner.file_key) AS fileKey,
            mFilePartner.file_name_orc
        from app_partner_center.s_media_and_attached sAttached
                 INNER JOIN app_file_center.m_file_partner mFilePartner ON mFilePartner.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name=#{fkTableName}
          AND  sAttached.type_key=#{typeKey}
          AND  sAttached.fk_table_id=#{fkTableId} ORDER BY sAttached.gmt_create
        LIMIT 100
    </select>
    <select id="selectPublicFileOne" resultType="com.partner.vo.FileArray">
        SELECT
            mFilePartner.id,
            mFilePartner.file_guid AS fileGuid,
            CONCAT(#{mMageAddress}, mFilePartner.file_key) AS fileKey,
            mFilePartner.file_name_orc
        from app_partner_center.s_media_and_attached sAttached
                 INNER JOIN app_file_center.m_file_partner mFilePartner ON mFilePartner.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name=#{fkTableName}
          AND  sAttached.type_key=#{typeKey}
          AND  sAttached.fk_table_id=#{fkTableId} ORDER BY sAttached.gmt_create DESC
        LIMIT 1
    </select>
    <select id="selectSaleOne" resultType="com.partner.entity.SMediaAndAttachedEntity">
        SELECT sMediaAndAttached.* from ais_sale_center.s_media_and_attached sMediaAndAttached
        WHERE  sMediaAndAttached.fk_table_name=#{fkTableName}
          AND  sMediaAndAttached.type_key=#{typeKey}
          AND  sMediaAndAttached.fk_table_id=#{fkTableId} ORDER BY sMediaAndAttached.gmt_create DESC
        LIMIT 1

    </select>

    <select id="selectSaleCenterAttach" resultType="com.partner.entity.SMediaAndAttachedEntity">
        select *
        from ais_sale_center.s_media_and_attached
        where fk_table_name = #{tableName}
          and fk_table_id = #{tableId}
          and type_key = #{typeKey}
    </select>

    <select id="selectSaleCenterAttachAndFile" resultType="com.partner.vo.FileArray">
        select fs.id            as id,
               fs.file_guid      as fileGuid,
               fs.file_key      as fileKey,
               fs.file_name_orc as fileNameOrc
        from ais_sale_center.s_media_and_attached a
                 inner join ais_file_center.m_file_sale fs on fs.file_guid = a.fk_file_guid
        where a.fk_table_name = #{tableName}
          and a.fk_table_id = #{tableId}
          and a.type_key = #{typeKey}
    </select>

    <delete id="deleteSaleCenterAttach">
        delete from ais_sale_center.s_media_and_attached where fk_file_guid in
        <foreach item="fileGuid" collection="fileGuids" separator="," close=")" open="(" index="">
            #{fileGuid}
        </foreach>
    </delete>
    <delete id="deleteSaleCenterAttachByParams">
        delete
        from ais_sale_center.s_media_and_attached
        where fk_table_name = #{tableName}
          and fk_table_id = #{tableId}
          and type_key = #{typeKey}
    </delete>
</mapper>
