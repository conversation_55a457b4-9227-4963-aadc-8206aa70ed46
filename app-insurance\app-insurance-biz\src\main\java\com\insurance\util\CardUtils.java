package com.insurance.util;

import java.util.Random;
import java.util.UUID;

/**
 * @Author:<PERSON>
 * @Date: 2025/5/6
 * @Version 1.0
 * @apiNote:
 */
public class CardUtils {
    /**
     * 生成一个16位的随机卡号（全为数字）
     *
     * @return 16位卡号字符串
     */
    public static String generateCardNumber() {
        StringBuilder cardNumber = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 16; i++) {
            cardNumber.append(random.nextInt(10));
        }
        return cardNumber.toString();
    }

    /**
     * 获取卡号的后三位
     *
     * @param cardNumber 卡号字符串
     * @return 后三位字符串
     */
    public static String getLastThreeDigits(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 3) {
            throw new IllegalArgumentException("卡号无效，长度不足3位");
        }
        return cardNumber.substring(cardNumber.length() - 3);
    }

    /**
     * 随机生成16位信用卡加密密钥
     * @return
     */
    public static String getCardSecretKey() {
        return UUID.randomUUID()
                .toString()
                .replaceAll("-", "")
                .toUpperCase()
                .substring(0, 16);
    }
}
