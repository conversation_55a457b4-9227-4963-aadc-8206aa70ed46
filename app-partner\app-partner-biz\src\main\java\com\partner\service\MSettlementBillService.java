package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.finance.MSettlementBillParamsDto;
import com.partner.entity.MSettlementBillEntity;
import com.partner.entity.MSettlementBillItemEntity;
import com.partner.vo.finance.MSettlementBillItemVo;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

public interface MSettlementBillService  extends IService<MSettlementBillEntity> {

    /**
     * 对账单列表
     * @param page
     * @param dto
     * @return
     */
    IPage getMSettlementBillPage(Page page, @ParameterObject MSettlementBillParamsDto dto);

    /**
     * 对账单明细
     * @param msettlementId
     * @return
     */
    List<MSettlementBillItemVo> getMSettlementBillDetail(@PathVariable("msettlementId")    Long msettlementId);

    /**
     * 下载账单
     * @param dto
     * @param response
     */
    void downloadSettlementBill( MSettlementBillParamsDto dto, HttpServletResponse response);
}
