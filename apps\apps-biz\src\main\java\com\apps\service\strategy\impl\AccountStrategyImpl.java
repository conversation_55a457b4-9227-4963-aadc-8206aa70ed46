package com.apps.service.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.apps.api.dto.AppLoginDto;
import com.apps.api.entity.SystemUserPlatformLoginEntity;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.api.enums.PlatformCodeEnum;
import com.apps.api.vo.system.UserPermissionVo;
import com.apps.exception.AppsGlobalException;
import com.apps.mapper.SystemUserPlatformLoginMapper;
import com.apps.service.SystemUserService;
import com.apps.service.logic.WechatService;
import com.apps.service.strategy.AppLoginVerifyStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/1/9  16:26
 * @Version 1.0
 */
@Service("ACCOUNT")
@Slf4j
@AllArgsConstructor
public class AccountStrategyImpl implements AppLoginVerifyStrategy {

    private final SystemUserPlatformLoginMapper systemUserPlatformLoginMapper;
    private final SystemUserService systemUserService;
    private final WechatService wechatService;

    @Override
    public UserPermissionVo appLoginVerify(AppLoginDto loginDto) {
        SystemUserPlatformLoginEntity loginEntity = systemUserPlatformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getLoginId, loginDto.getAccount())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformId, loginDto.getFormPlatformId())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformCode,
                        loginDto.getFormPlatformCode().equals(PlatformCodeEnum.PMP.getCode()) ? PlatformCodeEnum.PARTNER.getCode() : loginDto.getFormPlatformCode()));
        if (Objects.isNull(loginEntity)) {
            log.error("登录失败,账号不存在:{}", JSONObject.toJSONString(loginDto));
            throw new AppsGlobalException(GlobExceptionEnum.SYS_ACCOUNT_NOT_EXISTS);
        }
        systemUserService.checkLoginUser(loginEntity.getFkUserId());

        try {
            wechatService.saveUserOpenId(loginEntity.getFkUserId(), loginDto.getFormPlatformCode(), loginDto.getCode());
        } catch (Exception e) {
            log.error("保存用户openId失败:{}", e.getMessage());
        }

        return systemUserService.getUserCacheInfo(loginDto.getFormPlatformCode().equals(PlatformCodeEnum.PMP.getCode()) ? false : true
                , loginEntity, loginEntity.getFkUserId());



        //调用oauth2进行登录
//        Map<String, String> map = new HashMap<>();
//        map.put("grant_type", "password");
//        map.put("username", loginDto.getAccount());
//        map.put("password", loginDto.getCertificate());
//        map.put("scope", "server");
//
//        TokenVo tokenVo = remoteAuthService.accountLogin(map);
//        log.info("调用oauth2登录接口返回结果:{}", tokenVo);

//        return tokenService.createToken(tokenVo, loginEntity.getFkUserId());
//        return null;
    }
}
