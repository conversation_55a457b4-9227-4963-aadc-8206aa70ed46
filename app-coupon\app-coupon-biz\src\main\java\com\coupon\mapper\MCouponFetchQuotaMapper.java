package com.coupon.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coupon.dto.CouponQuotaDto;
import com.coupon.entity.MCouponFetchQuotaEntity;
import com.coupon.vo.BDInfo;
import com.coupon.vo.GetAllCouponQuotaVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("coupon")
public interface MCouponFetchQuotaMapper extends BaseMapper<MCouponFetchQuotaEntity> {
    IPage<GetAllCouponQuotaVo> getAllCouponQuotaVo(Page page, @Param("couponQuotaDto") CouponQuotaDto couponQuotaDto);
    BDInfo getBDInfo(@Param("code") String code);
}
