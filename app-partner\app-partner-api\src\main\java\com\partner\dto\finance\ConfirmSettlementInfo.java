package com.partner.dto.finance;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Schema(description = "提交结算申请-参数")
public class ConfirmSettlementInfo {

    @NotNull(message = "学生UUID不能为空")
    @Schema(description = "学生UUID")
    private String studentUUID;
    @Schema(description="学校ID")
    private Long institutionId;

   /* @NotNull(message = "代理id不能为空")
    @Schema(description = "代理id")
    private Long agentId;*/
    /*@Schema(description = "代理UUID")
    private String agentUUID;*/

    @Schema(description = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;



    @NotNull(message = "应付计划Id不能为空")
    @Schema(description = "应付计划Id")
    private Long payablePlanId;

    @NotBlank(message = "业务类型Key不能为空")
    @Schema(description = "业务类型Key")
    private String fkTypeKey;

    @NotNull(message = "结算状态 不能为空")
    @Schema(description = "当前结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    private Integer statusSettlement;


    @Schema(description = "实际支付金额（合并小计=实收+预付）")
    @NotNull(message = "实际支付金额不能为空")
    private BigDecimal amountActual;
    @Schema(description = "应付币种")
    private String fkCurrencyTypeNum;


    @Schema(description = "实际手续费金额（合并小计=实收+预付）")
    @NotNull(message = "实际支付金额不能为空")
    private BigDecimal serviceFeeActual;


    @Schema(description = "汇率")
    @NotNull(message = "汇率不能为空")
    private BigDecimal exchangeRate;
    @NotNull(message = "兑换支付不能为空")
    @Schema(description = "兑换支付金额")
    private BigDecimal amountExchange;
    @NotNull(message = "兑换手续不能为空")
    @Schema(description = "兑换手续费金额")
    private BigDecimal serviceFeeExchange;
}
