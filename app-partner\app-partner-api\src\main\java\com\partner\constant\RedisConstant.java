package com.partner.constant;

/**
 * @Author:<PERSON>
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:
 */
public class RedisConstant {

    /**
     * 全局缓存键前缀
     */
    public static final String REDIS_KEY_PREFIX = "2:";

    /**
     * 伙伴平台标识-大写
     */
    public static final String PARTNER_PLATFORM_CODE = "PARTNER";

    /**
     * 伙伴平台标识-小写
     */
    public static final String PARTNER_PLATFORM_CODE_LOWERCASE = "partner:";

    /**
     * 伙伴用户信息缓存键
     */
    public static final String PARTNER_USERINFO_KEY_PREFIX = REDIS_KEY_PREFIX + PARTNER_PLATFORM_CODE + ":";

    /**
     * 伙伴用户代理关系缓存键
     */
    public static final String PARTNER_USERINFO_AGENT_KEY_PREFIX = PARTNER_PLATFORM_CODE_LOWERCASE + "user_agent:";


}
