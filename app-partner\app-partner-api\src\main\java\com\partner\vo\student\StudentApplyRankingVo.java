package com.partner.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description="学生申请学校排名")
public class StudentApplyRankingVo {

    @Schema(description = "排名")
    private String ranking;
    @Schema(description = "学校Id")
    private Long institutionId;

    @Schema(description = "学校名称")
    private String institutionName;

    @Schema(description = "学校英文名称")
    private String institutionNameChn;
    @Schema(description = "申请数量")
    private int applyTotal;

}
