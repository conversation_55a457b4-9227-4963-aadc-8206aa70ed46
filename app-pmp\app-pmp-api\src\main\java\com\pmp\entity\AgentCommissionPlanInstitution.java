package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_agent_commission_plan_institution")
public class AgentCommissionPlanInstitution extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "代理佣金方案Id")
    private Long fkAgentCommissionPlanId;

    @Schema(description = "学校Id")
    private Long fkInstitutionId;
}
