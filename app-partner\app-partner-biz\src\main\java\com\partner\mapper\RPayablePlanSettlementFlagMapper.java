package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RPayablePlanSettlementFlagEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【r_payable_plan_settlement_flag】的数据库操作Mapper
* @createDate 2025-01-10 18:10:24
* @Entity com.partner.entity.RPayablePlanSettlementFlag
*/
@Mapper
@DS("financedb")
public interface RPayablePlanSettlementFlagMapper extends BaseMapper<RPayablePlanSettlementFlagEntity> {

}




