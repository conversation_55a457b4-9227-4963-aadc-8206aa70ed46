package com.partner.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.partner.dto.finance.paramsmapper.CommissionSearchParams;
import com.partner.dto.finance.paramsmapper.SettlementSearchParams;
import com.partner.entity.MReceivablePlanEntity;
import com.partner.vo.finance.AgentSettlementOfferItemVo;
import com.partner.vo.finance.CommissionAffirmVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FinanceMapper {
    IPage<CommissionAffirmVo> getCommissionAffirmPage(Page page, @Param("query") CommissionSearchParams params);

    List<MReceivablePlanEntity> getReceivablePlan(@Param("ids") List<Long> ids);


    List<AgentSettlementOfferItemVo> getAgentSettlementList(@Param("query") SettlementSearchParams params);

    int selectstatusSettlement(@Param("agentId") Long agentId);
}
