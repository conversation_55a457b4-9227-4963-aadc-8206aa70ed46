<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.coupon.mapper.MCouponFetchQuotaMapper">
    <select id="getAllCouponQuotaVo" resultType="com.coupon.vo.GetAllCouponQuotaVo">
        SELECT
        couponFetchQuota.id AS id,
        couponFetchQuota.fk_coupon_type_id AS couponTypeUuid,
        couponType.title AS couponTypeTitle,
        couponFetchQuota.fk_role_code AS roleCode,
        couponFetchQuota.quota AS quota
        FROM m_coupon_fetch_quota AS couponFetchQuota
        LEFT JOIN m_coupon_type AS couponType ON couponFetchQuota.fk_coupon_type_id = couponType.id
        <where>
            <if test="couponQuotaDto.couponTypeId != null and couponQuotaDto.couponTypeId != '' ">
                AND couponFetchQuota.fk_coupon_type_id = #{couponQuotaDto.couponTypeId}
            </if>
            <if test="couponQuotaDto.couponTypeTitle != null and couponQuotaDto.couponTypeTitle != '' ">
                AND couponType.title = #{couponQuotaDto.couponTypeTitle}
            </if>
            <if test="couponQuotaDto.roleCode != null and couponQuotaDto.roleCode != '' ">
                AND couponFetchQuota.fk_role_code = #{couponQuotaDto.roleCode}
            </if>
            <if test="couponQuotaDto.quota != null and couponQuotaDto.quota != '' ">
                AND couponFetchQuota.quota = #{couponQuotaDto.quota}
            </if>
        </where>
    </select>

    <select id="getBDInfo" resultType="com.coupon.vo.BDInfo">
        SELECT
        staff.name AS bdName,
        area.name_chn AS areaName
        FROM ais_sale_center.r_staff_bd_code AS bdCode
        LEFT JOIN ais_permission_center.m_staff AS staff ON bdCode.fk_staff_id = staff.id
        LEFT JOIN ais_institution_center.u_area_region AS area ON bdCode.fk_area_region_id = area.id
        <where>
            bdCode.bd_code = #{code}
        </where>
    </select>
</mapper>