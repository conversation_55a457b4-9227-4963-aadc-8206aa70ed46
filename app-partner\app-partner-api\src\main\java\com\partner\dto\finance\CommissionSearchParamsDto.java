package com.partner.dto.finance;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "佣金-待确认页面-参数")
public class CommissionSearchParamsDto {

    /*@NotBlank(message = "代理UUID不能为空")
    private String agentUUID;*/


   /* @NotNull(message = "伙伴用户（确认人）不能为空")
    private Long partnerUserId;*/
    @NotNull(message = "年份不为空")
    private Integer year;
    @NotNull(message = "月份不能为空")
    private Integer month;
    @NotNull(message = "确认类型：0待确认  1已确认 2过期自动确认")
    private Integer type;
}
