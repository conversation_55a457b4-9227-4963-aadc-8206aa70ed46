package com.partner.vo.file;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 文件上传参数-落库
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadFileParam {

    @Schema(description = "文件数据库名，如：app_file_center")
    private String fileDb;

    @Schema(description = "文件表名，如：m_file_pmp")
    private String fileTable;

    @Schema(description = "媒体数据库名，如：ais_pmp2_center")
    private String mediaDb;

    @Schema(description = "媒体表名，如：s_media_and_attached")
    private String mediaTable;

    @Schema(description = "目标表主键ID")
    private Long tableId;

    @Schema(description = "目标表名")
    private String tableName;

    @Schema(description = "类型关键字，如 institution_pic / alumnus_head_icon")
    private String typeKey;

    @Schema(description = "索引值（默认从0开始）")
    private Integer indexKey;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    // 非数据库字段，仅供动态SQL使用
    @JsonIgnore
    @Schema(description = "上传文件信息")
    private UploadFileVo mediaInfo;

    @JsonIgnore
    @Schema(description = "文件库和媒体库是否一起保存")
    private Boolean uploadTogether;

}
