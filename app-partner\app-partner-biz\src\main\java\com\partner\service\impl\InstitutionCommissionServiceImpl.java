package com.partner.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.CommissionParamsDetailDto;
import com.partner.dto.CommissionParamsDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.enums.ConfigTypeEnum;
import com.partner.enums.TypeEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.InstitutionCommissionMapper;
import com.partner.service.InstitutionCommissionService;
import com.partner.util.MyDateUtils;
import com.partner.util.TencentCloudUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.CommissionDetailInfo;
import com.partner.vo.CommissionDetailVo;
import com.partner.vo.base.LABELComboxVo;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.GroupResultCombox;
import com.partner.vo.combox.LeaveResultCombox;
import com.partner.vo.combox.TypeResultCombox;
import com.pmp.dto.CommissionDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.FeignCommissionDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.feign.RemotePmpService;
import com.pmp.vo.commission.MajorLevelTreeVo;
import com.pmp.vo.commission.MajorLevelVo;
import com.pmp.vo.commission.MergeCommissionVo;
import com.pmp.vo.institution.CountryVo;
import com.pmp.vo.institution.GroupVo;
import com.pmp.vo.institution.InstitutionTypeVo;
import com.pmp.vo.institution.InstitutionVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class InstitutionCommissionServiceImpl extends ServiceImpl<InstitutionCommissionMapper, CommissionDetailVo> implements InstitutionCommissionService {

    private final InstitutionCommissionMapper institutionCommissionMapper;
    private final TencentCloudUtils tencentCloudUtils;
    @Autowired
    private RemotePmpService remotePmpService;

    @Override
    public IPage getCommissionPage(Page page, CommissionParamsDto dto) {
        if (dto.getYear() == 0) {
            dto.setYear(MyDateUtils.getYear(new Date()));//获取当年
        }
        String baseurl = tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        dto.setMMageAddress(baseurl);
        IPage<CommissionDetailVo> commissionPage = institutionCommissionMapper.getCommissionPage(page, dto);
        List<CommissionDetailVo> commissionDetailVos = commissionPage.getRecords();
        //添加标签类型

        commissionDetailVos.forEach(vo -> {
            String labelIds = vo.getLabelIds();
            if (labelIds != null && labelIds.length() > 0) {
                List<LABELComboxVo> tmpbox = TypeEnum.enumsArrays(labelIds);
                vo.setLabelType(tmpbox);
            }
            CommissionParamsDetailDto paramsDe = new CommissionParamsDetailDto();
            paramsDe.setYear(dto.getYear());
            paramsDe.setInstitutionId(vo.getInstitutionId());
            paramsDe.setCompanyId(dto.getCompanyId());
            List<CommissionDetailInfo> rtesultDetail = institutionCommissionMapper.getCommissionDetail(paramsDe);
            vo.setCommissionDetailInfo(rtesultDetail);
        });


        return commissionPage;
    }

    @Override
    public List<CommissionDetailInfo> getCommissionDetail(CommissionParamsDetailDto dto) {
        if (dto.getYear() == 0) {
            dto.setYear(MyDateUtils.getYear(new Date()));//获取当年
        }

        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        dto.setCompanyId(userinfo.getCompanyId());
        //查询学校高佣设置详细
        List<CommissionDetailInfo> rtesultDetail = institutionCommissionMapper.getCommissionDetail(dto);

        return rtesultDetail;
    }


    @Override
    public List<CountryCombox> getCountryCombox(CommissionParamsDetailDto dto, int type) {
        List<CountryCombox> resultlist = new ArrayList<CountryCombox>();
        if (dto.getCompanyId() == null) {
            dto.setCompanyId(ConfigTypeEnum.M_COMPANY.key);
        }
        if (type == 0) {
            resultlist = institutionCommissionMapper.getCountryCombox(dto);
        } else if (type == 1) {
            resultlist = institutionCommissionMapper.getCountryComboxAll(dto);
        }

        return resultlist;
    }

    @Override
    public List<TypeResultCombox> getTypeList(CommissionParamsDetailDto dto) {

        Integer year = dto.getYear();
        if (year == null) {
            dto.setYear(LocalDateTime.now().getYear());
        }
        List<TypeResultCombox> typeResultVos = institutionCommissionMapper.getTypeList(dto);

        return typeResultVos;
    }


    @Override
    public List<GroupResultCombox> getGroupByCommission(CommissionParamsDetailDto dto) {
        Integer year = dto.getYear();

        if (year == null) {
            dto.setYear(LocalDateTime.now().getYear());
        }

        List<GroupResultCombox> groupResultVos = institutionCommissionMapper.getGroupByCommission(dto);
        return groupResultVos;
    }

    @Override
    public List<LeaveResultCombox> getLeaveList(CommissionParamsDetailDto dto) {

        Integer year = dto.getYear();
        if (year == null) {
            dto.setYear(LocalDateTime.now().getYear());
        }

        List<LeaveResultCombox> leaveResultVos = institutionCommissionMapper.getLeaveList(dto);
        return leaveResultVos;
    }


    @Override
    public List<CommissionDetailVo> getTallCommissionList(CommissionParamsDto dto) {
        if (dto.getYear() == 0) {
            dto.setYear(MyDateUtils.getYear(new Date()));//获取当年
        }
        if (dto.getCompanyId() == null) {
            dto.setCompanyId(ConfigTypeEnum.M_COMPANY.key);
        }
        List<CommissionDetailVo> resultList = Collections.emptyList();

        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());


        String baseurl = tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        dto.setMMageAddress(baseurl);
        dto.setCompanyId(userinfo.getCompanyId());
        resultList = institutionCommissionMapper.getTallCommissionList(dto);


        return resultList;
    }

    @Override
    public IPage getTallCommissionList(Page page, CommissionParamsDto dto) {
        if (dto.getYear() == 0) {
            dto.setYear(MyDateUtils.getYear(new Date()));//获取当年
        }
        String baseurl = tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        dto.setMMageAddress(baseurl);
        IPage<CommissionDetailVo> commissionPage = institutionCommissionMapper.getCommissionPage(page, dto);
        List<CommissionDetailVo> commissionDetailVos = commissionPage.getRecords();
        //添加标签类型

        commissionDetailVos.forEach(vo -> {
            String labelIds = vo.getLabelIds();
            if (labelIds != null && labelIds.length() > 0) {
                List<LABELComboxVo> tmpbox = TypeEnum.enumsArrays(labelIds);
                vo.setLabelType(tmpbox);
            }
            CommissionParamsDetailDto paramsDe = new CommissionParamsDetailDto();
            paramsDe.setYear(dto.getYear());
            paramsDe.setInstitutionId(vo.getInstitutionId());
            paramsDe.setCompanyId(dto.getCompanyId());
            List<CommissionDetailInfo> rtesultDetail = institutionCommissionMapper.getCommissionDetail(paramsDe);
            vo.setCommissionDetailInfo(rtesultDetail);
        });


        return commissionPage;

    }


    @Override
    public CommissionDetailVo getHomeCommissionDetail(CommissionParamsDetailDto dto) {
        CommissionDetailVo resultdetail = new CommissionDetailVo();
        if (dto.getYear() == 0) {
            dto.setYear(MyDateUtils.getYear(new Date()));//获取当年
        }
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        CommissionParamsDto institutionParams = new CommissionParamsDto();
        institutionParams.setYear(dto.getYear());
        institutionParams.setInstitutionId(dto.getInstitutionId());

        String baseurl = tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        institutionParams.setMMageAddress(baseurl);
        institutionParams.setCompanyId(userinfo.getCompanyId());
        List<CommissionDetailVo> resultList = institutionCommissionMapper.getTallCommissionList(institutionParams);
        if (resultList != null && resultList.size() > 0) {
            resultdetail = resultList.get(0);
        }

        dto.setCompanyId(userinfo.getCompanyId());
        List<CommissionDetailInfo> rtesultDetail = institutionCommissionMapper.getCommissionDetail(dto);
        resultdetail.setCommissionDetailInfo(rtesultDetail);

        return resultdetail;
    }

    @Override
    public MergeCommissionVo getMergeCommission(CommissionDto commissionDto) {
        FeignCommissionDto param = FeignCommissionDto.builder()
                .startDate(commissionDto.getStartDate())
                .institutionId(commissionDto.getInstitutionId())
                .agentId(UserInfoParamsUtils.getCurrentAgentId())
                .companyId(UserInfoParamsUtils.getCurrentCompanyId())
                .highCommissionCode("PARTNER")
                .build();
        log.info("调用pmp获取院校佣金,param:{}", JSONObject.toJSONString(param));
        R<MergeCommissionVo> result = remotePmpService.getMergeCommission(param);
        if (!result.isSuccess()) {
            log.error("调用pmp获取院校佣金失败:{}", JSONObject.toJSONString(result));
            log.error("调用pmp获取集团列表失败,参数:{}", JSONObject.toJSONString(param));
            throw new PartnerExceptionInfo(500, "获取院校佣金失败");
        }
        return result.getData();
    }

    @Override
    public List<CountryVo> countryList(DateDto dto) {
        dto.setAgentId(UserInfoParamsUtils.getCurrentAgentId());
        dto.setCompanyId(UserInfoParamsUtils.getCurrentCompanyId());
        R<List<CountryVo>> result = remotePmpService.countryList(dto);
        if (!result.isSuccess()) {
            log.error("调用pmp获取国家列表失败:{}", JSONObject.toJSONString(result));
            log.error("调用pmp获取集团列表失败,参数:{}", JSONObject.toJSONString(dto));
            throw new PartnerExceptionInfo(500, "获取国家列表失败");
        }
        return result.getData();
    }

    @Override
    public List<InstitutionTypeVo> institutionTypeList() {
        R<List<InstitutionTypeVo>> result = remotePmpService.institutionTypeList();
        if (!result.isSuccess()) {
            log.error("调用pmp获取学校列表失败:{}", JSONObject.toJSONString(result));
            throw new PartnerExceptionInfo(500, "获取学校类型列表失败");
        }
        return result.getData();
    }

    @Override
    public List<GroupVo> groupList(DateDto dateDto) {
        dateDto.setAgentId(UserInfoParamsUtils.getCurrentAgentId());
        dateDto.setCompanyId(UserInfoParamsUtils.getCurrentCompanyId());
        R<List<GroupVo>> result = remotePmpService.groupList(dateDto);
        if (!result.isSuccess()) {
            log.error("调用pmp获取集团列表失败:{}", JSONObject.toJSONString(result));
            log.error("调用pmp获取集团列表失败,参数:{}", JSONObject.toJSONString(dateDto));
            throw new PartnerExceptionInfo(500, "获取集团列表失败");
        }
        return result.getData();
    }

    @Override
    public List<MajorLevelVo> selectMajorLevel() {
        R<List<MajorLevelVo>> result = remotePmpService.selectMajorLevel();
        if (!result.isSuccess()) {
            log.error("调用pmp获取课程等级列表失败:{}", JSONObject.toJSONString(result));
            throw new PartnerExceptionInfo(500, "获取课程等级列表失败");
        }
        return result.getData();
    }

    @Override
    public List<MajorLevelTreeVo> getMajorLevelTree() {
        R<List<MajorLevelTreeVo>> result = remotePmpService.getMajorLevelTree();
        if (!result.isSuccess()) {
            log.error("调用pmp获取课程等级列表失败:{}", JSONObject.toJSONString(result));
            throw new PartnerExceptionInfo(500, "获取课程等级列表失败");
        }
        return result.getData();
    }

    @Override
    public Page<InstitutionVo> institutionList(InstitutionDto institutionDto) {
        institutionDto.setAgentId(UserInfoParamsUtils.getCurrentAgentId());
        institutionDto.setCompanyId(UserInfoParamsUtils.getCurrentCompanyId());
        if (Objects.nonNull(institutionDto.getIsHighCommission())&&institutionDto.getIsHighCommission()){
            institutionDto.setHighCommissionCode("PARTNER");
        }
        R<Page<InstitutionVo>> result = remotePmpService.institutionList(institutionDto);
        if (!result.isSuccess()) {
            log.error("调用pmp获取学校列表失败:{}", JSONObject.toJSONString(result));
            log.error("调用pmp获取学校列表失败,参数:{}", JSONObject.toJSONString(institutionDto));
            throw new PartnerExceptionInfo(500, "获取学校列表失败");
        }
        //填充学校封面
        List<InstitutionVo> records = result.getData().getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> institutionIds = records.stream().map(InstitutionVo::getInstitutionId).collect(Collectors.toList());
            Map<Long, String> coverMap = institutionCommissionMapper.selectInstitutionCover(institutionIds)
                    .stream()
                    .collect(Collectors.toMap(
                            InstitutionVo::getInstitutionId,
                            InstitutionVo::getInstitutionCover,
                            (existing, replacement) -> existing));
            String baseurl = tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
            records.stream().forEach(i->{
                if(coverMap.containsKey(i.getInstitutionId())){
                    i.setInstitutionCover(baseurl+coverMap.get(i.getInstitutionId()));
                }
            });
            result.getData().setRecords(records);
        }
        return result.getData();
    }
}
