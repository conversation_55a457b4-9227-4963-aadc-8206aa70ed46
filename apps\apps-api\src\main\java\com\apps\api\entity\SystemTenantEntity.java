package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_tenant")
@Schema(description = "租户")
public class SystemTenantEntity extends Model<SystemTenantEntity> {
    private static final long serialVersionUID = 1L;


    /**
     * 租户Id
     */
    @Schema(description="租户Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户名称
     */
    @Schema(description="租户名称")
    private String name;

    /**
     * 租户名称（英文）
     */
    @Schema(description="租户名称（英文）")
    private String nameEn;

    /**
     * 租户CODE
     */
    @Schema(description="租户CODE")
    private String code;

    /**
     * 租户域名
     */
    @Schema(description="租户域名")
    private String tenantDomain;

    /**
     * 开始时间
     */
    @Schema(description="开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description="结束时间")
    private LocalDateTime endTime;

    /**
     * 状态，0已停用，1启用中
     */
    @Schema(description="状态，0已停用，1启用中")
    private Integer status;

    /**
     * 删除标记，0未删除，1已删除
     */
    @Schema(description="删除标记，0未删除，1已删除")
    private Integer isDelFlag;;


    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}