package com.partner.vo.student;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "学生详情-申请信息")
public class StudentDetailOfferStepInfo {
    @Schema(description = "申请学校")
    private String institutionName;
    @Schema(description = "申请学校中文名")
    private String institutionNameChn;
    @Schema(description = "申请学校课程")
    private String courseName;

    private Long stepid;
    @Schema(description = "申请步骤")
    private String stepName;
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date itemStepTime;

    private String offerItemUUID;



}
