package com.partner.service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.MEventIncentiveParamsDto;
import com.partner.entity.MEventIncentiveEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.vo.MEventIncentiveVo;
import com.partner.vo.combox.CountryCombox;

import java.util.List;

public interface MEventIncentiveService
        extends IService<MEventIncentiveEntity>{

    /**
     * @desc  奖励政策分页查询
     * @return
     */
    IPage getMEventIncentivePage(Page page, MEventIncentiveParamsDto dto);

    List<CountryCombox> getCountryCombox(MEventIncentiveParamsDto dto);

    /**
     * @desc  奖励政策明细
     * @return
     */
    MEventIncentiveVo getIncentiveDetail(Long eventIncentiveId);
}
