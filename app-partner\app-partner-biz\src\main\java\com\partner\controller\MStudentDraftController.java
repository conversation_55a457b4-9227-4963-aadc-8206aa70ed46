package com.partner.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.student.MStudentDraftParamsDto;
import com.partner.service.MStudentDraftService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(description = "mstudentdraft" , name = "小程序-学生草稿-审核" )
@RestController
@RequestMapping("/mstudentdraft")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MStudentDraftController {

    private final MStudentDraftService studentDraftService;

    @Operation(summary = "学生管理-草稿箱列表-审核列表-审核失败列表" , description = "学生管理-草稿箱列表-审核列表-审核失败列表" )
    @SysLog("学生管理-草稿箱列表-审核列表-审核失败列表" )
    @GetMapping("/getPartnerStudents" )
    public R getPartnerStudents(Page page, @ParameterObject @Valid MStudentDraftParamsDto dto){
        IPage result=studentDraftService.getPartnerStudents(page,dto);

        return R.ok(result);
    }

    @Operation(summary = "学生管理-审核日志" , description = "学生管理-审核日志" )
    @SysLog("学生管理-审核日志" )
    @GetMapping("/getCheckStudentsPage" )
    public R getCheckStudentsPage(Page page, @ParameterObject @Valid MStudentDraftParamsDto dto){
        IPage   result=studentDraftService.getCheckStudentsPage(page,dto);
        return R.ok(result);
    }


    @Operation(summary = "学生管理-审核日志-阅读" , description = "学生管理-阅读" )
    @SysLog("学生管理-审核日志-阅读" )
    @GetMapping("/getReadingCheck" )
    public R getReadingCheck(){
        studentDraftService.getReadingCheck();
        return R.ok();
    }

    @Operation(summary = "学生管理-审核日志-未阅读数量" , description = "学生管理-未阅读数量" )
    @SysLog("学生管理-审核日志-未阅读数量" )
    @GetMapping("/getNoReadingNum" )
    public R getNoReadingNum(){
        return R.ok(studentDraftService.getNoReadingNum());
    }




    @Operation(summary = "学生管理-草稿箱-删除" , description = "学生管理-草稿箱-删除" )
    @SysLog("学生管理-草稿箱-删除" )
    @PostMapping("deleteOne/{drafId}")
    public R deleteOne(  @PathVariable("drafId") Long drafId){
        return R.ok(studentDraftService.deleteOne(drafId));
    }

    @Operation(summary = "学生管理-草稿箱-详情" , description = "学生管理-草稿箱-详情" )
    @SysLog("学生管理-草稿箱-详情" )
    @PostMapping("getStudentOne/{drafId}")
    public R getStudentOne(  @PathVariable("drafId") Long drafId){
        return R.ok(studentDraftService.getStudentOne(drafId));
    }

    /*@Operation(summary = "学生管理-草稿箱-提交" , description = "学生管理-草稿箱-提交" )
    @SysLog("学生管理-草稿箱-提交" )
    @PostMapping("submitPull/{drafId}")
    public R submitPull(  @PathVariable("drafId") Long drafId){
        return R.ok(studentDraftService.submitPull(drafId));
    }*/








}
