package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("s_media_and_attached")
@Schema(description = "附件表")
public class SMediaAndAttachedEntity {
    @TableId(type = IdType.AUTO) // 使用数据库自增主键
    @Schema(description = "媒体附件Id")
    private Long id;

    @Schema(description = "文件guid(文档中心)")
    private String fkFileGuid;

    @Schema(description = "表名")
    private String fkTableName;

    @Schema(description = "表Id")
    private Long fkTableId;

    @Schema(description = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @Schema(description = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;

    @Schema(description = "链接")
    private String link;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;
}
