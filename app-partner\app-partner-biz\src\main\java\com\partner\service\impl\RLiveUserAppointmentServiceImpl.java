package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.RLiveUserAppointmentDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.RLivePartnerUserAppointmentEntity;
import com.partner.enums.ConfigTypeEnum;
import com.partner.mapper.RLiveUserAppointmentMapper;
import com.partner.service.RLiveUserAppointmentService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.AppointmentInfo;
import com.partner.vo.AppointmentVo;
import com.partner.vo.jingang.UserAppointmentViewVo;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

@Service
@AllArgsConstructor
public class RLiveUserAppointmentServiceImpl extends ServiceImpl<RLiveUserAppointmentMapper, RLivePartnerUserAppointmentEntity>
        implements RLiveUserAppointmentService {
    private final RLiveUserAppointmentMapper appointmentMapper;
    private final RedisTemplate redisTemplate;

    private final RLiveUserAppointmentMapper rLiveUserAppointmentMapper;

    @Override
    public boolean userAppointment(RLiveUserAppointmentDto dto) {
        RLivePartnerUserAppointmentEntity entity= BeanCopyUtils.objClone(dto,RLivePartnerUserAppointmentEntity::new);
        String sliveid=entity.getFkLiveId().toString();


        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        String loginId=fzhUser.getLoginId();
        String suserid=userinfo.getPartnerUserId().toString();
        entity.setFkPartnerUserId(userinfo.getPartnerUserId());
        RLivePartnerUserAppointmentEntity livepo=appointmentMapper.selectOne(new LambdaQueryWrapper<RLivePartnerUserAppointmentEntity>()
                        .eq(RLivePartnerUserAppointmentEntity::getFkLiveId, sliveid)
                        .eq(RLivePartnerUserAppointmentEntity::getFkPartnerUserId, suserid)
                ,false);
        if(ObjectUtil.isNotEmpty(livepo)){
            return true;
        }


        AppointmentInfo appointmentInfo = new AppointmentInfo();
        appointmentInfo.setUserId(entity.getFkPartnerUserId());
        //查询用户头像
        UserAppointmentViewVo appointmentvo= rLiveUserAppointmentMapper.selectDetailUser(entity.getFkPartnerUserId());
        appointmentInfo.setFileKey(appointmentvo.getWechatIconUrl());//用户头像

        String appointmentZsetKey=ConfigTypeEnum.R_LIVE_USER_APPOINTMENT_ZSET.uNewsType+":"+sliveid;
        String appointmentHashKey=ConfigTypeEnum.R_LIVE_USER_APPOINTMENT_HASH.uNewsType+":"+sliveid;
        try{
                if(!Boolean.TRUE.equals(redisTemplate.hasKey(appointmentZsetKey))) {//键值不存在
                    List<RLivePartnerUserAppointmentEntity> livelist=appointmentMapper.selectList(new LambdaQueryWrapper<RLivePartnerUserAppointmentEntity>()
                            .eq(RLivePartnerUserAppointmentEntity::getFkLiveId,sliveid)
                            .orderByAsc(RLivePartnerUserAppointmentEntity::getGmtCreate)
                    );
                    if(ObjectUtils.isNotEmpty(livelist)){
                       /* List<AppointmentInfo> tmpinfo= new ArrayList<>();
                        List<AppointmentInfo> allInfo= new ArrayList<>();*/
                        for(int i=0;i<livelist.size();i++){
                            RLivePartnerUserAppointmentEntity tmpdbnum=livelist.get(i);
                            String spartnerUserid=tmpdbnum.getFkPartnerUserId().toString();

                            AppointmentInfo redisappointment=new AppointmentInfo();
                            redisappointment.setUserId(tmpdbnum.getFkPartnerUserId());
                            if(livelist.size()>5 && i>=livelist.size()-5){
                                //tmpinfo.add(redisappointment);
                                ZonedDateTime zonedDateTime = tmpdbnum.getGmtCreate().atZone(ZoneId.systemDefault());
                                redisTemplate.opsForZSet().add(appointmentZsetKey,spartnerUserid,zonedDateTime.toInstant().toEpochMilli());
                            }
                            if(livelist.size()<=5){
                                //tmpinfo.add(redisappointment);
                                ZonedDateTime zonedDateTime = tmpdbnum.getGmtCreate().atZone(ZoneId.systemDefault());
                                redisTemplate.opsForZSet().add(appointmentZsetKey,spartnerUserid,zonedDateTime.toInstant().toEpochMilli());
                            }
                           // allInfo.add(redisappointment);
                            redisTemplate.opsForHash().put(appointmentHashKey,spartnerUserid,redisappointment);
                        }
                    }
                }

                //保存预约用户
                redisTemplate.opsForZSet().add(appointmentZsetKey,suserid,System.currentTimeMillis());
                Object redisappointment=redisTemplate.opsForHash().get(appointmentHashKey,suserid);
                if(ObjectUtil.isEmpty(redisappointment)){
                    redisTemplate.opsForHash().put(appointmentHashKey,suserid,appointmentInfo);

                }else{
                    /*AppointmentInfo tmp=(AppointmentInfo)redisappointment;
                    tmp.getUserId();*/
                }
        }catch (Exception e){
            log.error(e.getMessage());
        }
        entity.setStatus(0);//未发送预约消息
        entity.setGmtCreateUser(loginId);
        entity.setGmtCreate(LocalDateTime.now());
        entity.setGmtModifiedUser(loginId);
        entity.setGmtModified(LocalDateTime.now());
        appointmentMapper.insert(entity);

        return true;
    }

    @Override
    public boolean cancelAppointment(RLiveUserAppointmentDto dto) {

        String sliveid=dto.getFkLiveId().toString();
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        String suserid=userinfo.getPartnerUserId().toString();

        RLivePartnerUserAppointmentEntity livepo=appointmentMapper.selectOne(new LambdaQueryWrapper<RLivePartnerUserAppointmentEntity>()
                        .eq(RLivePartnerUserAppointmentEntity::getFkLiveId, sliveid)
                        .eq(RLivePartnerUserAppointmentEntity::getFkPartnerUserId, suserid)
                ,false);

        if(ObjectUtil.isEmpty(livepo)){
            return true;
        }
        //取消预约
        String appointmentZsetKey=ConfigTypeEnum.R_LIVE_USER_APPOINTMENT_ZSET.uNewsType+":"+sliveid;
        String appointmentHashKey=ConfigTypeEnum.R_LIVE_USER_APPOINTMENT_HASH.uNewsType+":"+sliveid;
        redisTemplate.opsForZSet().remove(appointmentZsetKey,suserid);
        redisTemplate.opsForHash().delete(appointmentHashKey,suserid);
        appointmentMapper.deleteById(livepo.getId());
        return true;
    }


    @Override
    public List<AppointmentVo> getAppointmentList(Long liveId) {
        List<AppointmentVo> result=appointmentMapper.getAppointmentList(liveId);
        return result;
    }


}
