package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.MFilePartnerEntity;
import com.partner.entity.SMediaAndAttachedEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_file_partner】的数据库操作Mapper
* @createDate 2025-01-08 16:45:07
* @Entity com.partner.entity.MFilePartner
*/
@Mapper
@DS("appfiledb")
public interface MFilePartnerMapper extends BaseMapper<MFilePartnerEntity> {

    int insertSelective(MFilePartnerEntity entity);
    int insertAisFileSelective(MFilePartnerEntity entity);




    MFilePartnerEntity selectSaleFileOne(SMediaAndAttachedEntity attacheEntity);

}




