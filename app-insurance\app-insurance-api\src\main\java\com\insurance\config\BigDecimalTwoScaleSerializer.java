package com.insurance.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * BigDecimalSerializer
 * 金额显示
 *
 * <AUTHOR>
 * @date 2022/1/20 15:09
 */
public class BigDecimalTwoScaleSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            gen.writeNumber(value.setScale(2, RoundingMode.HALF_UP));
        } else {
            gen.writeNull();
        }
    }
}
