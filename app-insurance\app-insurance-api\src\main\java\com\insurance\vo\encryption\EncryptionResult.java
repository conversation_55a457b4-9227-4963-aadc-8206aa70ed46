package com.insurance.vo.encryption;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/31
 * @Version 1.0
 * @apiNote:
 */
@Data
public class EncryptionResult {

    @Schema(description = "状态码")
    private Integer code;

    @Schema(description = "请求结果")
    private Boolean success;

    @Schema(description = "信息")
    private String message;

    @Schema(description = "加密密钥")
    private String key;
}
