package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-23 10:05:44
 */

@Data
@TableName("u_feedback_order_type")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" u_feedback_order_type ")
public class UFeedbackOrderTypeEntity extends Model<UFeedbackOrderTypeEntity>{

  @Schema(description = "反馈工单类型Id")
  private Long id;
 

  @Schema(description = "平台应用Id")
  private Long fkPlatformId;
 

  @Schema(description = "平台应用CODE")
  private String fkPlatformCode;
 

  @Schema(description = "类型名称")
  private String typeName;
 

  @Schema(description = "排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
