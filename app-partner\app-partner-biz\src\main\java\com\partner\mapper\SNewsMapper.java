package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.SNewsParamsDto;
import com.partner.entity.SNewsEntity;
import com.partner.vo.SNewsListVo;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.InstitutionCombox;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SNewsMapper extends BaseMapper<SNewsEntity> {

    IPage<SNewsListVo> getSNewsListPage(Page page, @Param("query")SNewsParamsDto dto);

    SNewsEntity getByDetail(Long id);

    List<CountryCombox> getCountryCombox(SNewsParamsDto dto);

    List<InstitutionCombox> getInstitutionCombox(SNewsParamsDto dto);



}