package com.partner.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.ContractDto;
import com.partner.dto.MAgentContractDto;
import com.partner.dto.ais.CreateContractPdfDto;
import com.partner.entity.MAgentContractEntity;
import com.partner.vo.contract.AgentContractDetailVo;
import com.partner.vo.contract.AgentContractInfoVo;
import com.partner.vo.contract.AgentContractVo;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @description 针对表【m_agent_contract】的数据库操作Service
 * @createDate 2025-01-08 11:21:12
 */
public interface MAgentContractService extends IService<MAgentContractEntity> {
    /**
     * @param page
     * @param dto
     * @return
     * @desc 代理合同列表
     */
    Page<AgentContractInfoVo> getContractPage(Page page, MAgentContractDto dto);

    /**
     * @param dto
     * @return
     * @desc 合同详情
     */
    AgentContractVo getContract(MAgentContractDto dto);

    /**
     * @param file
     * @param contractId
     * @desc 合同变更
     */
    void uploadContract(MultipartFile file, Long contractId,String customerFileName);

    /**
     * @param contractDto
     * @desc 合同签名
     */
    void agentConfirmSignature(ContractDto contractDto);

    /**
     * @param contractId
     * @return
     * @desc 查看合同详情
     */
    AgentContractDetailVo getContractDetail(Long contractId);

    /**
     * @return
     * @desc 获取最新合同状态
     */
    Integer getLatestAgentContractStatus();


    /**
     * 获取生成合同参数
     * @param contractId
     * @param agentId
     * @param contractVsion
     * @param contractTemplateMode
     * @return
     */
    CreateContractPdfDto getEncryptCreateContractPdfParam(Long contractId,
                                                          Long agentId,
                                                          Integer contractVsion,
                                                          Integer contractTemplateMode);


}
