<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.apps.mapper.SystemUserRoleMapper">

    <select id="findUserIdByRoleCode" resultType="java.lang.Long">
        SELECT distinct (ur.fk_user_id)
        FROM system_user_role ur
        WHERE EXISTS (SELECT 1
        FROM system_role r
        WHERE r.fk_platform_id = #{platformId}
        AND r.fk_platform_code = #{platformCode}
        AND r.role_code
        in
        <foreach collection="roleCodes" item="roleCode" open="(" separator="," close=")">
            #{roleCode}
        </foreach>
        AND r.id = ur.fk_role_id)
    </select>
</mapper>