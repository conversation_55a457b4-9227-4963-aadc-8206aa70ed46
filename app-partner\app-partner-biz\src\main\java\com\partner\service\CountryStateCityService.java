package com.partner.service;

import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.base.InstitutionCourseParamsDto;
import com.partner.dto.base.InstitutionSearchDto;
import com.partner.dto.student.AreaCountryDto;
import com.partner.vo.base.*;
import com.partner.vo.combox.AreaCountryVo;
import org.jsoup.Connection;

import java.util.List;

public interface CountryStateCityService {


    List<CountryBaseCombox> getCountryCombox(CountryStateCityParamsDto dto);

    List<CountryBaseCombox> getAllPubCountryCombox(CountryStateCityParamsDto dto);


    List<StateBaseCombox> getStateCombox(CountryStateCityParamsDto dto);

    List<CityBaseCombox> getCityCombox(CountryStateCityParamsDto dto);

    List<BaseCombox> getEducationCombox();

    List<BaseCombox> getInstitutionList(CountryStateCityParamsDto dto);

    List<CourseCombox> getInstitutionCourseList(InstitutionCourseParamsDto dto);


    List<BaseCombox> getInstitutionListSearch(InstitutionSearchDto dto);


    List<AreaCountryVo>  getAreaCode(AreaCountryDto params);
}
