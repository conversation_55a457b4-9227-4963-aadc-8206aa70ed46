package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.dto.partner.SavePartnerUserDto;
import com.apps.api.dto.partner.UpdatePartnerLockDto;
import com.apps.api.dto.partner.UpdateUserRoleDto;
import com.apps.api.dto.system.ResetPasswordDto;
import com.apps.api.entity.SystemUserEntity;
import com.apps.api.enums.PlatformCodeEnum;
import com.apps.api.enums.RoleCodeEnum;
import com.apps.api.feign.RemoteAppsService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.config.RedisService;
import com.partner.constant.RedisConstant;
import com.partner.dto.*;
import com.partner.dto.agent.SwitchPartnerAgent;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.student.StudentApportionAddDto;
import com.partner.dto.student.paramsmapper.StudentApportionAdd;
import com.partner.entity.*;
import com.partner.enums.ConfigTypeEnum;
import com.partner.enums.PartnerErrorEnum;
import com.partner.enums.PartnerRoleEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.*;
import com.partner.service.FinanceService;
import com.partner.service.MPartnerUserService;
import com.partner.service.RPartnerUserAreaCountryService;
import com.partner.util.*;
import com.partner.vo.*;
import com.partner.vo.agent.PartnerAgent;
import com.partner.vo.agent.PartnerUserAgentVo;
import com.partner.vo.my.MPayablePlanMyDetailVo;
import com.partner.vo.student.StudentApportionVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【m_partner_user】的数据库操作Service实现
 * @createDate 2024-12-20 14:13:45
 */
@Service
@AllArgsConstructor
@Slf4j
public class MPartnerUserServiceImpl extends ServiceImpl<MPartnerUserMapper, MPartnerUserEntity>
        implements MPartnerUserService {

    private final MPartnerUserMapper mPartnerUserMapper;
    private final MStudentBaseMapper mStudentBaseMapper;
    private final MStudentOfferItemMapper mStudentOfferItemMapper;

    private final RPartnerUserStudentMapper rPartnerUserStudentMapper;

    private final RAgentUuidMapper rAgentUuidMapper;

    private final RStudentUuidMapper rStudentUuidMapper;
    @Autowired
    private RemoteAppsService appsService;
    private final RPartnerUserAreaCountryService userAreaCountryService;
    private final RPartnerUserSuperiorMapper userSuperiorMapper;

    private final FinanceService financeService;
    private final UExchangeRateMapper uExchangeRateMapper;

    private final SystemUserMapper systemUserMapper;

    private final RPartnerUserAreaCountryService rPartnerUserAreaCountryService;

    private final RPartnerUserAreaCountryMapper rPartnerUserAreaCountryMapper;
    private final PartnerRoleMapper partnerRoleMapper;
    private final PartnerUserPartnerRoleMapper partnerUserRoleMapper;
    @Autowired
    private RedisService redisService;


    @Override
    public List<StudentApportionVo> getApportionPartnerUser() {
        //有学生查看权限的用户都可以分派
        return mPartnerUserMapper.getApportionPartnerUser(UserInfoParamsUtils.getCurrentAgentId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long putStudentApportionAddDto(StudentApportionAddDto dto) {
        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()),
                user.getFkFromPlatformCode(), user.getId());
        String[] uuidArr = dto.getStudentUUIDArr();


        for (String uuid : uuidArr) {
            RStudentUuidEntity rStudentUuidEntity = rStudentUuidMapper.selectByUUID(uuid);
            StudentApportionAdd params = new StudentApportionAdd();
            params.setStudentId(rStudentUuidEntity.getFkStudentId());
            List<RPartnerUserStudentEntity> studentUserlistdb = rPartnerUserStudentMapper.selectList(new LambdaQueryWrapper<RPartnerUserStudentEntity>()
                    .eq(RPartnerUserStudentEntity::getFkStudentId, params.getStudentId())
                    .eq(RPartnerUserStudentEntity::getIsActive, 1)
            );


            if (dto.getType() != null && dto.getType() == 0) {//追加分配
                List<RPartnerUserStudentEntity> listsaveTmp = new ArrayList<>();
                Set<Long> partnerUserSet = new HashSet<>();
                if (ObjectUtil.isNotEmpty(studentUserlistdb)) {
                    partnerUserSet = studentUserlistdb.stream().map(RPartnerUserStudentEntity::getFkPartnerUserId).collect(Collectors.toSet());
                }
                for (Long partUserId : dto.getPartnerUserId()) {
                    if (partnerUserSet.contains(partUserId)) {
                        continue;
                    }
                    RPartnerUserStudentEntity userStudentEntity = new RPartnerUserStudentEntity();
                    userStudentEntity.setFkTenantId(userInfoParams.getTenantId());
                    userStudentEntity.setFkPartnerUserId(partUserId);
                    userStudentEntity.setFkStudentId(params.getStudentId());
                    userStudentEntity.setIsActive(true);
                    userStudentEntity.setActiveDate(LocalDateTime.now());
                    userStudentEntity.setGmtCreateUser(userInfoParams.getPartnerUserId().toString());
                    listsaveTmp.add(userStudentEntity);
                }
                rPartnerUserStudentMapper.insert(listsaveTmp);//批量分配可以添加

            } else if (dto.getType() != null && dto.getType() == 1) {//重新分配

                List<RPartnerUserStudentEntity> listsaveTmp = new ArrayList<>();
                for (Long partUserId : dto.getPartnerUserId()) {
                    RPartnerUserStudentEntity userStudentEntity = new RPartnerUserStudentEntity();
                    userStudentEntity.setFkTenantId(userInfoParams.getTenantId());
                    userStudentEntity.setFkPartnerUserId(partUserId);
                    userStudentEntity.setFkStudentId(params.getStudentId());
                    userStudentEntity.setIsActive(true);
                    userStudentEntity.setActiveDate(LocalDateTime.now());
                    userStudentEntity.setGmtCreateUser(userInfoParams.getPartnerUserId().toString());
                    listsaveTmp.add(userStudentEntity);
                }
                if (ObjectUtil.isNotEmpty(listsaveTmp)) {
                    if (ObjectUtil.isNotEmpty(studentUserlistdb)) {
                        for (RPartnerUserStudentEntity studentUserPo : studentUserlistdb) {
                            studentUserPo.setIsActive(false);
                        }
                        rPartnerUserStudentMapper.updateById(studentUserlistdb);//批量取消再分配
                    }
                    rPartnerUserStudentMapper.insert(listsaveTmp);//批量分配
                }
            }

        }


        return 0l;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveTeamMember(SaveTeamMemberDto saveTeamMemberDto) {
        FzhUser user = SecurityUtils.getUser();
        Long tenantId = Long.parseLong(user.getFkTenantId());
        String loginId = user.getLoginId();


        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()),
                user.getFkFromPlatformCode(), user.getId());

        SavePartnerUserDto partnerUserDto = SavePartnerUserDto.builder()
                .platformCode(user.getFkFromPlatformCode())
                .platformId(Long.parseLong(user.getFkFromPlatformId()))
                .tenantId(tenantId)
                .name(saveTeamMemberDto.getName())
                .email(saveTeamMemberDto.getEmail())
                .roleId(saveTeamMemberDto.getRoleId())
                .createUser(loginId)
                .build();
        log.info("调用apps注册用户:{}", JSONObject.toJSONString(partnerUserDto));
        R<Long> result = appsService.registryUser(partnerUserDto);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            log.error("调用apps注册用户失败:{}", JSONObject.toJSONString(result));
            String errorMsg = RemoteErrorUtil.extractInnerErrorMsg(JSONObject.toJSONString(result));
            throw new PartnerExceptionInfo(PartnerErrorEnum.REGISTER_USER_ERROR.errorCode,
                    PartnerErrorEnum.REGISTER_USER_ERROR.errorMessage +
                            (StringUtils.isBlank(errorMsg) ? "" : ":" + errorMsg));
        }
        Long userId = result.getData();
        //新增partner用户
        MPartnerUserEntity partnerUser = MPartnerUserEntity.builder()
//                .fkTenantId(2L)
                .fkUserId(userId)
                .fkUserLoginId(saveTeamMemberDto.getEmail())
                .name(saveTeamMemberDto.getName())
                .email(saveTeamMemberDto.getEmail())
                .isIdentityChecked(true)
                .isModifiedPs(false)
                .isActive(true)
                .fkAgentId(Objects.nonNull(userInfoParams.getAgentId()) ? userInfoParams.getAgentId() : 0L)
                .fkCompanyId(Objects.nonNull(userInfoParams.getCompanyId()) ? userInfoParams.getCompanyId() : 0L)
                .gmtCreateUser(loginId)
                .gmtCreate(LocalDateTime.now())
                .gmtModifiedUser(loginId)
                .gmtModified(LocalDateTime.now()).build();
        this.save(partnerUser);
        Long partnerUserId = partnerUser.getId();
        //新增国家关联
        if (CollectionUtils.isNotEmpty(saveTeamMemberDto.getCountyIds())) {
            List<RPartnerUserAreaCountryEntity> countryEntityList = saveTeamMemberDto.getCountyIds().stream()
                    .map(countyId -> {
                        RPartnerUserAreaCountryEntity entity = new RPartnerUserAreaCountryEntity();
                        entity.setFkPartnerUserId(partnerUserId);
//                        entity.setFkTenantId(tenantId);
                        entity.setFkAreaCountryId(countyId);
                        entity.setGmtCreateUser(loginId);
                        entity.setGmtCreate(LocalDateTime.now());
                        entity.setGmtModifiedUser(loginId);
                        entity.setGmtModified(LocalDateTime.now());
                        return entity;
                    }).collect(Collectors.toList());
            userAreaCountryService.saveBatch(countryEntityList);
        }
        //新增上下级关联
        if (Objects.nonNull(saveTeamMemberDto.getSuperiorId()) && saveTeamMemberDto.getSuperiorId() > 0L) {
            RPartnerUserSuperiorEntity superior = new RPartnerUserSuperiorEntity();
//            superior.setFkTenantId(tenantId);
            superior.setFkPartnerUserId(partnerUserId);
            superior.setFkPartnerUserIdSuperior(saveTeamMemberDto.getSuperiorId());
            superior.setGmtCreateUser(loginId);
            superior.setGmtCreate(LocalDateTime.now());
            superior.setGmtModifiedUser(loginId);
            superior.setGmtModified(LocalDateTime.now());
            userSuperiorMapper.insert(superior);
        }

        try {
            if (Objects.nonNull(saveTeamMemberDto.getSuperiorId()) && saveTeamMemberDto.getSuperiorId() > 0L
                    && (saveTeamMemberDto.getRoleId().longValue() == 4 || saveTeamMemberDto.getRoleId().longValue() == 16)
                    && Objects.nonNull(partnerUserId)) {
                //todo a()
                rPartnerUserAreaCountryService.deleteSupCache(partnerUserId);
            }
        } catch (Exception e) {
            log.error("清理上级缓存失败:{}", e.getMessage());
        }
        return partnerUserId;
    }

    @Override
    @DSTransactional
    public Long updateTeamCountry(SaveOrUpdateTeamDto saveTeamMemberDto) {

        FzhUser fzhUser = SecurityUtils.getUser();
        String loginId = fzhUser.getLoginId();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        if (Objects.isNull(userinfo.getUserId()) || Objects.isNull(userinfo.getRoleCode())) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":权限不足!");
        }

        MPartnerUserEntity partnerUserEntity = mPartnerUserMapper.selectOne(new LambdaQueryWrapper<MPartnerUserEntity>()
                        .eq(MPartnerUserEntity::getFkUserId, saveTeamMemberDto.getUserId())
                , false
        );


        if (ObjectUtil.isNotEmpty(partnerUserEntity)) {
            List<RPartnerUserAreaCountryEntity> rPartnerUserCountryList =
                    rPartnerUserAreaCountryMapper.selectList(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                            .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUserEntity.getId())
                    );
            if (CollectionUtils.isNotEmpty(rPartnerUserCountryList)) {
                List<Long> ids = rPartnerUserCountryList.stream().map(RPartnerUserAreaCountryEntity::getId).collect(Collectors.toList());
                rPartnerUserAreaCountryMapper.deleteByIds(ids);
            }
            if (CollectionUtils.isNotEmpty(saveTeamMemberDto.getCountryIds())) {
                List<RPartnerUserAreaCountryEntity> countryEntityList = saveTeamMemberDto.getCountryIds().stream()
                        .map(countyId -> {
                            RPartnerUserAreaCountryEntity entity = new RPartnerUserAreaCountryEntity();
                            entity.setFkPartnerUserId(partnerUserEntity.getId());
                            entity.setFkAreaCountryId(countyId);
                            entity.setGmtCreateUser(loginId);
                            entity.setGmtCreate(LocalDateTime.now());
                            entity.setGmtModifiedUser(loginId);
                            entity.setGmtModified(LocalDateTime.now());
                            return entity;
                        }).collect(Collectors.toList());
                rPartnerUserAreaCountryMapper.insert(countryEntityList);
            }

            UserInfoParamsUtils.deleteUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), saveTeamMemberDto.getUserId());

        }


        return 0l;
    }


    @Override
    public List<TeamMemberVo> getTeamMemberList(Integer year, Boolean initRoleName) {
        year = DateUtil.getYearOrCurrentYear(year);
        List<MPartnerUserEntity> partnerUserList = new ArrayList<>();
        FzhUser user = SecurityUtils.getUser();
        Long tenantId = Long.parseLong(user.getFkTenantId());
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(tenantId, user.getFkFromPlatformCode(), user.getId());
        //管理员、企业负责人看到的成员是同一个代理ID下的全部账号
        if (Arrays.asList(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.DIRECTOR.getCode())
                .contains(userInfoParams.getRoleCode())) {
            List<MPartnerUserEntity> userEntityList = mPartnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>()
                    .eq(MPartnerUserEntity::getFkAgentId, userInfoParams.getAgentId())
                    .orderByAsc(MPartnerUserEntity::getId));
            if (CollectionUtils.isNotEmpty(userEntityList)) {
                partnerUserList.addAll(userEntityList);
            }
            return createTeamMemberList(year, partnerUserList, initRoleName);
        }
        //顾问角色、文案专员-看到直属上司和他自己和他的下属
        //上司
        RPartnerUserSuperiorEntity superior = userSuperiorMapper.selectOne(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                .eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, userInfoParams.getPartnerUserId()));
        if (Objects.nonNull(superior)) {
            partnerUserList.add(mPartnerUserMapper.selectById(superior.getFkPartnerUserIdSuperior()));
        }
        partnerUserList.add(mPartnerUserMapper.selectById(userInfoParams.getPartnerUserId()));
        //下属
        List<Long> subordinatePartnerUserIds = userSuperiorMapper.selectChildList(userInfoParams.getPartnerUserId());
        if (CollectionUtils.isNotEmpty(subordinatePartnerUserIds)) {
            partnerUserList.addAll(mPartnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>()
                    .in(MPartnerUserEntity::getId, subordinatePartnerUserIds)
            ));
        }
        return createTeamMemberList(year, partnerUserList, initRoleName);
    }

    @Override
    public List<TeamMemberDetailVo> getTeamMemberDetailList(TeamDataSumDto params) {
        List<TeamMemberDetailVo> result = new ArrayList<>();
        List<TeamMemberVo> teamMemberList = getTeamMemberList(params.getYear(), true);

        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());
        Map<Long, SystemUserEntity> userMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(teamMemberList)) {
            Set<Long> userids = teamMemberList.stream().map(TeamMemberVo::getUserId).collect(Collectors.toSet());
            List<SystemUserEntity> systemUserList = systemUserMapper.selectBatchIds(userids);
            if (ObjectUtil.isNotEmpty(systemUserList)) {
                userMap = systemUserList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
            }
        }

        for (TeamMemberVo teamMemberVo : teamMemberList) {

            params.setRoleFlag(true);


            params.setAgentId(userinfo.getAgentId());

            List<Long> levelPartnerUserIdsTmp = new ArrayList<>();
            levelPartnerUserIdsTmp.add(teamMemberVo.getPartnerUserId());
            params.setRoleCode(teamMemberVo.getRoleCode()); //
            params.setPartnerUserId(teamMemberVo.getPartnerUserId());
            params.setLevelPartnerUserIds(levelPartnerUserIdsTmp);//只查自己

            TeamMemberDetailVo tmp = BeanCopyUtils.objClone(teamMemberVo, TeamMemberDetailVo::new);
            if (userMap.containsKey(teamMemberVo.getUserId())) {
                SystemUserEntity userEntity = userMap.get(teamMemberVo.getUserId());
                tmp.setWechatIconUrl(userEntity.getWechatIconUrl());
            }


            List<Long> levelCompleteTotal = mStudentOfferItemMapper.getLevelCompleteStudens(params);//完成用户数量
            tmp.setDealNum(levelCompleteTotal.size());

            List<Long> followTotal = mStudentBaseMapper.getStudentsLevelCount(params);//跟进中学生数量
            if (ObjectUtil.isNotEmpty(followTotal)) {
                if (ObjectUtil.isNotEmpty(levelCompleteTotal)) {
                    followTotal.removeAll(levelCompleteTotal);//去除已经完成的用户数量
                    tmp.setFollowStudentNum(followTotal.size());
                } else {
                    tmp.setFollowStudentNum(followTotal.size());
                }

            } else {
                tmp.setFollowStudentNum(0);
            }


            //成交金额
            BigDecimal transactionAmount = new BigDecimal(0);
            List<MPayablePlanMyDetailVo> planAmountArr = mPartnerUserMapper.getSelfAmount(params);
            if (ObjectUtil.isNotEmpty(planAmountArr)) {
                List<MPayablePlanVo> resultlist = getConvAmount(planAmountArr, userinfo);
                for (MPayablePlanVo planVo : resultlist) {
                    transactionAmount = transactionAmount.add(planVo.getToPayableAmount());
                }
            }
            tmp.setDealAmount(transactionAmount);
            result.add(tmp);
        }


        return result;
    }

    @Override
    public void lockUser(UpdatePartnerLockDto lockDto) {
        log.info("调用apps停用启用用户,参数:{}", JSONObject.toJSONString(lockDto));
        R r = appsService.lockUser(lockDto);
        if (!r.isSuccess()) {
            log.error("调用apps停用启用用户失败,参数:{}", JSONObject.toJSONString(lockDto));
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage);
        }
        //修改用户
        MPartnerUserEntity partnerUser = mPartnerUserMapper.selectOne(new LambdaQueryWrapper<MPartnerUserEntity>().eq(MPartnerUserEntity::getFkUserId, lockDto.getUserId()));
        if (Objects.nonNull(partnerUser)) {
            //停用
            if (lockDto.getLockFlag().equals(1)) {
                partnerUser.setIsActive(Boolean.FALSE);
            } else {
                partnerUser.setIsActive(Boolean.TRUE);
            }
            partnerUser.setGmtModified(LocalDateTime.now());
            partnerUser.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
            mPartnerUserMapper.updateById(partnerUser);
        }


    }

    @Override
    public List<TeamMemberTreeVo> getTeamMemberTree(Integer year) {
        List<TeamMemberTreeVo> list = new ArrayList<>();
        //排序-管理员-企业负责人-财务-顾问-文案专员
        List<TeamMemberVo> teamMemberList = getTeamMemberList(year, true);
        //管理员
        List<TeamMemberVo> adminList = filterTeamMember(teamMemberList, RoleCodeEnum.ADMIN.getCode());
        if (CollectionUtils.isNotEmpty(adminList)) {
            list.addAll(initTeamMemberTree(adminList));
        }
        //企业负责人
        List<TeamMemberVo> directorList = filterTeamMember(teamMemberList, RoleCodeEnum.DIRECTOR.getCode());
        if (CollectionUtils.isNotEmpty(directorList)) {
            list.addAll(initTeamMemberTree(directorList));
        }
        //财务
        List<TeamMemberVo> financeList = filterTeamMember(teamMemberList, RoleCodeEnum.FINANCE.getCode());
        if (CollectionUtils.isNotEmpty(financeList)) {
            list.addAll(initTeamMemberTree(financeList));
        }
        //顾问
        // 将 adminList 和 financeList 转为 Set 提高性能
        Set<TeamMemberVo> adminSet = new HashSet<>(adminList);
        Set<TeamMemberVo> financeSet = new HashSet<>(financeList);
        Set<TeamMemberVo> directorSet = new HashSet<>(directorList);
        List<TeamMemberVo> consultantList = teamMemberList.stream()
                .filter(teamMember ->
                        !adminSet.contains(teamMember) && !financeSet.contains(teamMember)
                                && !directorSet.contains(teamMember))
                .collect(Collectors.toList());

        // 用来存储已经处理过的顾问ID，避免重复处理
        Set<Long> processedIds = new HashSet<>();
        consultantList.sort(Comparator.comparing(TeamMemberVo::getPartnerUserId));
        for (TeamMemberVo consultant : consultantList) {
            // 检查当前顾问是否已经处理过
            if (processedIds.contains(consultant.getPartnerUserId())) {
                continue;
            }
            //查询顾问的下级
            TeamMemberTreeVo userTree = new TeamMemberTreeVo(consultant.getUserId(), consultant.getPartnerUserId(), consultant.getName(), consultant.getRoleName(), consultant.getRoleCode());
            // 查询该顾问的下级
            List<TeamMemberTreeVo> subordinates = getSubordinates(consultant.getPartnerUserId(), teamMemberList);
            userTree.setChildren(subordinates);
            // 标记当前顾问已处理
            processedIds.addAll(getAllPartnerUserIdsIteratively(userTree));
            list.add(userTree);

        }
        return list;
    }

    @Override
    public List<TeamMemberVo> getConsultantList() {
        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()), user.getFkFromPlatformCode(), user.getId());
        R<List<Long>> platformUserByRole = appsService.getPlatformUserByRole(Long.parseLong(user.getFkFromPlatformId()),
                PlatformCodeEnum.PARTNER.getCode(),
                Arrays.asList(RoleCodeEnum.COUNSELOR.getCode(), RoleCodeEnum.COPYWRINTING.getCode()));
        if (!platformUserByRole.isSuccess()) {
            log.error("调用apps获取顾问列表失败,参数:{}", user.getFkFromPlatformId());
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage);
        }
        return mPartnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>()
                        .eq(MPartnerUserEntity::getFkAgentId, userInfoParams.getAgentId())
                        .in(MPartnerUserEntity::getFkUserId, platformUserByRole.getData()))
                .stream().map(partnerUserEntity -> {
                    TeamMemberVo teamMemberVo = new TeamMemberVo();
                    teamMemberVo.setPartnerUserId(partnerUserEntity.getId());
                    teamMemberVo.setName(partnerUserEntity.getName());
                    return teamMemberVo;
                }).collect(Collectors.toList());
    }


    private List<TeamMemberVo> createTeamMemberList(Integer year, List<MPartnerUserEntity> partnerUserList, Boolean initRoleName) {
        Long currentUserId = SecurityUtils.getUser().getId();
        Map<Long, Map<String, String>> roleNameMap;
        //去重
        partnerUserList = partnerUserList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        MPartnerUserEntity::getId,
                        teamMember -> teamMember,
                        (existing, replacement) -> existing)).values().stream()
                .sorted(
                        Comparator.comparing((MPartnerUserEntity user) ->
                                Objects.equals(user.getFkUserId(), currentUserId) ? 0 : 1
                        ).thenComparing(
                                MPartnerUserEntity::getGmtCreate,
                                Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

        if (initRoleName) {
            List<Long> userIds = partnerUserList.stream().map(MPartnerUserEntity::getFkUserId).distinct().collect(Collectors.toList());
            log.info("调用apps获取用户角色,参数:{}", userIds);
            long startTime = System.currentTimeMillis();
            R<Map<Long, Map<String, String>>> roleNaMapR = appsService.getUserRoleNameList(userIds);
            // 记录结束时间并计算耗时
            log.info("调用apps获取用户角色完成,花费时间: {}ms", System.currentTimeMillis() - startTime);
            if (!roleNaMapR.isSuccess()) {
                log.error("调用apps获取用户角色失败,参数:{}", userIds);
                throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage);
            }
            roleNameMap = roleNaMapR.getData();
        } else {
            roleNameMap = new HashMap<>();
        }

        return partnerUserList.stream().map(user -> {
            TeamMemberVo teamMemberVo = new TeamMemberVo();
            teamMemberVo.setPartnerUserId(user.getId());
            teamMemberVo.setUserId(user.getFkUserId());
            teamMemberVo.setName(user.getName());
            teamMemberVo.setLoginId(user.getFkUserLoginId());
            teamMemberVo.setGmtCreate(user.getGmtCreate());
            teamMemberVo.setIsYearCreate(false);
            // 设置是否为当年创建
            if (Objects.nonNull(user.getGmtCreate())) {
                int createYear = user.getGmtCreate().getYear();
                teamMemberVo.setIsYearCreate(createYear == year);
            }
            if (initRoleName) {
                teamMemberVo.setRoleName(Objects.nonNull(roleNameMap.get(user.getFkUserId()))
                        ? roleNameMap.get(user.getFkUserId()).get("roleName") : "");
                teamMemberVo.setRoleCode(Objects.nonNull(roleNameMap.get(user.getFkUserId()))
                        ? roleNameMap.get(user.getFkUserId()).get("roleCode") : "");
                teamMemberVo.setLockFlag(Objects.nonNull(roleNameMap.get(user.getFkUserId()))
                        ? Integer.parseInt(roleNameMap.get(user.getFkUserId()).get("lockFlag")) : 0);
            }

            return teamMemberVo;
        }).collect(Collectors.toList());
    }

    private List<TeamMemberTreeVo> getSubordinates(Long superiorId, List<TeamMemberVo> teamMemberList) {
        // 查询该用户的直接下级
        List<RPartnerUserSuperiorEntity> relations = userSuperiorMapper.selectList(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                .eq(RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior, superiorId));

        List<TeamMemberTreeVo> subordinates = new ArrayList<>();

        // 如果有下级，递归查询
        for (RPartnerUserSuperiorEntity relation : relations) {
            Long partnerUserId = relation.getFkPartnerUserId();
            // 获取下级用户的基本信息
            TeamMemberVo teamMember = getTeamMemberByPartnerUserId(partnerUserId, teamMemberList);
            if (Objects.nonNull(teamMember)) {
                // 组装下级信息
                TeamMemberTreeVo subordinateTree = new TeamMemberTreeVo(teamMember.getUserId(), teamMember.getPartnerUserId(), teamMember.getName(), teamMember.getRoleName(), teamMember.getRoleCode());
                // 递归查询该下级的下级
                subordinateTree.setChildren(getSubordinates(partnerUserId, teamMemberList));
                subordinates.add(subordinateTree);
            }
        }
        return subordinates;
    }

    private TeamMemberVo getTeamMemberByPartnerUserId(Long partnerUserId, List<TeamMemberVo> teamMemberList) {
        return teamMemberList.stream()
                .filter(teamMember -> teamMember.getPartnerUserId().equals(partnerUserId))
                .findFirst()
                .orElse(null);
    }

    private List<TeamMemberVo> filterTeamMember(List<TeamMemberVo> list, String roleCode) {
        List<TeamMemberVo> userList = list.stream()
                .filter(teamMember ->
                        StringUtils.isNoneBlank(teamMember.getRoleCode()) &&
                                Arrays.asList(teamMember.getRoleCode().split(",")).contains(roleCode))
                .sorted(Comparator.comparing(TeamMemberVo::getPartnerUserId))
                .collect(Collectors.toList());
        if (userList.isEmpty()) {
            return new ArrayList<>();
        }
        return userList;
    }

    private List<TeamMemberTreeVo> initTeamMemberTree(List<TeamMemberVo> userList) {
        return userList.stream().map(user ->
                        new TeamMemberTreeVo(user.getUserId(), user.getPartnerUserId(), user.getName(), user.getRoleName(), user.getRoleCode()))
                .collect(Collectors.toList());
    }

    public Set<Long> getAllPartnerUserIdsIteratively(TeamMemberTreeVo userTree) {
        Set<Long> partnerUserIds = new HashSet<>();
        // 用于缓存已访问的节点
        Set<Long> visitedIds = new HashSet<>();
        // 使用栈模拟递归过程
        Deque<TeamMemberTreeVo> stack = new ArrayDeque<>();
        // 将根节点压入栈
        stack.push(userTree);

        while (!stack.isEmpty()) {
            TeamMemberTreeVo currentNode = stack.pop();
            // 如果当前节点已访问过，则跳过
            if (visitedIds.contains(currentNode.getPartnerUserId())) {
                continue;
            }
            // 添加当前节点的 partnerUserId
            partnerUserIds.add(currentNode.getPartnerUserId());
            // 标记当前节点为已访问
            visitedIds.add(currentNode.getPartnerUserId());
            // 将所有子节点压入栈中
            if (currentNode.getChildren() != null) {
                for (TeamMemberTreeVo child : currentNode.getChildren()) {
                    stack.push(child);
                }
            }
        }

        return partnerUserIds;
    }

    @Override
    public List<Map<String, Object>> getRoleList() {
        FzhUser user = SecurityUtils.getUser();
        R<List<Map<String, Object>>> roleList = appsService.getRoleList(Long.parseLong(user.getFkFromPlatformId()), user.getFkFromPlatformCode());
        if (!roleList.isSuccess()) {
            log.error("获取角色列表失败,FromPlatformId:{},FromPlatformCode:{}", user.getFkFromPlatformId(), user.getFkFromPlatformCode());
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage);
        }
        return roleList.getData();
    }

    @Override
    public TeamDataSumVo getTeamDataSum(TeamDataSumDto params) {
        TeamDataSumVo result = new TeamDataSumVo();

        /*   TeamDataSumDto params=new TeamDataSumDto(); //查询参数*/

        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());
        BeanCopyUtils.copyProperties(userinfo, params);
        //long time1=new Date().getTime();
        int peoplecount = mPartnerUserMapper.getTeamPeopleCount(params);

        int num = userSuperiorMapper.selectSuper(params);
        if (num > 0) {
            peoplecount = peoplecount + num;//上司
        }


        int studentcount = mStudentBaseMapper.getStudentsCount(params);


        int complete = mStudentOfferItemMapper.getCompleteStudens(params);

        //BigDecimal allAmount=mPartnerUserMapper.getAllAmount(params);
        List<MPayablePlanMyDetailVo> planAmountArr = mPartnerUserMapper.getAgentMPayablePlan(params);

        BigDecimal allAmount = new BigDecimal(0);
        if (ObjectUtil.isNotEmpty(planAmountArr)) {
            List<MPayablePlanVo> resultlist = getConvAmount(planAmountArr, userinfo);
            for (MPayablePlanVo planVo : resultlist) {
                allAmount = allAmount.add(planVo.getToPayableAmount());
            }
        }
        result.setTramPeopleTotal(peoplecount);
        result.setStudentTotal(studentcount);
        result.setCompleteStudentTotal(complete);
        if (peoplecount != 0) {

            Double avgnumD = (double) complete / peoplecount;
            String formattedAvgNum = String.format("%.2f", avgnumD);
            result.setAvgCompleteTotal(formattedAvgNum);
        }


        BigDecimal bpeopleCount = new BigDecimal(peoplecount);
        if (ObjectUtil.isNotEmpty(allAmount)) {
            if (peoplecount != 0) {
                result.setAvgCompleteAmount(allAmount.divide(bpeopleCount, 2, BigDecimal.ROUND_HALF_UP));
            }
        } else {
            result.setAvgCompleteAmount(new BigDecimal(0));
        }


        return result;
    }

    @Override
    public Boolean hasTeamPermission() {
        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()), user.getFkFromPlatformCode(), user.getId());
        //管理员、企业负责人有权限
        if (Arrays.asList(RoleCodeEnum.ADMIN.getCode(), RoleCodeEnum.DIRECTOR.getCode())
                .contains(userInfoParams.getRoleCode())) {
            return Boolean.TRUE;
        }
        //财务没有权限
        if (Arrays.asList(RoleCodeEnum.FINANCE.getCode()).contains(userInfoParams.getRoleCode())) {
            return Boolean.TRUE;
        }
        //顾问、文案专员要有下属才有权限
        if (Arrays.asList(RoleCodeEnum.COUNSELOR.getCode(), RoleCodeEnum.COPYWRINTING.getCode())
                .contains(userInfoParams.getRoleCode())) {
            //下属
            List<Long> subordinatePartnerUserIds = userSuperiorMapper.selectChildList(userInfoParams.getPartnerUserId());
            if (CollectionUtils.isNotEmpty(subordinatePartnerUserIds)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public void resetPartnerPassword(ResetPartnerPasswordDto partnerPasswordDto) {
        MPartnerUserEntity partnerUserEntity = mPartnerUserMapper.selectOne(new LambdaQueryWrapper<MPartnerUserEntity>()
                .eq(MPartnerUserEntity::getEmail, partnerPasswordDto.getEmail())
                .eq(MPartnerUserEntity::getName, partnerPasswordDto.getName()));
        if (ObjectUtil.isEmpty(partnerUserEntity)) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.USER_INFO_ERROR.errorCode, PartnerErrorEnum.USER_INFO_ERROR.errorMessage);
        }
        R<Boolean> resetResult = appsService.resetPartnerUserPassword(new ResetPasswordDto(partnerPasswordDto.getEmail()));
        if (!resetResult.isSuccess()) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage);
        }
        if (!resetResult.getData()) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.RESET_USER_PASSWORD_ERROR.errorCode, PartnerErrorEnum.RESET_USER_PASSWORD_ERROR.errorMessage);
        }
    }

    /**
     * 我的团队-修改团队成员
     *
     * @param memberDto
     * @return
     */
    @Override
    @DSTransactional
    public void updateTeamMember(UpdateTeamMemberDto memberDto) {
        FzhUser fzhUser = SecurityUtils.getUser();
        Long partnerUserId = memberDto.getPartnerUserId();

        // 获取角色
        // 通过ID获取MPartnerUserEntity
        MPartnerUserEntity partnerUser = this.mPartnerUserMapper.selectById(partnerUserId);
        if (ObjectUtil.isNull(partnerUser)) {
            log.error("修改团队成员失败，用户不存在");
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":修改团队成员失败，用户不存在!");
        }
        // 伙伴id
        Long userId = partnerUser.getFkUserId();
        // 3. 通过远程服务调用获取角色信息
        R<Map<Long, Map<String, String>>> roleNaMapR = appsService.getUserRoleNameList(Collections.singletonList(userId));
        if (!roleNaMapR.isSuccess()) {
            log.error("调用apps获取用户角色失败,参数:{}", userId);
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage);
        }
        // 4. 从返回结果中提取角色信息
        Map<Long, Map<String, String>> roleNameMap = roleNaMapR.getData();
        // 角色代码
        String roleCode = roleNameMap.get(userId).get("roleCode");
        if (StrUtil.isBlank(roleCode)) {
            log.error("修改团队成员失败，用户角色不存在");
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":修改团队成员失败，用户角色不存在!");
        }

        // 文案专员和顾问处理
        if (RoleCodeEnum.isCounselorOrCopywriter(roleCode)) {
            // 查询用户的下级ID列表
            List<Long> subordinatePartnerUserIds = userSuperiorMapper.selectChildList(partnerUserId);
            // 判断是否有下级
            boolean hasSubordinates = CollectionUtils.isNotEmpty(subordinatePartnerUserIds);
            if (hasSubordinates) {
                log.error("当前账号存在下属，请解除下属关系后再进行变更。");
                throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":当前账号存在下属，请解除下属关系后再进行变更。");
            }
        }
        // 修改角色DTO创建
        UpdateUserRoleDto updateUserRoleDto = new UpdateUserRoleDto();
        updateUserRoleDto.setCreateUser(fzhUser.getLoginId());
        updateUserRoleDto.setUserId(userId);
        updateUserRoleDto.setRoleId(memberDto.getRoleId());
        // 修改角色并删除缓存
        log.info("调用apps修改用户角色: {}", userId);
        R r = this.appsService.updateUserRole(updateUserRoleDto);
        if (!r.isSuccess()) {
            log.error("调用apps修改用户角色失败,参数:{}", userId);
            String errorMsg = RemoteErrorUtil.extractInnerErrorMsg(JSONObject.toJSONString(r));
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage + ":" + errorMsg);
        }

        // 文案专员和顾问处理
        if (RoleCodeEnum.isCounselorOrCopywriter(roleCode)) {
            // 如果提供了上司ID，则创建新的上司关系
            if (memberDto.getSuperiorId() != null) {
                LambdaQueryWrapper<RPartnerUserSuperiorEntity> rPartnerUserSuperiorEntityLambdaQueryWrapper = new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                        .eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, partnerUser.getId());
                RPartnerUserSuperiorEntity rPartnerUserSuperiorEntity = this.userSuperiorMapper.selectOne(rPartnerUserSuperiorEntityLambdaQueryWrapper);
                if (rPartnerUserSuperiorEntity != null) {
                    // 先删除旧的上司关系
                    this.userSuperiorMapper.delete(rPartnerUserSuperiorEntityLambdaQueryWrapper);
                }
                // 插入新数据
                RPartnerUserSuperiorEntity superiorEntity = new RPartnerUserSuperiorEntity();
                superiorEntity.setFkPartnerUserId(partnerUser.getId());
                superiorEntity.setFkPartnerUserIdSuperior(memberDto.getSuperiorId());
                superiorEntity.setGmtCreateUser(fzhUser.getLoginId());
                superiorEntity.setGmtCreate(LocalDateTime.now());
                userSuperiorMapper.insert(superiorEntity);
            }
            // 修改跟进国家
            if (CollectionUtils.isNotEmpty(memberDto.getCountyIds())) {

                List<RPartnerUserAreaCountryEntity> rPartnerUserCountryList =
                        rPartnerUserAreaCountryMapper.selectList(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                                .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUserId)
                        );
                if (CollectionUtils.isNotEmpty(rPartnerUserCountryList)) {
                    List<Long> ids = rPartnerUserCountryList.stream().map(RPartnerUserAreaCountryEntity::getId).collect(Collectors.toList());
                    rPartnerUserAreaCountryMapper.deleteByIds(ids);
                }
                if (CollectionUtils.isNotEmpty(memberDto.getCountyIds())) {
                    List<RPartnerUserAreaCountryEntity> countryEntityList = memberDto.getCountyIds().stream()
                            .map(countyId -> {
                                RPartnerUserAreaCountryEntity entity = new RPartnerUserAreaCountryEntity();
                                entity.setFkPartnerUserId(partnerUserId);
                                entity.setFkAreaCountryId(countyId);
                                entity.setGmtCreateUser(fzhUser.getLoginId());
                                entity.setGmtCreate(LocalDateTime.now());
                                entity.setGmtModifiedUser(fzhUser.getLoginId());
                                entity.setGmtModified(LocalDateTime.now());
                                return entity;
                            }).collect(Collectors.toList());
                    rPartnerUserAreaCountryMapper.insert(countryEntityList);
                }
            }
            if (memberDto.getIsViewCommission() != null) {
                // 修改是否查看佣金权限
                MPartnerUserEntity updateEntity = new MPartnerUserEntity();
                updateEntity.setId(partnerUser.getId());
//                updateEntity.setIsViewCommission(memberDto.getIsViewCommission());
                this.updateById(updateEntity);
            }
        }

        // 删除伙伴用户缓存
        UserInfoParamsUtils.deleteUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), userId);
        // 删除用户缓存并下线
        log.info("调用apps删除用户缓存 : {}", userId);
        R delUserCacheR = this.appsService.delUserCache(userId);
        if (!delUserCacheR.isSuccess()) {
            log.error("调用apps删除用户缓存,参数:{}", userId);
            String errorMsg = RemoteErrorUtil.extractInnerErrorMsg(JSONObject.toJSONString(r));
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage + ":" + errorMsg);
        }
    }

    @Override
    public Boolean hasViewCommission() {
        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()), user.getFkFromPlatformCode(), user.getId());
        String roleCode = userInfoParams.getRoleCode();
        RoleCodeEnum roleEnum = RoleCodeEnum.getRoleCodeEnumByCode(roleCode);
        if (Objects.isNull(roleEnum)) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":当前用户角色不存在!");
        }
        switch (roleEnum) {
            case ADMIN:
            case DIRECTOR:
            case FINANCE:
                return Boolean.TRUE;
            case COUNSELOR:
            case COPYWRINTING:
                return this.validateViewCommission(userInfoParams.getPartnerUserId());
            default:
                return Boolean.FALSE;
        }
    }

    /**
     * 查看成员信息
     *
     * @param partnerUserId
     * @return
     */
    @Override
    public TeamDetailVo getTeamDetailByPartnerUserId(Long partnerUserId) {
        TeamDetailVo teamDetailVo = new TeamDetailVo();

        // 获取角色
        log.info("调用apps查询用户角色id : {}", partnerUserId);
        MPartnerUserEntity partnerUser = this.mPartnerUserMapper.selectById(partnerUserId);
        if (ObjectUtil.isNull(partnerUser)) {
            log.error("修改团队成员失败，用户不存在");
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":修改团队成员失败，用户不存在!");
        }
        R<Long> roleIdByUserIdR = this.appsService.getRoleIdByUserId(partnerUser.getFkUserId());
        if (!roleIdByUserIdR.isSuccess()) {
            log.info("调用apps查询用户角色id失败, 参数是 : {}", partnerUserId);
            String errorMsg = RemoteErrorUtil.extractInnerErrorMsg(JSONObject.toJSONString(roleIdByUserIdR));
            throw new PartnerExceptionInfo(PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorCode, PartnerErrorEnum.FEIGN_SERVICE_ERROR.errorMessage + " : " + errorMsg);
        }
        Long roleId = roleIdByUserIdR.getData();
        teamDetailVo.setRoleId(roleId);
        // 文案专员和顾问处理
        if (PartnerRoleEnum.isCounselorOrCopywriter(roleId)) {
            // 查询用户的下级ID列表
            List<Long> subordinatePartnerUserIds = userSuperiorMapper.selectChildList(partnerUserId);
            // 判断是否有下级
            boolean hasSubordinates = CollectionUtils.isNotEmpty(subordinatePartnerUserIds);
            teamDetailVo.setHasSubordinates(hasSubordinates);
        }

        // 团队基本信息查询
        LambdaQueryWrapper<MPartnerUserEntity> partnerUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<MPartnerUserEntity>()
                .eq(MPartnerUserEntity::getId, partnerUserId);
        MPartnerUserEntity mPartnerUserEntity = this.getOne(partnerUserEntityLambdaQueryWrapper);
        if (Objects.isNull(mPartnerUserEntity)) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.USER_INFO_ERROR.errorCode, PartnerErrorEnum.USER_INFO_ERROR.errorMessage + " : 找不到成员用户");
        }
        BeanCopyUtils.copyProperties(mPartnerUserEntity, teamDetailVo);

        // 上司
        LambdaQueryWrapper<RPartnerUserSuperiorEntity> rPartnerUserSuperiorEntityLambdaQueryWrapper = new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                .eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, partnerUserId);
        RPartnerUserSuperiorEntity rPartnerUserSuperiorEntity = this.userSuperiorMapper.selectOne(rPartnerUserSuperiorEntityLambdaQueryWrapper);
        if (ObjectUtils.isNotNull(rPartnerUserSuperiorEntity)) {
            teamDetailVo.setSuperiorId(rPartnerUserSuperiorEntity.getFkPartnerUserIdSuperior());
        }

        // 国家
        LambdaQueryWrapper<RPartnerUserAreaCountryEntity> rPartnerUserAreaCountryEntityLambdaQueryWrapper = new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUserId);
        List<RPartnerUserAreaCountryEntity> countryEntityList = this.rPartnerUserAreaCountryMapper.selectList(rPartnerUserAreaCountryEntityLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(countryEntityList)) {
            List<Long> countryIdList = countryEntityList.stream()
                    .map(RPartnerUserAreaCountryEntity::getFkAreaCountryId)
                    .collect(Collectors.toList());
            teamDetailVo.setCountyIds(countryIdList);
        }
        return teamDetailVo;
    }

    @Override
    public List<PartnerUserAgentVo> getPartnerUserAgentList() {
        FzhUser user = SecurityUtils.getUser();
        //查询当前用户管理的代理列表
        List<PartnerUserAgentVo> agentList = mPartnerUserMapper.selectPartnerUserAgentList(user.getId());
        //填充角色信息
        List<Long> partnerUserIds = agentList.stream().map(PartnerUserAgentVo::getPartnerUserId).collect(Collectors.toList());
        // 1. 批量查询所有绑定关系
        List<PartnerUserPartnerRole> relationList = partnerUserRoleMapper.selectList(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                .in(PartnerUserPartnerRole::getFkPartnerUserId, CollectionUtils.isEmpty(partnerUserIds) ? Arrays.asList(0L) : partnerUserIds)
        );
        // 2. 提取出所有角色ID
        List<Long> roleIds = relationList
                .stream()
                .map(PartnerUserPartnerRole::getFkPartnerRoleId)
                .distinct()
                .collect(Collectors.toList());

        // 3. 查询所有角色详情
        Map<Long, PartnerRole> roleMap = partnerRoleMapper.selectBatchIds(CollectionUtils.isEmpty(roleIds) ? Arrays.asList(0L) : roleIds)
                .stream()
                .collect(Collectors.toMap(PartnerRole::getId, Function.identity()));
        // 4. 构建最终映射 Map<Long, List<PartnerRole>>
        Map<Long, List<PartnerRole>> userRoleMap = relationList.stream()
                .collect(Collectors.groupingBy(
                        PartnerUserPartnerRole::getFkPartnerUserId,
                        Collectors.mapping(rel -> roleMap.get(rel.getFkPartnerRoleId()), Collectors.toList())));

        agentList.stream().forEach(agent -> {
            //角色
            List<PartnerRole> roles = userRoleMap.getOrDefault(agent.getPartnerUserId(), Collections.emptyList());
            agent.setPartnerRoleNames(roles.stream().map(PartnerRole::getRoleName).collect(Collectors.toList()));
            agent.setPartnerRoleIds(roles.stream().map(PartnerRole::getId).collect(Collectors.toList()));
        });
        return agentList;
    }

    @Override
    public Boolean switchPartnerAgent(SwitchPartnerAgent switchPartnerAgent) {
        FzhUser user = SecurityUtils.getUser();
        String uuid = RequestHeaderUtils.getUuidOrThrow(user.getId());
        //清除当前登录的业务缓存信息
        //删除伙伴用户信息缓存
        log.info("============>切换代理,代理信息:{}=========>{}", UserInfoParamsUtils.getCurrentAgentId(), switchPartnerAgent.getAgentId());
        log.info("============>切换代理,伙伴信息:{}=========>{}", UserInfoParamsUtils.getCurrentPartnerUserId(), switchPartnerAgent.getPartnerUserId());
        log.info("删除伙伴用户信息缓存,系统用户ID：{}", user.getId());
        redisService.del(RedisConstant.PARTNER_USERINFO_KEY_PREFIX + user.getId() + "-" + uuid);
        //更新新的伙伴用户和代理的关联的缓存信息
        PartnerAgent agent = PartnerAgent.builder()
                .partnerUserId(switchPartnerAgent.getPartnerUserId())
                .agentId(switchPartnerAgent.getAgentId())
                .systemUserId(user.getId())
                .build();
        redisService.set(RedisConstant.PARTNER_USERINFO_AGENT_KEY_PREFIX + user.getId() + "-" + uuid, JSONObject.toJSONString(agent));
        //重新设置缓存
        UserInfoParamsUtils.getUserInfoParams(Long.valueOf(user.getFkTenantId()), user.getFkFromPlatformCode(), user.getId());
        log.info("============>切换代理,当前用户信息:伙伴用户ID:{}=========>代理ID:{}", UserInfoParamsUtils.getCurrentPartnerUserId(), UserInfoParamsUtils.getCurrentAgentId());
        return Boolean.TRUE;
    }

    @Override
    public PartnerUserAgentVo getCurrentPartnerUserAgent() {
        FzhUser user = SecurityUtils.getUser();
        String uuid = RequestHeaderUtils.getUuidOrThrow(user.getId());
        Object obj = redisService.get(RedisConstant.PARTNER_USERINFO_AGENT_KEY_PREFIX + user.getId() + "-" + uuid);
        if (Objects.isNull(obj)) {
            return null;
        }
        List<PartnerUserAgentVo> agentList = mPartnerUserMapper.selectPartnerUserAgentList(user.getId());
        PartnerUserAgentVo partnerUserAgent = JSONObject.parseObject((String) obj, PartnerUserAgentVo.class);
        Optional<PartnerUserAgentVo> optional = agentList.stream().filter(agent ->
                        agent.getPartnerUserId().equals(agent.getPartnerUserId())
                                && agent.getAgentId().equals(partnerUserAgent.getAgentId()))
                .findFirst();
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    /**
     * 校验当前用户是否有查看佣金权限
     *
     * @param partnerUserId
     * @return
     */
    private Boolean validateViewCommission(Long partnerUserId) {
//        MPartnerUserEntity mPartnerUserEntity = this.mPartnerUserMapper.selectById(partnerUserId);
//        if (ObjectUtils.isNull(mPartnerUserEntity) || ObjectUtils.isNull(mPartnerUserEntity.getIsViewCommission())) {
//            return Boolean.FALSE;
//        }
        return Boolean.FALSE;
//        return mPartnerUserEntity.getIsViewCommission();
    }

    //目标币种转换
    public List<MPayablePlanVo> getConvAmount(List<MPayablePlanMyDetailVo> planAmountArr, UserInfoParams userinfo) {
        List<MPayablePlanVo> result = new ArrayList<>();
        //默认币种-目标币种
        String defaultCurrency = financeService.getCurrencyDefault(userinfo.getCompanyId(), "PARTNER", "PARTNER_COMPANY_DEFAULT_CURRENCY");
        if (ObjectUtil.isNotEmpty(planAmountArr) && StringUtils.isNoneBlank(defaultCurrency)) {
            for (MPayablePlanMyDetailVo planEntity : planAmountArr) {
                MPayablePlanVo planVo = BeanCopyUtils.objClone(planEntity, MPayablePlanVo::new);
                String formcurrencyTypenum = planEntity.getFkCurrencyTypeNum();
                if (StringUtils.isNoneBlank(formcurrencyTypenum)) {
                    if (formcurrencyTypenum.equals(defaultCurrency)) {

                        if (planEntity.getFenpeiNum() != 0 && planEntity.getFenpeiNum() > 0 && planEntity.getPayableAmount() != null &&
                                planEntity.getPayableAmount().compareTo(BigDecimal.ZERO) != 0) {

                            BigDecimal avgPayableAmout = planEntity.getPayableAmount()
                                    .divide(new BigDecimal(planEntity.getFenpeiNum()), 2, RoundingMode.HALF_UP
                                    );
                            planVo.setToPayableAmount(avgPayableAmout);

                        } else {
                            planVo.setToPayableAmount(planEntity.getPayableAmount());
                        }
                        result.add(planVo);
                        continue;
                    }
                    BigDecimal exchangeRate_redis = UserInfoParamsUtils.getExchangeRate(ConfigTypeEnum.M_EXCHANGERATE_HASH.uNewsType,
                            formcurrencyTypenum, defaultCurrency);
                    if (exchangeRate_redis.compareTo(BigDecimal.ONE) == 0) {
                        //获取汇率
                        UExchangeRateEntity entityWebExchange = financeService.getLastRate(formcurrencyTypenum, defaultCurrency);
                        if (ObjectUtil.isNotEmpty(entityWebExchange) && ObjectUtil.isEmpty(entityWebExchange.getId())) {
                            entityWebExchange.setGetDate(LocalDate.now());
                            entityWebExchange.setGmtCreateUser(userinfo.getPartnerUserId().toString());
                            uExchangeRateMapper.insert(entityWebExchange);
                        }
                        BigDecimal exchangeRate = entityWebExchange.getExchangeRate();
                        UserInfoParamsUtils.setExchangeRate(ConfigTypeEnum.M_EXCHANGERATE_HASH.uNewsType,
                                formcurrencyTypenum, defaultCurrency, exchangeRate);
                        planVo.setToPayableAmount(planEntity.getPayableAmount().multiply(exchangeRate));
                    } else {
                        planVo.setToPayableAmount(planEntity.getPayableAmount().multiply(exchangeRate_redis));
                    }
                }

                if (planEntity.getFenpeiNum() != 0 && planEntity.getFenpeiNum() > 0 && planVo.getToPayableAmount() != null &&
                        planVo.getToPayableAmount().compareTo(BigDecimal.ZERO) != 0) {

                    BigDecimal avgtoPayableAmout = planVo.getToPayableAmount()
                            .divide(new BigDecimal(planEntity.getFenpeiNum()), 2, RoundingMode.HALF_UP
                            );
                    planVo.setToPayableAmount(avgtoPayableAmout);
                }
                result.add(planVo);
            }
        }
        return result;
    }


}




