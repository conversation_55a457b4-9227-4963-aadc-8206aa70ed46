package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-24 14:54:41
 */

@Data
@TableName("m_student")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_student ")
public class MStudentEntity extends Model<MStudentEntity>{

  @Schema(description = "学生Id")
  private Long id;
 

  @Schema(description = "公司Id")
  private Long fkCompanyId;
 

  @Schema(description = "学生资源Id")
  private Long fkClientId;
 

  @Schema(description = "学生编号")
  private String num;
 

  @Schema(description = "学生姓名（中）")
  private String name;
 

  @Schema(description = "姓（英/拼音）")
  private String lastName;
 

  @Schema(description = "名（英/拼音）")
  private String firstName;
 

  @Schema(description = "性别")
  private String gender;
 

  @Schema(description = "生日")
  @JsonFormat(pattern = "yyyy-MM-dd")
  @DateTimeFormat(
          pattern = "yyyy-MM-dd"
  )
  private LocalDate birthday;
 

  @Schema(description = "学生国籍所在国家Id")
  private Long fkAreaCountryIdNationality;
 

  @Schema(description = "学生国籍所在国家名称")
  private String fkAreaCountryNameNationality;
 

  @Schema(description = "绿卡国家Id")
  private Long fkAreaCountryIdGreenCard;
 

  @Schema(description = "护照编号（保险业务必填）")
  private String passportNum;
 

  @Schema(description = "护照签发地Id")
  private Long fkAreaCountryIdPassport;
 

  @Schema(description = "学生出生所在国家Id")
  private Long fkAreaCountryIdBirth;
 

  @Schema(description = "学生出生所在州省Id")
  private Long fkAreaStateIdBirth;
 

  @Schema(description = "学生出生所在城市Id")
  private Long fkAreaCityIdBirth;
 

  @Schema(description = "学生出生所在国家名称")
  private String fkAreaCountryNameBirth;
 

  @Schema(description = "学生出生所在州省名称")
  private String fkAreaStateNameBirth;
 

  @Schema(description = "学生出生所在城市名称")
  private String fkAreaCityNameBirth;
 

  @Schema(description = "手机区号")
  private String mobileAreaCode;
 

  @Schema(description = "移动电话")
  private String mobile;
 

  @Schema(description = "电话区号")
  private String telAreaCode;
 

  @Schema(description = "电话")
  private String tel;
 

  @Schema(description = "Email")
  private String email;
 

  @Schema(description = "学生现居所在国家Id")
  private Long fkAreaCountryId;
 

  @Schema(description = "学生现居所在州省Id")
  private Long fkAreaStateId;
 

  @Schema(description = "学生现居所在城市Id")
  private Long fkAreaCityId;
 

  @Schema(description = "学生现居所在国家名称")
  private String fkAreaCountryName;
 

  @Schema(description = "学生现居所在州省名称")
  private String fkAreaStateName;
 

  @Schema(description = "学生现居所在城市名称")
  private String fkAreaCityName;
 

  @Schema(description = "邮编")
  private String zipcode;
 

  @Schema(description = "联系地址")
  private String contactAddress;
 

  @Schema(description = "学历等级类型：高中/大学/本科/研究生/硕士/博士在读/博士/博士后")
  private String educationLevelType;
 

  @Schema(description = "毕业专业")
  private String educationMajor;
 

  @Schema(description = "毕业大学国家Id")
  private Long fkAreaCountryIdEducation;
 

  @Schema(description = "毕业大学州省Id")
  private Long fkAreaStateIdEducation;
 

  @Schema(description = "毕业大学城市Id")
  private Long fkAreaCityIdEducation;
 

  @Schema(description = "毕业大学国家名称")
  private String fkAreaCountryNameEducation;
 

  @Schema(description = "毕业大学州省名称")
  private String fkAreaStateNameEducation;
 

  @Schema(description = "毕业大学城市名称")
  private String fkAreaCityNameEducation;
 

  @Schema(description = "毕业院校Id")
  private Long fkInstitutionIdEducation;
 

  @Schema(description = "毕业院校名称")
  private String fkInstitutionNameEducation;
 

  @Schema(description = "毕业学校类型：985/211/其他，默认选项：其他")
  private String institutionTypeEducation;
 

  @Schema(description = "学历等级类型（国际）：高中/大学/本科/研究生/硕士/博士在读/博士/博士后")
  private String educationLevelType2;
 

  @Schema(description = "毕业专业（国际）")
  private String educationMajor2;
 

  @Schema(description = "毕业大学国家Id（国际）")
  private Long fkAreaCountryIdEducation2;
 

  @Schema(description = "毕业大学州省Id（国际）")
  private Long fkAreaStateIdEducation2;
 

  @Schema(description = "毕业大学城市Id（国际）")
  private Long fkAreaCityIdEducation2;
 

  @Schema(description = "毕业大学国家名称（国际）")
  private String fkAreaCountryNameEducation2;
 

  @Schema(description = "毕业大学州省名称（国际）")
  private String fkAreaStateNameEducation2;
 

  @Schema(description = "毕业大学城市名称（国际）")
  private String fkAreaCityNameEducation2;
 

  @Schema(description = "毕业院校Id（国际）")
  private Long fkInstitutionIdEducation2;
 

  @Schema(description = "毕业院校名称（国际）")
  private String fkInstitutionNameEducation2;
 

  @Schema(description = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
  private Integer educationProject;
 

  @Schema(description = "学位情况，枚举：获得双学位/获得国际学位/获得国内学位")
  private Integer educationDegree;
 

  @Schema(description = "是否复杂教育背景：0否/1是")
  private Integer isComplexEducation;
 

  @Schema(description = "复杂教育背景备注")
  private String complexEducationRemark;
 

  @Schema(description = "高中成绩类型，枚举Key")
  private String highSchoolTestType;
 

  @Schema(description = "高中成绩")
  private String highSchoolTestScore;
 

  @Schema(description = "本科成绩类型，枚举Key")
  private String standardTestType;
 

  @Schema(description = "本科成绩")
  private String standardTestScore;
 

  @Schema(description = "硕士成绩类型，枚举Key")
  private String masterTestType;
 

  @Schema(description = "硕士成绩")
  private String masterTestScore;
 

  @Schema(description = "英语测试类型")
  private String englishTestType;
 

  @Schema(description = "英语测试成绩")
  private BigDecimal englishTestScore;
 

  @Schema(description = "备注")
  private String remark;

  @JsonFormat(pattern="yyyy-MM-dd")
  @Schema(description = "收到申请资料时间")
  private LocalDateTime receivedApplicationDataDate;
 

  @Schema(description = "共享路径")
  private String sharedPath;
 

  @Schema(description = "学生业务状态(多选)：0转代理学生/1奖学金占学费的60%以上")
  private String conditionType;
 

  @Schema(description = "旧数据num(gea)")
  private String numGea;
 

  @Schema(description = "旧数据num(iae)")
  private String numIae;
 

  @Schema(description = "旧数据id(issue学生Id)")
  private String idIssue;
 

  @Schema(description = "旧数据id(issue学生信息Id)")
  private String idIssueInfo;
 

  @Schema(description = "旧数据id(gea)")
  private String idGea;
 

  @Schema(description = "旧数据id(iae)")
  private String idIae;


  @DateTimeFormat(
          pattern = "yyyy-MM-dd HH:mm:ss"
  )
  @JsonFormat(
          pattern = "yyyy-MM-dd HH:mm:ss",
          timezone = "GMT+8"
  )
  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;


  @DateTimeFormat(
          pattern = "yyyy-MM-dd HH:mm:ss"
  )
  @JsonFormat(
          pattern = "yyyy-MM-dd HH:mm:ss",
          timezone = "GMT+8"
  )
  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
