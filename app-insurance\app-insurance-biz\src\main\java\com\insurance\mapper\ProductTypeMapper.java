package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.insurance.entity.ProductType;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Author:<PERSON>
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface ProductTypeMapper extends BaseMapper<ProductType> {

    /**
     * 根据产品类型_key查询产品类型信息
     * @param key
     * @return
     */
    ProductType selectByKey(String key);
}
