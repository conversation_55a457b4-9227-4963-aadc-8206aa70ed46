package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.work.MFeedbackOrderDddDto;
import com.partner.dto.work.MFeedbackOrderDto;
import com.partner.dto.work.MFeedbackOrderReplyDto;
import com.partner.entity.MFeedbackOrderEntity;
import com.partner.entity.UFeedbackOrderTypeEntity;
import com.partner.vo.work.MFeedbackOrderDetailVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_feedback_order】的数据库操作Service
* @createDate 2025-05-23 10:16:24
*/
public interface MFeedbackOrderService extends IService<MFeedbackOrderEntity> {

    /**
     * @desc 反馈分页查询
     * @return
     */
    IPage getPage(Page page, MFeedbackOrderDto params);
    /**
     * @desc 反馈详情
     * @return
     */
    MFeedbackOrderDetailVo getMFeedbackDetail(Long id);

    /**
     * @desc 反馈工单添加
     * @return
     */
    Long addWorkOrder(MFeedbackOrderDddDto adddto);

    Long feedDack(MFeedbackOrderReplyDto replydto);


    List<UFeedbackOrderTypeEntity> getUFeedbackOrderType();


    void solve(Long id);

}
