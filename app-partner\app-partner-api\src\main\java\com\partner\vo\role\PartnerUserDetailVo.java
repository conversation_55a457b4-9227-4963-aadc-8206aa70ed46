package com.partner.vo.role;

import com.partner.entity.MPartnerUserEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:伙伴用户详情
 */
@Data
public class PartnerUserDetailVo extends MPartnerUserEntity {

    @Schema(description = "角色ID集合")
    private List<Long> roleIds = new ArrayList<>();

    @Schema(description = "上司伙伴用户ID")
    private List<Long> superiorIds = new ArrayList<>();

    @Schema(description = "根据国家ID")
    private List<Long> countryIds = new ArrayList<>();

    @Schema(description = "是否拥有团队权限")
    private Boolean hasTeamPermission;

    @Schema(description = "锁定标记，0未锁定，1已锁定")
    private Integer lockFlag;

}
