package com.auth.rocketmq.dlq;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/3/20
 * @Version 1.0
 * @apiNote:用户下线死信消息监听者
 */
@Slf4j
@RocketMQMessageListener(
        topic = "%DLQ%user_offline_topic",  // 监听用户下线死信队列
        consumerGroup = "dlq-user_offline_topic_group",
        consumeMode = ConsumeMode.CONCURRENTLY
)
@Component
public class UserOfflineDlqConsumer implements RocketMQListener<Long> {
    @Override
    public void onMessage(Long userId) {
        log.error("用户下线死信队列监听到消息,用户ID:{}", userId);
    }
}
