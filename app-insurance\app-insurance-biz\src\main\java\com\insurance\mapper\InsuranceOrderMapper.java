package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.insurance.entity.InsuranceOrder;
import com.insurance.vo.insurance.client.ClientOrderVo;
import com.insurance.vo.insurance.order.OrderDetailVo;
import com.insurance.vo.insurance.order.OrderStatisticsVo;
import com.insurance.vo.settlement.SettlementOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface InsuranceOrderMapper extends BaseMapper<InsuranceOrder> {

    /**
     * 根据订单号查询保单信息
     *
     * @param orderNum
     * @return
     */
    InsuranceOrder selectByOrderNum(@Param("orderNum") String orderNum);

    /**
     * 根据订单号查询订单信息
     *
     * @param limit
     * @return
     */
    List<InsuranceOrder> selectPendingOrders(@Param("limit") int limit);

    /**
     * 根据订单号查询订单详情
     *
     * @param id
     * @return
     */
    OrderDetailVo selectOrderDetailById(@Param("id") Long id);

    /**
     * 根据伙伴用户Id、订单状态、日期查询订单列表
     *
     * @param agentId
     * @param orderStatus
     * @param date
     * @return
     */
    List<InsuranceOrder> selectOrderList(@Param("agentId") Long agentId,
                                         @Param("orderStatus") Integer orderStatus,
                                         @Param("date") String date);

    /**
     * 根据伙伴用户Id、日期查询订单数量
     * @param agentId
     * @param date
     * @return
     */
    OrderStatisticsVo selectOrderCountByDate(@Param("agentId") Long agentId,
                                             @Param("date") String date);

    /**
     * 根据伙伴用户Id、订单结算状态、日期查询订单列表
     * @param settlementStatus
     * @param agentId
     * @param date
     * @return
     */
    List<SettlementOrderVo> selectOrderBySettlementStatus(@Param("settlementStatus") Integer settlementStatus,
                                                          @Param("agentId") Long  agentId,
                                                          @Param("date") String date);


    /**
     * 根据客户邮箱查询订单列表
     * @param email
     * @return
     */
    List<ClientOrderVo> selectClientOrderList(@Param("email") String email);
}
