package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_user_platform_login")
@Schema(description = "用户登录信息")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SystemUserPlatformLoginEntity extends Model<SystemUserPlatformLoginEntity> {
    private static final long serialVersionUID = 1L;


    /**
     * 用户平台应用登录信息Id
     */
    @Schema(description="用户平台应用登录信息Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @Schema(description="租户Id")
    private Long fkTenantId;

    /**
     * 用户Id
     */
    @Schema(description="用户Id")
    private Long fkUserId;

    /**
     * 平台应用Id
     */
    @Schema(description="平台应用Id")
    private Long fkPlatformId;

    /**
     * 平台应用CODE
     */
    @Schema(description="平台应用CODE")
    private String fkPlatformCode;

    /**
     * 登陆用户Id
     */
    @Schema(description="登陆用户Id")
    private String loginId;

    /**
     * 登陆用户密码
     */
    @Schema(description="登陆用户密码")
    private String loginPs;

    /**
     * 盐值
     */
    @Schema(description="盐值")
    private String salt;

    /**
     * mp_openid
     */
    @Schema(description="mp_openid")
    private String miniProgramOpenid;

    /**
     * mp_uid
     */
    @Schema(description="mp_uid")
    private String miniProgramUid;

    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}