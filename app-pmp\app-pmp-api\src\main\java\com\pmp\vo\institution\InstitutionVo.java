package com.pmp.vo.institution;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/3  17:52
 * @Version 1.0
 * 学校VO
 */
@Data
public class InstitutionVo {

    @Schema(description = "学校id")
    private Long institutionId;

    @Schema(description = "学校类型")
    private String type;

    @Schema(description = "学校类型中文名称")
    private String typeChn;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "国家中文名称")
    private String countryChn;

    @Schema(description = "州/省")
    private String areaState;

    @Schema(description = "州省中文名称")
    private String areaStateChn;

    @Schema(description = "QS排名")
    private Integer qsRank;

    @Schema(description = "学校名称")
    private String name;

    @Schema(description = "学校名称-英文")
    private String nameChn;

    @Schema(description = "学校名称-展示名称")
    private String nameDisplay;

    @Schema(description = "网站")
    private String website;

    @Schema(description = "标签列表")
    List<CustomizeLabelVo> labelList;

    @Schema(description = "学校封面图")
    private String institutionCover;

    @Schema(description = "是否是高佣学校")
    private Boolean isHighCommission = Boolean.FALSE;

    @Schema(description = "高佣学校描述-是高佣学校才有值")
    private String highCommissionDescription;

    @Schema(description = "高佣学校图片-是高佣学校才有值")
    private String highCommissionPic;

}
