package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.MLiveParamsDto;
import com.partner.dto.MLivecalendarParamsDto;
import com.partner.entity.MLiveEntity;
import com.partner.vo.MLivePageVo;
import com.partner.vo.MLivecalendarVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MLiveMapper extends BaseMapper<MLiveEntity>{

    IPage<MLivePageVo> getMLiveListPage(Page page, @Param("query") MLiveParamsDto dto);

    List<MLivePageVo> getMLiveFive(MLiveParamsDto dto);

    List<MLivePageVo> getStatus(MLivecalendarParamsDto params);
}
