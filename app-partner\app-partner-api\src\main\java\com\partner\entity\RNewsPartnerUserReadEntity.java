package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-08 10:20:54
 */

@Data
@TableName("r_news_partner_user_read")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_news_partner_user_read ")
public class RNewsPartnerUserReadEntity extends Model<RNewsPartnerUserReadEntity>{

  @Schema(description = "新闻资讯阅读关系Id")
  private Long id;
 

  @Schema(description = "新闻Id")
  private Long fkNewsId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
