package com.partner.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.config.RedisService;
import com.partner.constant.RedisConstant;
import com.partner.entity.MPartnerUserEntity;
import com.partner.entity.RPartnerUserSuperiorEntity;
import com.partner.mapper.MPartnerUserMapper;
import com.partner.mapper.RPartnerUserSuperiorMapper;
import com.partner.service.RPartnerUserSuperiorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【r_partner_user_superior】的数据库操作Service实现
 * @createDate 2025-01-14 21:05:44
 */
@Service
public class RPartnerUserSuperiorServiceImpl extends ServiceImpl<RPartnerUserSuperiorMapper, RPartnerUserSuperiorEntity> implements RPartnerUserSuperiorService {

    @Autowired
    private RPartnerUserSuperiorMapper userSuperiorMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private MPartnerUserMapper partnerUserMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePartnerUserSuperior(Long partnerUserId, List<Long> superiorIds) {
        if (Objects.isNull(partnerUserId)) {
            return;
        }
        if (CollectionUtils.isEmpty(superiorIds)) {
            this.remove(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>().eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, partnerUserId));
            return;
        }

        FzhUser user = SecurityUtils.getUser();
        //当前用户的上司
        List<Long> currentSuperiorIds = this.list(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                        .eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, partnerUserId))
                .stream().map(RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior).collect(Collectors.toList());

        //获取新增的国家
        List<Long> addSuperiorIds = superiorIds.stream()
                .filter(id -> !currentSuperiorIds.contains(id)).collect(Collectors.toList());

        //获取需要删除的上司
        List<Long> delSuperiorIds = currentSuperiorIds.stream()
                .filter(id -> !superiorIds.contains(id)).collect(Collectors.toList());

        Boolean changeFlag = Boolean.FALSE;

        if (CollectionUtils.isNotEmpty(addSuperiorIds)) {
            List<RPartnerUserSuperiorEntity> saveList = addSuperiorIds.stream()
                    .map(id -> {
                        RPartnerUserSuperiorEntity userSuperior = new RPartnerUserSuperiorEntity();
                        userSuperior.setFkPartnerUserId(partnerUserId);
                        userSuperior.setFkTenantId(Long.valueOf(user.getFkTenantId()));
                        userSuperior.setFkPartnerUserIdSuperior(id);
                        userSuperior.setGmtCreate(LocalDateTime.now());
                        userSuperior.setGmtModified(LocalDateTime.now());
                        userSuperior.setGmtCreateUser(user.getLoginId());
                        userSuperior.setGmtModifiedUser(user.getLoginId());
                        return userSuperior;
                    }).collect(Collectors.toList());
            this.saveBatch(saveList);
            changeFlag = true;
        }

        if (CollectionUtils.isNotEmpty(delSuperiorIds)) {
            this.remove(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                    .eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, partnerUserId)
                    .in(RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior, delSuperiorIds));
            changeFlag = true;
        }

        //上级有变动-清除上级缓存
        if (changeFlag) {
            deleteSupCache(partnerUserId);
        }
    }

    @Override
    public Set<Long> getAllSubUserIds(Long partnerUserId) {
        List<RPartnerUserSuperiorEntity> allRelations = userSuperiorMapper.selectList(null);
        Map<Long, List<Long>> subMap = buildSubordinateMap(allRelations);
        return recursiveFind(subMap, partnerUserId);
    }

    @Override
    public Set<Long> getAllSuperiorUserIds(Long partnerUserId) {
        List<RPartnerUserSuperiorEntity> allRelations = userSuperiorMapper.selectList(null);
        Map<Long, List<Long>> superiorMap = buildSuperiorMap(allRelations);
        return recursiveFind(superiorMap, partnerUserId);
    }

    @Override
    public void deleteSupCache(Long partnerUserId) {
        try {
            Set<Long> allSuperiorUserIds = getAllSuperiorUserIds(partnerUserId);
            List<Long> list = Objects.isNull(allSuperiorUserIds) ? Arrays.asList(0L) : new ArrayList<>(allSuperiorUserIds);
            List<Long> systemUserIds = partnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>().in(MPartnerUserEntity::getId, list))
                    .stream().map(MPartnerUserEntity::getFkUserId).collect(Collectors.toList());
            List<String> keys = systemUserIds.stream()
                    .map(id -> RedisConstant.PARTNER_USERINFO_KEY_PREFIX + id)
                    .collect(Collectors.toList());
            redisService.deleteBatch(keys);
        } catch (Exception e) {
            log.error("清空父级用户缓存失败:{}", e);
        }

    }

    /**
     * 获取所有的上下级关系映射：下级 -> 上级
     */
    private Map<Long, List<Long>> buildSuperiorMap(List<RPartnerUserSuperiorEntity> allRelations) {
        return allRelations.stream()
                .collect(Collectors.groupingBy(
                        RPartnerUserSuperiorEntity::getFkPartnerUserId, // 下级
                        Collectors.mapping(RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior, Collectors.toList())
                ));
    }

    /**
     * 获取所有的上下级关系映射：上级 -> 下级
     */
    private Map<Long, List<Long>> buildSubordinateMap(List<RPartnerUserSuperiorEntity> allRelations) {
        return allRelations.stream()
                .collect(Collectors.groupingBy(
                        RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior, // 上级
                        Collectors.mapping(RPartnerUserSuperiorEntity::getFkPartnerUserId, Collectors.toList())
                ));
    }

    /**
     * 通用递归查找
     */
    private Set<Long> recursiveFind(Map<Long, List<Long>> relationMap, Long startId) {
        Set<Long> result = new HashSet<>();
        Deque<Long> stack = new ArrayDeque<>();
        stack.push(startId);

        while (!stack.isEmpty()) {
            Long current = stack.pop();
            List<Long> children = relationMap.getOrDefault(current, Collections.emptyList());
            for (Long child : children) {
                if (result.add(child)) { // 添加成功说明没重复
                    stack.push(child);
                }
            }
        }
        return result;
    }
}




