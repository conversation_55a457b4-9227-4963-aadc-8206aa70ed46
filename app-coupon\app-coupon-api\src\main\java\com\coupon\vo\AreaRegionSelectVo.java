package com.coupon.vo;

import io.swagger.v3.oas.annotations.Operation;
import lombok.Data;

import java.util.List;

@Data
public class AreaRegionSelectVo {


    private Long id;

    private String name;

    private String nameChn;

    private String fullName;

    private String oldName;

    private String oldFullName;
    private String num;

    private Long deputyId;

    private Long status;

    private Integer order;

    private Long parentId;

    private Long offerItemId;

    private List<AreaRegionSelectVo> institutionTabs;

    private Boolean isFollow;

    private Integer mode;

    public String getFullName() {
        if (this.fullName!=null&&!"".equals(this.fullName)) {
            return this.fullName;
        } else if (this.name!=null&&!"".equals(this.name)) {
            return (this.nameChn != null && !this.nameChn.trim().isEmpty())
                    ? this.name + "（" + this.nameChn + "）"
                    : this.name;
        } else {
            return this.nameChn;
        }
    }

}
