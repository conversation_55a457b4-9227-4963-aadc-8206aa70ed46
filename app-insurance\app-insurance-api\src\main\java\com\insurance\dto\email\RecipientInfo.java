package com.insurance.dto.email;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/8/6
 * @Version 1.0
 * @apiNote:邮件接收者信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RecipientInfo {

    @Schema(description = "接收者邮箱/电话")
    private String contract;

    @Schema(description = "邮件信息")
    private Map<String, String> params;
}
