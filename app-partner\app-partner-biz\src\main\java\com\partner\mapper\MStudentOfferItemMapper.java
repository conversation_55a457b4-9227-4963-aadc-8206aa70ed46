package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.TeamDataSumDto;
import com.partner.entity.MStudentOfferItemEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student_offer_item(学生申请方案学习计划)】的数据库操作Mapper
* @createDate 2025-01-18 11:51:30
* @Entity com.partner.entity.MStudentOfferItem
*/
@Mapper
@DS("saledb")
public interface MStudentOfferItemMapper extends BaseMapper<MStudentOfferItemEntity> {
    int getCompleteStudens(TeamDataSumDto params);
    List<Long> getLevelCompleteStudens(TeamDataSumDto params);


}




