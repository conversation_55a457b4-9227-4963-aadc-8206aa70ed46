package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.MAgentContractAccountEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_agent_contract_account】的数据库操作Mapper
* @createDate 2025-01-09 14:06:45
* @Entity com.partner.entity.MAgentContractAccount
*/
@Mapper
@DS("saledb")
public interface MAgentContractAccountMapper extends BaseMapper<MAgentContractAccountEntity> {
    List<MAgentContractAccountEntity> getAgentContractAccount(@Param("agentId")  Long agentId);


}




