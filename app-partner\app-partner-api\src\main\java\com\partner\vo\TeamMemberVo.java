package com.partner.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  15:21
 * @Version 1.0
 */
@Data
public class TeamMemberVo {

    @Schema(description = "Partner用户ID")
    private Long partnerUserId;

    @Schema(description = "系统用户ID")
    private Long userId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色Code")
    private String roleCode;

    @Schema(description = "登录账号")
    private String loginId;

    @Schema(description = "锁定标记，0未锁定，1已锁定")
    private Integer lockFlag;

    @Schema(description = "成交金额")
    private BigDecimal dealAmount = new BigDecimal(0);

    @Schema(description = "成交人数")
    private Integer dealNum = 0;

    @Schema(description = "跟进中的学生人数")
    private Integer followStudentNum = 0;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    @Schema(description = "当前年份用户是否有创建")
    private Boolean isYearCreate = Boolean.TRUE;

    @Schema(description = "角色ID集合")
    private List<Long> roleIds;

    @Schema(description = "角色名称集合")
    private List<String> roleNames;

    @Schema(description = "上司名称集合")
    private List<String> superiorNames;

    @Schema(description = "上司id集合")
    private List<Long> superiorIds;

    @Schema(description = "上司名称-逗号拼接")
    private String superiorNamesStr;

    @Schema(description = "是否超级管理员0否1是")
    private Integer isAdmin = 0;

}
