package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-13 19:02:43
 */

@Data
@TableName("m_settlement_bill_item")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_settlement_bill_item ")
public class MSettlementBillItemEntity extends Model<MSettlementBillItemEntity>{

  @Schema(description = "结算账单明细项Id")
  private Long id;
 

  @Schema(description = "结算账单Id")
  private Long fkSettlementBillId;
 

  @Schema(description = "学生Id")
  private Long fkStudentId;
 

  @Schema(description = "应付计划Id")
  private Long fkPayablePlanId;
 

  @Schema(description = "应付币种")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "实际支付金额（合并小计=实收+预付）")
  private BigDecimal amountActual;
 

  @Schema(description = "实际手续费金额（合并小计=实收+预付）")
  private BigDecimal serviceFeeActual;

  @Schema(description = "汇率")
  private BigDecimal exchangeRate;
  @Schema(description = "兑换支付金额")
  private BigDecimal amountExchange;
  @Schema(description = "兑换手续费金额")
  private BigDecimal serviceFeeExchange;


  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
