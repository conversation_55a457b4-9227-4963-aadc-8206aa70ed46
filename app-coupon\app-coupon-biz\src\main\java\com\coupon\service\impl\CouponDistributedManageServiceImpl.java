package com.coupon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.coupon.dto.AddCouponDto;
import com.coupon.dto.VerificationCouponDto;
import com.coupon.entity.MAppIntroductionEntity;
import com.coupon.entity.MCouponEntity;
import com.coupon.entity.MCouponTypeEntity;
import com.coupon.entity.RCouponFetchEntity;
import com.coupon.mapper.MAppIntroductionMapper;
import com.coupon.mapper.MCouponMapper;
import com.coupon.mapper.MCouponTypeMapper;
import com.coupon.mapper.RCouponFetchMapper;
import com.coupon.service.ICouponDistributedManageService;
import com.coupon.vo.CouponImport;
import com.coupon.vo.CouponVerifyCationVo;
import com.coupon.vo.ImportCouponVo;
import com.coupon.vo.VerifyCationData;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CouponDistributedManageServiceImpl implements ICouponDistributedManageService {
    @Resource
    private MCouponMapper couponMapper;

    @Resource
    private MCouponTypeMapper couponTypeMapper;

    @Resource
    private RCouponFetchMapper couponFetchMapper;

    @Resource
    private MAppIntroductionMapper appIntroductionMapper;

    @Override
    public R importCoupon(Long couponTypeId, MultipartFile[] files) throws Exception {
        ImportCouponVo importCouponVo = new ImportCouponVo();
        // 获取数据库中已经存在的 code
        QueryWrapper<MCouponEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("fk_coupon_type_id", couponTypeId);
        List<MCouponEntity> mCouponTypeEntities = couponMapper.selectList(queryWrapper);
        List<String> codeList = mCouponTypeEntities.stream()
                .map(MCouponEntity::getCode)
                .collect(Collectors.toList());
        for (MultipartFile file : files) {
            // 源文件名
            String fileNameOrc = file.getOriginalFilename();
            int i = fileNameOrc.lastIndexOf(".");
            // 获取后缀名
            String fileTypeOrc = fileNameOrc.substring(i, fileNameOrc.length()).toLowerCase();
            if (!fileTypeOrc.equals(".xlsx")) {
                return R.restResult(null, 1, "导入文件格式错误");
            }
            InputStream inputStream = file.getInputStream();
            // 检查文件是否为空
            if (file.isEmpty()) {
                return R.restResult(null, 1, "空文件");
            }
            // 创建Workbook实例
            Workbook workbook = new XSSFWorkbook(inputStream);
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            int couponNum = 0;
            int repeatCouponNum = 0;
            int lapseCodeNum = 0;
            int alreadyImportNum = 0;
            List<CouponImport> coupons = new ArrayList<>();
            List<CouponImport> repeatCoupons = new ArrayList<>();
            List<CouponImport> lapseCoupons = new ArrayList<>();
            List<CouponImport> alreadyImportCoupons = new ArrayList<>();
            // 所有优惠卷
            List<String> codes = new ArrayList<>();
            try {
                // 遍历行 Row
                Iterator<Row> rowIterator = sheet.iterator();
                for (int j = 0; rowIterator.hasNext(); j++) {
                    Row row = rowIterator.next();
                    // 根据优惠卷格式，从第六行开始获取优惠卷
                    if (j > 4) {
                        row.getCell(2).setCellType(CellType.STRING);
                        // 统计优惠卷总数
                        couponNum++;
                        // 首先判断是否优惠卷已经导入过
                        if (codeList.contains(row.getCell(2).getStringCellValue())) {
                            alreadyImportNum++;
                            CouponImport coupon = new CouponImport();
                            coupon.setCode(row.getCell(2).getStringCellValue());
                            if (!"ACTIVE".equals(row.getCell(3).getStringCellValue())) {
                                coupon.setActive(false);
                            } else {
                                coupon.setActive(true);
                            }
                            alreadyImportCoupons.add(coupon);
                            continue;
                        }
                        if (codes.contains(row.getCell(2).getStringCellValue())) {
                            repeatCouponNum++;
                            CouponImport coupon = new CouponImport();
                            coupon.setCode(row.getCell(2).getStringCellValue());
                            if (!"ACTIVE".equals(row.getCell(3).getStringCellValue())) {
                                coupon.setActive(false);
                            } else {
                                coupon.setActive(true);
                            }
                            repeatCoupons.add(coupon);
                        } else {
                            CouponImport coupon = new CouponImport();
                            coupon.setCode(row.getCell(2).getStringCellValue());
                            codes.add(row.getCell(2).getStringCellValue());
                            if (!"ACTIVE".equals(row.getCell(3).getStringCellValue())) {
                                lapseCodeNum++;
                                coupon.setActive(false);
                                lapseCoupons.add(coupon);
                            } else {
                                coupon.setActive(true);
                                coupons.add(coupon);
                            }
                        }
                    }
                }
                importCouponVo.setCouponNum(couponNum);
                importCouponVo.setRepeatCouponNum(repeatCouponNum);
                importCouponVo.setLapseCodeNum(lapseCodeNum);
                importCouponVo.setAlreadyImportNum(alreadyImportNum);
                importCouponVo.setCoupons(coupons);
                importCouponVo.setRepeatCoupons(repeatCoupons);
                importCouponVo.setLapseCoupons(lapseCoupons);
                importCouponVo.setAlreadyImportCoupons(alreadyImportCoupons);
                workbook.close();
            } catch (Exception e) {
                return R.restResult(null, 1, "导入文件格式错误");
            }

        }
        return R.restResult(importCouponVo, 0, "");
    }

    @Override
    public R addCoupon(AddCouponDto addCouponDto) throws Exception {
        long couponTypeID = addCouponDto.getCouponTypeId();
        QueryWrapper<MCouponTypeEntity> couponTypeQueryWrapper = new QueryWrapper<>();
        couponTypeQueryWrapper.eq("id", couponTypeID);
        MCouponTypeEntity couponType = couponTypeMapper.selectOne(couponTypeQueryWrapper);
        if (couponType == null) {
            return R.restResult(null, 1, "优惠卷类型不存在，请先创建优惠卷类型");
        }
        FzhUser fzhUser = SecurityUtils.getUser();
        for (int i = 0; i < addCouponDto.getCouponImportList().size(); i++) {
            MCouponEntity mCouponEntity = new MCouponEntity();
            mCouponEntity.setFkCouponTypeId(couponTypeID);
            mCouponEntity.setCode(addCouponDto.getCouponImportList().get(i).getCode());
            mCouponEntity.setIsTaken(false);
            mCouponEntity.setIsUsed(false);
            mCouponEntity.setIsActive(addCouponDto.getCouponImportList().get(i).isActive());
            mCouponEntity.setGmtCreate(LocalDateTime.now());
            mCouponEntity.setGmtCreateUser(Objects.nonNull(fzhUser) ? fzhUser.getLoginId() : "admin");
            try {
                couponMapper.insert(mCouponEntity);
            } catch (Exception e) {
                return R.restResult(null, 2, "已经存在不同类型但是兑换码相同的优惠卷");
            }

        }
        return R.ok();
    }

    @Override
    public R importVerification(Long couponTypeId, MultipartFile[] files) throws Exception {
        CouponVerifyCationVo couponVerifyCationVo = new CouponVerifyCationVo();
        List<VerifyCationData> verifyCationDataArrayList = new ArrayList<>();
        int usedNum = 0;
        for (MultipartFile file : files) {
            // 源文件名
            String fileNameOrc = file.getOriginalFilename();
            int i = fileNameOrc.lastIndexOf(".");
            // 获取后缀名
            String fileTypeOrc = fileNameOrc.substring(i, fileNameOrc.length()).toLowerCase();
            if (!fileTypeOrc.equals(".xlsx")) {
                return R.restResult(null, 1, "导入文件格式错误");
            }
            InputStream inputStream = file.getInputStream();
            // 检查文件是否为空
            if (file.isEmpty()) {
                return R.restResult(null, 1, "空文件");
            }
            // 创建Workbook实例
            Workbook workbook = new XSSFWorkbook(inputStream);
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            try {
                // 遍历行 Row
                Iterator<Row> rowIterator = sheet.iterator();
                for (int j = 0; rowIterator.hasNext(); j++) {
                    Row row = rowIterator.next();
                    // 根据优惠卷格式，从第六行开始获取优惠卷
                    if (j > 4) {
                        row.getCell(5).setCellType(CellType.STRING);
                        if (row.getCell(5).toString().isEmpty()) {
                            continue;
                        }
                        usedNum++;
                        VerifyCationData verifyCationData = new VerifyCationData();
                        row.getCell(6).setCellType(CellType.STRING);
                        row.getCell(7).setCellType(CellType.STRING);
                        row.getCell(2).setCellType(CellType.STRING);
                        verifyCationData.setCode(row.getCell(2).getStringCellValue());
                        verifyCationData.setName(row.getCell(5).getStringCellValue());
                        verifyCationData.setNeeaID(row.getCell(6).getStringCellValue());
                        verifyCationData.setDate(row.getCell(7).getStringCellValue());
                        verifyCationDataArrayList.add(verifyCationData);
                    }
                }

                workbook.close();
            } catch (Exception e) {
                return R.restResult(null, 1, "导入文件格式错误");
            }
        }
        couponVerifyCationVo.setCouponTypeId(couponTypeId);
        couponVerifyCationVo.setUsedNum(usedNum);
        couponVerifyCationVo.setVerifyCationDataList(verifyCationDataArrayList);
        return R.restResult(couponVerifyCationVo, 0, "");
    }

    @Override
    public R verificationCoupon(VerificationCouponDto verificationCouponDto) throws Exception {
        FzhUser fzhUser = SecurityUtils.getUser();
        List<VerifyCationData> verifyCationDataList = verificationCouponDto.getVerifyCationDataList();
        for (VerifyCationData verifyCationData : verifyCationDataList) {
            // 修改优惠卷领取信息
            QueryWrapper<MCouponEntity> mCouponEntityQueryWrapper = new QueryWrapper<>();
            mCouponEntityQueryWrapper.eq("code", verifyCationData.getCode());
            mCouponEntityQueryWrapper.eq("fk_coupon_type_id", verificationCouponDto.getCouponTypeId());
            List<MCouponEntity> mCouponEntityList = couponMapper.selectList(mCouponEntityQueryWrapper);
            for (MCouponEntity mCouponEntity : mCouponEntityList) {
                mCouponEntity.setIsUsed(true);
                mCouponEntity.setReservedField1(verifyCationData.getName());
                mCouponEntity.setReservedField2(verifyCationData.getNeeaID());
                mCouponEntity.setReservedField3(verifyCationData.getDate());
                mCouponEntity.setGmtModified(LocalDateTime.now());
                mCouponEntity.setGmtModifiedUser(Objects.nonNull(fzhUser)?fzhUser.getLoginId():"admin");
                couponMapper.updateById(mCouponEntity);
            }
            QueryWrapper<RCouponFetchEntity> rCouponFetchEntityQueryWrapper = new QueryWrapper<>();
            rCouponFetchEntityQueryWrapper.eq("fk_coupon_type_id", verificationCouponDto.getCouponTypeId());
            rCouponFetchEntityQueryWrapper.eq("fk_coupon_code", verifyCationData.getCode());
            List<RCouponFetchEntity> rCouponFetchEntityList = couponFetchMapper.selectList(rCouponFetchEntityQueryWrapper);
            for (RCouponFetchEntity rCouponFetchEntity : rCouponFetchEntityList) {
                rCouponFetchEntity.setIsUsed(true);
                rCouponFetchEntity.setGmtModified(LocalDateTime.now());
                rCouponFetchEntity.setGmtModifiedUser(Objects.nonNull(fzhUser)?fzhUser.getLoginId():"admin");
                couponFetchMapper.updateById(rCouponFetchEntity);
            }
        }
        return R.restResult(null, 0, "核销完成");
    }

    @Override
    public R updateIntroduction(MAppIntroductionEntity mAppIntroductionEntity) throws Exception {
        MAppIntroductionEntity mAppIntroductionEntity1 = new MAppIntroductionEntity();

        if(mAppIntroductionEntity.getIntroduction() != null && !mAppIntroductionEntity.getIntroduction().isEmpty()) {
            mAppIntroductionEntity1.setIntroduction(mAppIntroductionEntity.getIntroduction());
        }
        mAppIntroductionEntity1.setIsActive(mAppIntroductionEntity.getIsActive());
        if(mAppIntroductionEntity.getId() != null) {
            mAppIntroductionEntity1.setId(mAppIntroductionEntity.getId());
            appIntroductionMapper.updateById(mAppIntroductionEntity1);
        }
        mAppIntroductionEntity1.setGmtCreate(LocalDateTime.now());
        appIntroductionMapper.insert(mAppIntroductionEntity1);
        return R.restResult(null, 0, "小程序介绍添加完成");
    }
}
