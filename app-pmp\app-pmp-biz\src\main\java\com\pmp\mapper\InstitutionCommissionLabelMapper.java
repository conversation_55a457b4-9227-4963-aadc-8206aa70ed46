package com.pmp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pmp.entity.InstitutionCommissionLabel;
import com.pmp.vo.institution.CustomizeLabelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InstitutionCommissionLabelMapper extends BaseMapper<InstitutionCommissionLabel> {

    /**
     * 根据标签Id查询自定义标签详情
     *
     * @param labelIds
     * @return
     */
    List<CustomizeLabelVo> selectCustomizeLabel(@Param("labelIds") List<Long> labelIds);
}
