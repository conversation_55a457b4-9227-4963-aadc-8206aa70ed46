package com.fzh.job.util;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fzh.job.config.WechatApplet;
import com.partner.wechat.result.AccessTokenResult;

import java.util.HashMap;
import java.util.Map;


public class WechatAppletUtil {

    public static String getToken(WechatApplet wechatApplet){
        String token="";
        String wechattokenKey="PartnerLoginWechatToken";
        token=RedisUtil.getWechatToken(wechattokenKey);
        if(StringUtils.isNotEmpty(token)){
            return token;
        }

        Map<String, String> headerParams = new HashMap<>();
        Map<String, String> bodyParams = new HashMap<>();
        bodyParams.put("grant_type", "client_credential");
        bodyParams.put("appid",wechatApplet.getAppId());
        bodyParams.put("secret",wechatApplet.getSecret());
        String tokenResult= HttpClientUtils.sendPostRequest(wechatApplet.getStableTokenUrl(), headerParams, bodyParams);
        System.out.println(tokenResult);
        AccessTokenResult accessTokenResult = JSON.parseObject(tokenResult, AccessTokenResult.class);
        if(accessTokenResult!=null){
            token=accessTokenResult.getAccess_token();
            RedisUtil.setWechatToken(wechattokenKey,token);
        }
        return token;
    }
}
