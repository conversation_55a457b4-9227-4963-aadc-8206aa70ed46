package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.offeritem.MAppDefatStudentOfferItemDto;
import com.partner.dto.student.MStudentParamsDetailDto;
import com.partner.dto.student.MStudentParamsDto;
import com.partner.dto.student.MStudentSubAndAddOrEditDto;
import com.partner.dto.student.paramsmapper.MStudentParams;
import com.partner.dto.student.paramsmapper.MStudentParamsDetail;
import com.partner.entity.MStudentEntity;
import com.partner.vo.base.CountryBaseCombox;
import com.partner.vo.offeritem.MAppAppalyStudentOfferItemVo;
import com.partner.vo.student.*;
import com.partner.vo.combox.StudentOfferItemCourtyCombox;
import com.partner.vo.combox.StudentOfferItemStepCombox;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student】的数据库操作Mapper
* @createDate 2024-12-05 19:03:19
* @Entity com.partner.entity.MStudent
*/
@Mapper
@DS("saledb")
public interface MStudentMapper extends BaseMapper<MStudentEntity> {


    List<StudentOfferItemCourtyCombox> getCountryComboxPeople(MStudentParams params);
    List<StudentOfferItemCourtyCombox> getCountryComboxApply(MStudentParams params);



    List<StudentOfferItemStepCombox> getAllStepOrderApplyNums(MStudentParams params);

    List<StudentOfferItemStepCombox> getAllPeopleNum(MStudentParams params);


    List<MStudentStepList> getPeopleStudentList(MStudentParams params);
    List<MStudentStepList> getApplyStudentList(MStudentParams params);

    List<MAppAppalyStudentOfferItemVo> getAppOfferItemStudents(MAppDefatStudentOfferItemDto params);

    List<MStudentHomeVo> getHomeStudents(MStudentParams params);

    MStudentDetailTmpVo getStudentInfo( @Param("studentId") Long studentId);



    int getPeopleStudentNum(MStudentParams params);



    int getPeopleStudentApplayTotal(MStudentParams params);

    List<StudentMonthOfferVo> getMonthApplayTotal(MStudentParams params);

    List<StudentMonthOfferVo> getMonthsubApplyTotal(MStudentParams params);




    List<StudentApplyRankingVo> getStudentsApplyInstitutionRanking(MStudentParams params);


    StudentOfferItemDetailVo getApplyDetail(MStudentParamsDetail params);



    int getMonthNewSum(MStudentParams params);
    int finshMonthNum(MStudentParams params);

    List<CountryBaseCombox> getCountryCombox(MStudentParams params);


    MStudentEntity selectAgentOne(MStudentSubAndAddOrEditDto dto);


}




