<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.InsuranceOrderSettlementMapper">


    <select id="getPayablePlanList" resultType="com.insurance.vo.settlement.PayablePlanVo">
        select id as payablePlanId,
        commission_rate as commissionRate,
        commission_amount as commissionAmount
        from ais_sale_center.m_payable_plan
        where id in
        <foreach item="item" collection="payablePlanIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectSettlementProgressOrder" resultType="java.lang.Integer">
        select count(1)
        from m_insurance_order o
                 inner join m_insurance_order_settlement s on s.fk_insurance_order_id = o.id
        where o.order_status = 2
          and o.fk_agent_id = #{agentId}
          and s.status_settlement in (2, 3)
    </select>

    <select id="getSettlementOrderStatistics"
            resultType="com.insurance.vo.settlement.SettlementOrderStatisticsVo">
        SELECT SUM(CASE WHEN s.status_settlement = 1 THEN 1 ELSE 0 END)       AS settableCount,
               SUM(CASE WHEN s.status_settlement IN (2, 3) THEN 1 ELSE 0 END) AS processCount,
               SUM(CASE WHEN s.status_settlement = 4 THEN 1 ELSE 0 END)       AS completeCount
        FROM m_insurance_order o
                 INNER JOIN m_insurance_order_settlement s ON s.fk_insurance_order_id = o.id
        WHERE o.order_status = 2
          AND o.fk_agent_id = #{agentId}
    </select>


    <select id="selectSettlementOrderByStatusAndDate"
            resultType="com.insurance.vo.settlement.SettlementOrderVo">
        select os.id as orderSettlementId,
        os.fk_num_opt_batch as numOptBatch,
        os.status_settlement as settlementStatus,
        os.fk_payable_plan_id as payablePlanId,
        o.*
        from m_insurance_order_settlement os
        inner join m_insurance_order o on o.id = os.fk_insurance_order_id
        <where>
            o.order_status = 2 and o.fk_agent_id = #{agentId}
            and os.status_settlement in
            <foreach item="item" collection="statusList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            <if test="date != null and date != ''">
                AND DATE_FORMAT(os.gmt_modified, '%Y-%m') =
                #{date}
            </if>
        </where>
    </select>
</mapper>