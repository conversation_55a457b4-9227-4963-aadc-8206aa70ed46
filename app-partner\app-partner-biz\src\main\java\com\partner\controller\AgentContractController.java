package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.ContractDto;
import com.partner.dto.MAgentContractDto;
import com.partner.dto.ais.CreateContractPdfDto;
import com.partner.service.MAgentContractService;
import com.partner.util.SecureEncryptUtil;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.contract.AgentContractDetailVo;
import com.partner.vo.contract.AgentContractInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Objects;

@Tag(description = "agentContract", name = "小程序-代理-合同")
@RestController
@RequestMapping("/agentContract")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class AgentContractController {

    private final MAgentContractService mAgentContractService;

    @Operation(summary = "查看-代理合同列表", description = "查看-代理合同列表")
    @PostMapping("/getContractPage")
    public R<Page<AgentContractInfoVo>> getContractPage(@RequestBody MAgentContractDto dto) {
        Page<Object> page = new Page<>(Objects.isNull(dto.getPage()) ? 1 : dto.getPage(),
                Objects.isNull(dto.getPageSize()) ? 20 : dto.getPageSize());
        return R.ok(mAgentContractService.getContractPage(page, dto));
    }


    @Operation(summary = "查看-代理合同详情", description = "查看-代理合同详情")
    @GetMapping("/getContractDetail/{contractId}")
    public R<AgentContractDetailVo> getContractDetail(@PathVariable("contractId") Long contractId) {
        return R.ok(mAgentContractService.getContractDetail(contractId));
    }

    @Operation(summary = "获取代理最新合同状态", description = "获取代理最新合同状态:0-无合同/1-有合同/2-未签署/3-待审核/4-审核通过/-4审核驳回/5续约在/6生效中/7已过期")
    @GetMapping("/getLatestAgentContractStatus")
    public R<Integer> getLatestAgentContractStatus() {
        return R.ok(mAgentContractService.getLatestAgentContractStatus());
    }

    @Operation(summary = "上传盖章合同", description = "上传盖章合同-公司性质的合同")
    @PostMapping("/uploadContract")
    public R uploadContract(@RequestParam("file") @NotNull(message = "文件不能为空") MultipartFile file,
                            @RequestParam("contractId") @Valid @NotNull(message = "合同ID为空") Long contractId,
                            String customerFileName) {
        mAgentContractService.uploadContract(file, contractId,customerFileName);
        return R.ok();
    }

    @Operation(summary = "合同签名", description = "合同签名")
    @PostMapping("/agentConfirmSignature")
    public R agentConfirmSignature(@RequestBody @Validated ContractDto contractDto) {
        mAgentContractService.agentConfirmSignature(contractDto);
        return R.ok();
    }

    @Operation(summary = "获取生成合同pdf加密参数", description = "获取生成合同pdf加密参数")
    @GetMapping("/getEncryptCreateContractPdfParam")
    public R<CreateContractPdfDto> getEncryptCreateContractPdfParam(@RequestParam("contractId") @Valid @NotNull(message = "合同Id不能为空") Long contractId,
                                                                    @RequestParam("agentId") @Valid @NotNull(message = "代理商Id不能为空") Long agentId,
                                                                    @RequestParam("contractTemplateMode") @Valid @NotNull(message = "合同模板模式不能为空") Integer contractTemplateMode) {
        return R.ok(mAgentContractService.getEncryptCreateContractPdfParam(contractId, agentId, 0, contractTemplateMode));
    }

    @Operation(summary = "解密测试", description = "解密测试")
    @PostMapping("/test")
    @SneakyThrows
    public R<Object> test(@RequestBody CreateContractPdfDto createContractPdfDto) {
        return R.ok(SecureEncryptUtil.decrypt(createContractPdfDto.getEncryptContractId(), createContractPdfDto.getSecret()));
    }

    @Operation(summary = "测试2", description = "测试2")
    @PostMapping("/test2")
    @SneakyThrows
    public R<Object> test2() {
        FzhUser user = SecurityUtils.getUser();
        return R.ok(UserInfoParamsUtils.getUserInfoParams(Long.valueOf(user.getFkTenantId()), user.getFkFromPlatformCode(), user.getId()));
    }

}
