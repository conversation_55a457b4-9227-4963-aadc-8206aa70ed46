package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.MReleaseInfoParamsDto;
import com.partner.entity.MReleaseInfoEntity;
import com.partner.vo.MReleaseInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface MReleaseInfoMapper extends BaseMapper<MReleaseInfoEntity> {

    /**
     * 分页查询发版信息
     *
     * @param page 分页对象
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<MReleaseInfoVo> searchReleaseInfo(Page<MReleaseInfoVo> page, @Param("params") MReleaseInfoParamsDto params);

}
 