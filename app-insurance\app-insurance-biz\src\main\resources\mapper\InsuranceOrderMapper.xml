<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.InsuranceOrderMapper">

    <resultMap id="InsuranceOrderResultMap" type="com.insurance.entity.InsuranceOrder">
        <id property="id" column="id"/>
        <result property="fkTenantId" column="fk_tenant_id"/>
        <result property="fkCompanyId" column="fk_company_id"/>
        <result property="fkAgentId" column="fk_agent_id"/>
        <result property="fkPartnerUserId" column="fk_partner_user_id"/>
        <result property="fkInsuranceCompanyId" column="fk_insurance_company_id"/>
        <result property="fkProductTypeId" column="fk_product_type_id"/>
        <result property="orderNum" column="order_num"/>
        <result property="insuranceNum" column="insurance_num"/>
        <result property="insuranceType" column="insurance_type"/>
        <result property="fkCurrencyTypeNum" column="fk_currency_type_num"/>
        <result property="insuranceAmount" column="insurance_amount"/>
        <result property="insurantName" column="insurant_name"/>
        <result property="insurantLastName" column="insurant_last_name"/>
        <result property="insurantFirstName" column="insurant_first_name"/>
        <result property="insurantGender" column="insurant_gender"/>
        <result property="insurantNationality" column="insurant_nationality"/>
        <result property="insurantBirthday" column="insurant_birthday"/>
        <result property="insurantPassportNum" column="insurant_passport_num"/>
        <result property="insurantEmail" column="insurant_email"/>
        <result property="insurantMobileAreaCode" column="insurant_mobile_area_code"/>
        <result property="insurantMobile" column="insurant_mobile"/>
        <result property="enrollmentTime" column="enrollment_time"/>
        <result property="graduationTime" column="graduation_time"/>
        <result property="fkAreaCountryIdTo" column="fk_area_country_id_to"/>
        <result property="insuranceStartTime" column="insurance_start_time"/>
        <result property="insuranceEndTime" column="insurance_end_time"/>
        <result property="orderRemark" column="order_remark"/>
        <result property="orderJson" column="order_json"/>
        <result property="orderTime" column="order_time"/>
        <result property="remark" column="remark"/>
        <result property="paymentType" column="payment_type"/>
        <result property="fkCreditCardId" column="fk_credit_card_id"/>
        <result property="mpPaymentOpenid" column="mp_payment_openid"/>
        <result property="mpPaymentStatus" column="mp_payment_status"/>
        <result property="orderStatus" column="order_status"/>
        <result property="orderMessage" column="order_message"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtCreateUser" column="gmt_create_user"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtModifiedUser" column="gmt_modified_user"/>

        <!-- 非数据库字段 -->
        <result property="productTypeKey" column="productTypeKey"/>
        <result property="mpPaymentAmount" column="mpPaymentAmount"/>
    </resultMap>



<!--    <select id="selectByOrderNum" resultType="com.insurance.entity.InsuranceOrder">-->
<!--        SELECT o.*,-->
<!--               t.type_key as productTypeKey-->
<!--        FROM m_insurance_order o-->
<!--                 LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id-->
<!--        WHERE order_num = #{orderNum}-->
<!--        limit 1-->
<!--    </select>-->

    <select id="selectByOrderNum" resultMap="InsuranceOrderResultMap">
        SELECT o.*,
               t.type_key AS productTypeKey,
               p.mp_payment_amount as mpPaymentAmount
        FROM m_insurance_order o
                 LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
                 LEFT JOIN m_insurance_order_mp_payment p ON p.fk_insurance_order_id = o.id
        WHERE o.order_num = #{orderNum}
        LIMIT 1
    </select>

    <select id="selectPendingOrders" resultType="com.insurance.entity.InsuranceOrder">
        SELECT o.*,
               t.type_key as productTypeKey
        FROM m_insurance_order o
                 LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
        WHERE o.order_status in (0, 1)
        ORDER BY order_status asc, o.gmt_create ASC
        LIMIT #{limit}
    </select>

    <select id="selectOrderDetailById" resultType="com.insurance.vo.insurance.order.OrderDetailVo">
        SELECT o.*,
               t.type_key  as productTypeKey,
               t.type_name as productTypeName,
               c.name      as insuranceCompanyName,
               p.mp_payment_amount as mpPaymentAmount
        FROM m_insurance_order o
                 LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
                 LEFT JOIN u_insurance_company c ON c.id = o.fk_insurance_company_id
                 LEFT JOIN m_insurance_order_mp_payment p ON p.fk_insurance_order_id = o.id
        WHERE o.id = #{id}
    </select>

    <select id="selectOrderList" resultType="com.insurance.entity.InsuranceOrder">
        SELECT o.*,
        t.type_key as productTypeKey
        FROM m_insurance_order o
        LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
        <where>
            o.fk_agent_id = #{agentId}
            <if test="orderStatus != null and orderStatus == 1">
                AND o.order_status in (0, 1)
            </if>
            <if test="orderStatus != null and orderStatus  != 1">
                AND o.order_status =
                #{orderStatus}
            </if>
            <if test="date != null and date != ''">
                AND DATE_FORMAT(o.gmt_create, '%Y-%m') =
                <choose>
                    <when test="date.length() == 7">
                        #{date}
                    </when>
                    <otherwise>
                        CONCAT(SUBSTRING_INDEX(#{date}, '-', 1), '-', LPAD(SUBSTRING_INDEX(#{date}, '-', -1), 2, '0'))
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY o.gmt_create desc
    </select>


    <select id="selectOrderCountByDate" resultType="com.insurance.vo.insurance.order.OrderStatisticsVo">
        SELECT
        COUNT(*) AS totalOrder,
        SUM(CASE WHEN o.order_status IN (0, 1) THEN 1 ELSE 0 END) AS processOrder,
        SUM(CASE WHEN o.order_status = 2 THEN 1 ELSE 0 END) AS completeOrder,
        SUM(CASE WHEN o.order_status = -2 THEN 1 ELSE 0 END) AS failOrder
        FROM
        m_insurance_order o
        <where>
            o.fk_agent_id = #{agentId}
            <if test="date != null and date != ''">
                AND DATE_FORMAT(o.gmt_create, '%Y-%m') =
                <choose>
                    <when test="date.length() == 7">
                        #{date}
                    </when>
                    <otherwise>
                        CONCAT(SUBSTRING_INDEX(#{date}, '-', 1), '-', LPAD(SUBSTRING_INDEX(#{date}, '-', -1), 2, '0'))
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="selectOrderBySettlementStatus" resultType="com.insurance.vo.settlement.SettlementOrderVo">
        select o.*,
        s.status_settlement as settlementStatus,
        s.fk_payable_plan_id as payablePlanId,
        s.fk_num_opt_batch as numOptBatch,
        S.id as orderSettlementId
        from m_insurance_order o
        inner join m_insurance_order_settlement s on s.fk_insurance_order_id = o.id
        <where>
            o.order_status = 2 and o.fk_agent_id = #{agentId}
            and exists (select 1 from m_insurance_order_settlement s where s.fk_insurance_order_id = o.id
            <choose>
                <when test="settlementStatus == 2">
                    and s.status_settlement in (2, 3)
                </when>
                <otherwise>
                    and s.status_settlement = #{settlementStatus}
                </otherwise>
            </choose>
            )
        </where>
        order by o.gmt_create desc
    </select>

    <select id="selectClientOrderList" resultType="com.insurance.vo.insurance.client.ClientOrderVo">
        select o.*,
               os.fk_payable_plan_id as payablePlanId
        from m_insurance_order o
                 left join m_insurance_order_settlement os on os.fk_insurance_order_id = o.id
        where o.insurant_email = #{email}
        order by o.gmt_create desc
    </select>
</mapper>