package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.MEventIncentiveParamsDto;
import com.partner.entity.MEventIncentiveEntity;
import com.partner.vo.MEventIncentiveVo;
import com.partner.vo.combox.CountryCombox;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MEventIncentiveMapper  extends BaseMapper<MEventIncentiveEntity> {

    IPage<MEventIncentiveVo> getMEventIncentivePage(Page page, @Param("query") MEventIncentiveParamsDto dto);

    List<CountryCombox> getCountryCombox(MEventIncentiveParamsDto dto);

    MEventIncentiveVo getMeventDetail( @Param("eventIncentiveId")Long eventIncentiveId, @Param("mMageAddress")String mMageAddress);
}
