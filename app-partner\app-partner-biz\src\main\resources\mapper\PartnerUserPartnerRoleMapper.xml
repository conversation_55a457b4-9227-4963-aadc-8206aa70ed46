<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.PartnerUserPartnerRoleMapper">

    <select id="selectRoleByPartnerUserId" resultType="com.partner.entity.PartnerRole">
        select r.*
        from m_partner_role r
        where r.id in (select rp.fk_partner_role_id
                       from r_partner_user_partner_role rp
                       where rp.fk_partner_user_id = #{partnerUserId})
    </select>


    <select id="selectMenuPermissionKeyByPartnerUserId" resultType="java.lang.String">
        select sm.permission_key
        from app_system_center.system_menu sm
        where sm.id in (select distinct(rm.fk_menu_id)
                        from app_partner_center.m_partner_role_menu rm
                        where rm.fk_partner_role_id in (select rpu.fk_partner_role_id
                                                        from app_partner_center.r_partner_user_partner_role rpu
                                                        where rpu.fk_partner_user_id = #{partnerUserId}))
    </select>
</mapper>