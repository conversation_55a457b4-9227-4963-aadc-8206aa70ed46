package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("r_coupon_fetch")
@Schema(description = "优惠卷领取表")
public class RCouponFetchEntity {
    @TableId(type = IdType.AUTO) // 使用数据库自增主键
    @Schema(description = "优惠券用户领取Id")
    private Long id;

    @Schema(description = "外键：优惠卷类型uuid")
    private Long fkCouponTypeId;

    @Schema(description = "外键：用户Id")
    private Long fkCouponUserId;

    @Schema(description = "外键：优惠券码")
    private String fkCouponCode;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学生email")
    private String studentEmail;


    @Schema(description = "学生NeeaId")
    private String studentNeeaId;

    @Schema(description = "拟考试日期")
    private String examDate;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "是否已兑换：0否/1是")
    private Boolean isUsed;


    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;

    // 确保优惠券码唯一
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
