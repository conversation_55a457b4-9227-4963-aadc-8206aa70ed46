package com.partner.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_partner_user_partner_role")
@Schema(description = "伙伴用户和伙伴角色关系")
public class PartnerUserPartnerRole extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "租户Id")
    private Long fkTenantId;

    @Schema(description = "伙伴用户Id")
    private Long fkPartnerUserId;

    @Schema(description = "伙伴角色Id")
    private Long fkPartnerRoleId;
}
