package com.partner.vo;

import com.partner.entity.RLivePartnerUserAppointmentEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AppointmentVo extends RLivePartnerUserAppointmentEntity {

    @Schema(description="用户名(中文或英文)")
    private String userName;

    @Schema(description = "用户头像-桶地址")
    private String fileKey;

    @Schema(description="用户中文名")
    private String name;
    @Schema(description="用户英文名")
    private String nameEn;

    public String getUserName() {
        if(name!=null && name.length()>0){
            userName=name;
        } else if (nameEn!=null && nameEn.length()>0) {
            userName=nameEn;
        }
        return userName;
    }
}
