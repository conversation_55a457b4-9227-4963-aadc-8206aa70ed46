package com.partner.dto.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "分配学生查询参数")
public class StudentApportionParamsDto   {

   /* @Schema(description = "代理UUID")
    @NotBlank(message = "代理UUID不能为空")
    private String agentUUID;

    @Schema(description = "公司ID")
    @NotNull(message = "公司Id不能为空")
    private Long companyId;*/

    @Schema(description = "角色ID")
    private Long fkRoleId;


    @Schema(description = "角色编码")
    private String roleCode;

}
