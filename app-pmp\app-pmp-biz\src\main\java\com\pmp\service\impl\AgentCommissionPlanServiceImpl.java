package com.pmp.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pmp.dto.CommissionDto;
import com.pmp.dto.DateDto;
import com.pmp.entity.*;
import com.pmp.enums.TerritoryRuleEnum;
import com.pmp.mapper.*;
import com.pmp.service.AgentCommissionMajorLevelCustomService;
import com.pmp.service.AgentCommissionPlanService;
import com.pmp.service.InstitutionCenterService;
import com.pmp.util.DateTimeUtils;
import com.pmp.util.TerritoryInfoMerger;
import com.pmp.util.UserInfoUtils;
import com.pmp.vo.commission.*;
import com.pmp.vo.institution.CountryVo;
import com.pmp.vo.institution.InstitutionVo;
import com.pmp.vo.institution.ProviderGroupVo;
import com.pmp.vo.institution.RegionVo;
import com.pmp.vo.partner.PartnerUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  10:37
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionPlanServiceImpl extends ServiceImpl<AgentCommissionPlanMapper, AgentCommissionPlan> implements AgentCommissionPlanService {

    @Autowired
    private AgentCommissionPlanMapper agentCommissionPlanMapper;
    @Autowired
    private AgentCommissionMajorLevelCustomService majorLevelCustomService;
    @Autowired
    private AgentCommissionMapper agentCommissionMapper;
    @Autowired
    private AgentCommissionMajorLevelCustomMapper agentCommissionMajorLevelCustomMapper;
    @Autowired
    private MajorLevelCustomMapper levelCustomMapper;
    @Autowired
    private AgentCommissionPlanInstitutionMapper planInstitutionMapper;
    @Autowired
    @Lazy
    private InstitutionCenterService institutionCenterService;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanTerritoryMapper commissionPlanTerritoryMapper;
    @Autowired
    private AgentCommissionTypeAgentMapper commissionTypeAgentMapper;
    @Autowired
    private UserInfoUtils userInfoUtils;
    @Value("${imgBaseUrl:https://hti-ais-images-dev-**********.cos.ap-shanghai.myqcloud.com}")
    private String imgBaseUrl;

    @Override
    public List<Long> getPlanIds(Long agentId, Long companyId, Date startDate) {
        if (Objects.isNull(startDate)) {
            startDate = DateTimeUtils.getCurrentDate();
        }
        if (Objects.isNull(agentId)) {
            PartnerUserVo partnerUser = userInfoUtils.getPartnerUser();
            agentId = partnerUser.getAgentId();
            companyId = partnerUser.getCompanyId();
        }
        //根据代理人、分公司、时间查询佣金方案
        return getPlanIdsByAgentId(agentId, companyId, startDate);
    }

    @Override
    public List<Long> getPlanIdsByAgentId(Long agentId, Long companyId, Date startDate) {
        List<Long> allPlanIds = new ArrayList<>();
        //只展示相应等级的方案
        Boolean onlyQueryCommission = Boolean.FALSE;
        //获取代理等级的方案
        AgentCommissionTypeAgent commissionType = commissionTypeAgentMapper.getAgentCommissionTypeAgentById(agentId);
        if (Objects.nonNull(commissionType) && Objects.nonNull(commissionType.getIsShowOnly()) && commissionType.getIsShowOnly().equals(1)) {
            onlyQueryCommission = Boolean.TRUE;
        }
        //获取当前用户下的提供商
        List<Long> allPlanProviderIds = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<>())
                .stream().map(AgentCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allPlanProviderIds)) {
            log.error("没有方案可用,代理ID:{},分公司ID:{} 日期:{}", agentId, companyId, startDate);
        }
        List<Long> providerIds = agentCommissionPlanMapper.getCurrentUserProviderIds(allPlanProviderIds, companyId);
        if (CollectionUtils.isEmpty(providerIds)) {
            log.error("当前用户没有可操作的提供商,代理ID:{},分公司ID:{} 日期:{}", agentId, companyId, startDate);
            return new ArrayList<>();
        }
        //查询默认方案
        if (!onlyQueryCommission) {
            List<Long> defaultPlanIds = agentCommissionPlanMapper.selectPlanIds(agentId,
                    companyId,
                    startDate,
                    1,
                    providerIds);
            if (CollectionUtils.isNotEmpty(defaultPlanIds)) {
                allPlanIds.addAll(defaultPlanIds);
            }
        }
        //查询代理等级的方案
        if (Objects.nonNull(commissionType)) {
            List<Long> commissionTypePlanIds = agentCommissionPlanMapper.selectPlanIds(agentId,
                    companyId,
                    startDate,
                    2,
                    providerIds);
            if (CollectionUtils.isNotEmpty(commissionTypePlanIds)) {
                allPlanIds.addAll(commissionTypePlanIds);
            }
        }
        if (CollectionUtils.isEmpty(allPlanIds)) {
            log.error("当前用户没有可用的佣金方案,代理ID:{},分公司ID:{} 日期:{}", agentId, companyId, startDate);
        }
        return allPlanIds;
    }

    @Override
    public List<AgentCommissionPlanVo> commissionPlanList(CommissionDto dto) {
        List<Long> planIds = getPlanIds(null, null, dto.getStartDate());
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        List<Long> institutionPlans = planInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .eq(AgentCommissionPlanInstitution::getFkInstitutionId, dto.getInstitutionId())
                        .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planIds))
                .stream().map(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(institutionPlans)) {
            return Collections.emptyList();
        }
        int year = DateTimeUtils.getYearFromDate(Objects.isNull(dto.getStartDate()) ? DateTimeUtils.getCurrentDate() : dto.getStartDate());
        InstitutionVo institutionDetail = institutionCenterService.institutionDetail(dto.getInstitutionId(), year);
        //集团-跟学校供应商关联
        List<AgentCommissionPlan> plans = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                .in(AgentCommissionPlan::getId, institutionPlans));
        List<Long> providerIds = plans.stream().map(AgentCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        Map<Long, String> groupMap = institutionCenterMapper.providerGroupMap(providerIds)
                .stream()
                .collect(Collectors.toMap(ProviderGroupVo::getProviderId, ProviderGroupVo::getName));
        Map<Long, List<PlanTerritoryVo>> planTerritoryMap = getPlanTerritoryMap(plans);
        //地区-跟学校提供商佣金方案关联
        List<AgentCommissionPlanVo> list = institutionPlans.stream().map(planId -> {
            AgentCommissionPlanVo result = new AgentCommissionPlanVo();
            //佣金方案基本信息
            AgentCommissionPlan plan = agentCommissionPlanMapper.selectById(planId);
            if (Objects.isNull(plan)) {
                return null;
            }
            BeanUtils.copyProperties(plan, result);
            //集团
            result.setGroup(groupMap.get(plan.getFkInstitutionProviderId()));
//            // 适用地区
//            if (Objects.nonNull(planTerritoryMap.get(plan.getFkInstitutionProviderCommissionPlanId()))) {
//                result.setTerritories(planTerritoryMap.get(plan.getFkInstitutionProviderCommissionPlanId()));
//            } else {
//                result.setTerritories(Arrays.asList(PlanTerritoryVo.createGlobalTerritory()));
//            }
            result.setInstitutionDetail(institutionDetail);
            //佣金明细
            result.setCommissions(getPlanCommissionList(planId));
            return result;
        }).collect(Collectors.toList());
        list = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return list;
    }

    @Override
    public AgentCommissionDetailVo commissionDetail(CommissionDto dto) {
        AgentCommissionDetailVo result = new AgentCommissionDetailVo();
        List<Long> planIds = getPlanIds(null, null, dto.getStartDate());
        if (CollectionUtils.isEmpty(planIds)) {
            return result;
        }
        List<Long> institutionPlans = planInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .eq(AgentCommissionPlanInstitution::getFkInstitutionId, dto.getInstitutionId())
                        .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planIds))
                .stream().map(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(institutionPlans)) {
            return result;
        }
        int year = DateTimeUtils.getYearFromDate(Objects.isNull(dto.getStartDate()) ? DateTimeUtils.getCurrentDate() : dto.getStartDate());
        InstitutionVo institutionDetail = institutionCenterService.institutionDetail(dto.getInstitutionId(), year);
        //集团-跟学校供应商关联
        List<AgentCommissionPlan> plans = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                .in(AgentCommissionPlan::getId, institutionPlans));
        List<Long> providerIds = plans.stream().map(AgentCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        Map<Long, ProviderGroupVo> groupMap = institutionCenterMapper.providerGroupMap(providerIds)
                .stream()
                .collect(Collectors.toMap(ProviderGroupVo::getProviderId, providerGroupVo -> providerGroupVo));
        Map<Long, List<PlanTerritoryVo>> planTerritoryMap = getPlanTerritoryMap(plans);

        AgentCommissionListVo allPlanCommissionList = new AgentCommissionListVo();
        List<TerritoryInfoVo> territoryInfos = new ArrayList<>();
        institutionPlans.stream().forEach(planId -> {
            AgentCommissionPlan plan = agentCommissionPlanMapper.selectById(planId);
            if (Objects.nonNull(plan)) {
                //佣金明细
                AgentCommissionListVo planCommissionList = getPlanCommissionList(planId);
                allPlanCommissionList.getAgentCommissionInfoList().addAll(planCommissionList.getAgentCommissionInfoList());
                allPlanCommissionList.getAgentCombinationList().addAll(planCommissionList.getAgentCombinationList());
                allPlanCommissionList.getAgentBonusList().addAll(planCommissionList.getAgentBonusList());

                TerritoryInfoVo territoryInfoVo = new TerritoryInfoVo();
                if (planTerritoryMap.containsKey(plan.getFkInstitutionProviderCommissionPlanId())) {
                    territoryInfoVo.setTerritories(planTerritoryMap.get(plan.getFkInstitutionProviderCommissionPlanId()));
                } else {
                    territoryInfoVo.setTerritories(Arrays.asList(PlanTerritoryVo.createGlobalTerritory()));
                }
                territoryInfoVo.setRemark(plan.getRemark());
                TerritoryInfoVo vo = agentCommissionPlanMapper.getTerritoryInfoVo(plan.getFkInstitutionProviderCommissionPlanId());
                if (Objects.nonNull(vo)) {
                    territoryInfoVo.setTerritory(vo.getTerritory());
                    territoryInfoVo.setTerritoryChn(vo.getTerritoryChn());
                    territoryInfoVo.setCourse(vo.getCourse());
                    territoryInfoVo.setCourseChn(vo.getCourseChn());
                }
                territoryInfos.add(territoryInfoVo);

                if (groupMap.containsKey(plan.getFkInstitutionProviderId())) {
                    result.setGroup(groupMap.get(plan.getFkInstitutionProviderId()).getName());
                    result.setGroupChn(groupMap.get(plan.getFkInstitutionProviderId()).getNameChn());
                }
            }
        });
        result.setInstitutionDetail(institutionDetail);
        result.setTerritoryInfos(territoryInfos);
        allPlanCommissionList.setAgentBonusList(sortBonusInfoList(allPlanCommissionList.getAgentBonusList()));
        result.setCommissions(allPlanCommissionList);
        return result;
    }

    @Override
    public List<CommissionDetail> getCommissionDetail(CommissionDto dto, Long agentId, Long companyId) {
        //获取学校详情
        int year = DateTimeUtils.getYearFromDate(Objects.isNull(dto.getStartDate()) ? DateTimeUtils.getCurrentDate() : dto.getStartDate());
        InstitutionVo institutionDetail = institutionCenterService.institutionDetail(dto.getInstitutionId(), year);

        List<Long> planIds = getPlanIds(agentId, companyId, dto.getStartDate());
        if (CollectionUtils.isEmpty(planIds)) {
            return CommissionDetail.initCommissionDetail(institutionDetail);
        }
        List<Long> institutionPlans = planInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .eq(AgentCommissionPlanInstitution::getFkInstitutionId, dto.getInstitutionId())
                        .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planIds))
                .stream().map(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(institutionPlans)) {
            return CommissionDetail.initCommissionDetail(institutionDetail);
        }
        //集团-跟学校供应商关联
        List<AgentCommissionPlan> plans = agentCommissionPlanMapper.selectList(
                new LambdaQueryWrapper<AgentCommissionPlan>()
                        .in(AgentCommissionPlan::getId, institutionPlans)
                        .orderByDesc(AgentCommissionPlan::getViewOrder)
                        .orderByDesc(AgentCommissionPlan::getGmtCreate)
        );
        List<Long> providerIds = plans.stream().map(AgentCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        Map<Long, ProviderGroupVo> groupMap = institutionCenterMapper.providerGroupMap(providerIds)
                .stream()
                .collect(Collectors.toMap(ProviderGroupVo::getProviderId, providerGroupVo -> providerGroupVo));
        Map<Long, List<PlanTerritoryVo>> planTerritoryMap = getPlanTerritoryMap(plans);

        return plans.stream().map(plan -> {
            CommissionDetail detail = new CommissionDetail();
            //佣金明细
            AgentCommissionListVo planCommissionList = getPlanCommissionList(plan.getId());
            TerritoryInfoVo territoryInfoVo = new TerritoryInfoVo();
            if (planTerritoryMap.containsKey(plan.getFkInstitutionProviderCommissionPlanId())) {
                territoryInfoVo.setTerritories(planTerritoryMap.get(plan.getFkInstitutionProviderCommissionPlanId()));
            } else {
                territoryInfoVo.setTerritories(Arrays.asList(PlanTerritoryVo.createGlobalTerritory()));
            }
            territoryInfoVo.setRemark(plan.getRemark());
            TerritoryInfoVo vo = agentCommissionPlanMapper.getTerritoryInfoVo(plan.getFkInstitutionProviderCommissionPlanId());
            if (Objects.nonNull(vo)) {
                territoryInfoVo.setTerritory(vo.getTerritory());
                territoryInfoVo.setTerritoryChn(vo.getTerritoryChn());
                territoryInfoVo.setCourse(vo.getCourse());
                territoryInfoVo.setCourseChn(vo.getCourseChn());
            }
            detail.setCommissions(planCommissionList);
            detail.setTerritoryInfos(territoryInfoVo);
            detail.setInstitutionDetail(institutionDetail);
            if (groupMap.containsKey(plan.getFkInstitutionProviderId())) {
                detail.setGroup(groupMap.get(plan.getFkInstitutionProviderId()).getName());
                detail.setGroupChn(groupMap.get(plan.getFkInstitutionProviderId()).getNameChn());
            }
            return detail;
        }).collect(Collectors.toList());
    }


    @Override
    public MergeCommissionVo getMergeCommission(CommissionDto dto, Long agentId, Long companyId,String highCommissionCode) {
        MergeCommissionVo result = new MergeCommissionVo();
        List<CommissionDetail> commissionDetails = getCommissionDetail(dto, agentId, companyId);
        if (CollectionUtils.isNotEmpty(commissionDetails)) {
            //集团名称、学校详情
            result.setInstitutionDetail(commissionDetails.get(0).getInstitutionDetail());
            result.setGroup(commissionDetails.get(0).getGroup());
            result.setGroupChn(commissionDetails.get(0).getGroupChn());
            //查询高佣信息
            DateDto dateDto = DateDto.builder()
                    .startDate(dto.getStartDate())
                    .agentId(agentId)
                    .companyId(companyId).build();
            List<InstitutionVo> highCommissionList = institutionCenterService.highCommissionList(Arrays.asList(commissionDetails.get(0).getInstitutionDetail().getInstitutionId()),
                    companyId, dateDto,highCommissionCode);
            if (CollectionUtils.isNotEmpty(highCommissionList)) {
                InstitutionVo institutionDetail = result.getInstitutionDetail();
                institutionDetail.setIsHighCommission(true);
                institutionDetail.setHighCommissionDescription(highCommissionList.get(0).getHighCommissionDescription());
                if (StringUtils.isNotBlank(highCommissionList.get(0).getHighCommissionPic())) {
                    institutionDetail.setHighCommissionPic(imgBaseUrl + highCommissionList.get(0).getHighCommissionPic());
                }
            }
            // 合并组合
            List<AgentCommissionListVo.AgentCombinationInfo> allCombinations = commissionDetails.stream()
                    .map(CommissionDetail::getCommissions)
                    .filter(Objects::nonNull)
                    .flatMap(c -> Optional.ofNullable(c.getAgentCombinationList()).orElse(Collections.emptyList()).stream())
                    .collect(Collectors.toList());
            result.setAgentCombinationList(allCombinations);
            // 合并bonus
            List<AgentCommissionListVo.AgentBonusInfo> allBonusList = commissionDetails.stream()
                    .map(CommissionDetail::getCommissions)
                    .filter(Objects::nonNull)
                    .flatMap(c -> Optional.ofNullable(c.getAgentBonusList()).orElse(Collections.emptyList()).stream())
                    .collect(Collectors.toList());
            result.setAgentBonusList(allBonusList);
            //合并明细
            List<TerritoryInfoVo> territoryInfoVos = commissionDetails.stream()
                    .filter(Objects::nonNull)
                    .map(detail -> {
                        TerritoryInfoVo territory = detail.getTerritoryInfos();
                        if (Objects.isNull(territory)) {
                            return null;
                        }
                        AgentCommissionListVo commissions = detail.getCommissions();
                        if (commissions != null && CollectionUtils.isNotEmpty(commissions.getAgentCommissionInfoList())) {
                            // 设置 agentCommissionInfoList 到 territoryInfo
                            territory.setAgentCommissionInfoList(commissions.getAgentCommissionInfoList());
                        }
                        territory.setKey(UuidUtils.generateUuid().replaceAll("-", ""));
                        return territory;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            result.setTerritoryInfos(TerritoryInfoMerger.merge(territoryInfoVos));
        }
        return result;
    }

    @Override
    public Integer highCommissionCount(DateDto startDate) {
        PartnerUserVo partnerUser = userInfoUtils.getPartnerUser();
        List<InstitutionVo> highedCommissionList = institutionCenterService.highCommissionList(Collections.emptyList(), partnerUser.getCompanyId(), startDate,null);
        return highedCommissionList.size();
    }

    private List<AgentCommissionListVo.AgentBonusInfo> sortBonusInfoList(List<AgentCommissionListVo.AgentBonusInfo> bonusInfoList) {
        if (CollectionUtils.isEmpty(bonusInfoList)) {
            return new ArrayList<>();
        }
        List<Long> allLevelIds = bonusInfoList.stream()
                .filter(bonus -> CollectionUtils.isNotEmpty(bonus.getLevelIds()))
                .flatMap(bonus -> bonus.getLevelIds().stream())
                .distinct()
                .collect(Collectors.toList());
        //排序,优先排只有一个课程的，只有一个课程的根据viewOrder降序排序，多个课程等级的根据viewOrder的总和降序
        if (CollectionUtils.isEmpty(allLevelIds)) {
            return bonusInfoList;
        }
        Map<Long, Integer> levelViewOrderMap = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                        .in(MajorLevelCustom::getId, allLevelIds)).stream()
                .collect(Collectors.toMap(
                        MajorLevelCustom::getId,
                        MajorLevelCustom::getViewOrder,
                        Integer::max));
        bonusInfoList.sort((a, b) -> {
            int aSize = a.getLevelIds() != null ? a.getLevelIds().size() : 0;
            int bSize = b.getLevelIds() != null ? b.getLevelIds().size() : 0;

            // 1. 先按 levelId 数量，1个的排前面
            if (aSize == 1 && bSize != 1) {
                return -1;
            }
            if (aSize != 1 && bSize == 1) {
                return 1;
            }

            // 2. 如果都是 size==1，就用 viewOrder 倒序
            if (aSize == 1 && bSize == 1) {
                Integer aOrder = levelViewOrderMap.getOrDefault(a.getLevelIds().get(0), 0);
                Integer bOrder = levelViewOrderMap.getOrDefault(b.getLevelIds().get(0), 0);
                return Integer.compare(bOrder, aOrder);
            }

            // 3. 都是 size > 1，按 viewOrder 总和倒序
            int aSum = a.getLevelIds().stream()
                    .mapToInt(id -> levelViewOrderMap.getOrDefault(id, 0))
                    .sum();
            int bSum = b.getLevelIds().stream()
                    .mapToInt(id -> levelViewOrderMap.getOrDefault(id, 0))
                    .sum();
            return Integer.compare(bSum, aSum);
        });
        return bonusInfoList;
    }

    private AgentCommissionListVo getPlanCommissionList(Long planId) {
        AgentCommissionListVo list = new AgentCommissionListVo();
        list.setAgentCommissionInfoList(getAgentCommissionList(planId));
        //组合佣金方案
        list.setAgentCombinationList(getCombinationList(planId));
        //bonus佣金方案
        list.setAgentBonusList(getBonusInfoList(planId));
        return list;
    }

    private List<AgentCommissionListVo.AgentCommissionInfo> getAgentCommissionList(Long planId) {
        List<AgentCommission> commissionList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getIsActive, 1)
                .eq(AgentCommission::getCommissionType, 1));
        return getCommonCommissionList(commissionList);
    }

    private List<AgentCommissionListVo.AgentCombinationInfo> getCombinationList(Long planId) {
        List<AgentCommission> combinationList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getIsActive, 1)
                .eq(AgentCommission::getCommissionType, 3));
        Map<String, List<AgentCommission>> packageList = combinationList.stream()
                .collect(Collectors.groupingBy(AgentCommission::getPackageKey));
        List<AgentCommissionListVo.AgentCombinationInfo> combinationInfoList = packageList.entrySet().stream().map(entry -> {
            AgentCommissionListVo.AgentCombinationInfo info = new AgentCommissionListVo.AgentCombinationInfo();
            info.setPackageKey(entry.getKey());
            info.setPackageName(entry.getValue().get(0).getPackageName());
            info.setPackageNameNative(entry.getValue().get(0).getPackageNameNative());
            List<AgentCommissionListVo.AgentCommissionInfo> commissionInfoList = getCommonCommissionList(entry.getValue());
            info.setAgentCommissionInfos(commissionInfoList);
            List<Long> commissionIds = entry.getValue().stream().map(AgentCommission::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(commissionIds)) {
                List<Long> levelIds = agentCommissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                        .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commissionIds)
                ).stream().map(AgentCommissionMajorLevelCustom::getFkMajorLevelCustomId).distinct().collect(Collectors.toList());
                info.setLevelIds(levelIds);
                if (CollectionUtils.isNotEmpty(levelIds)) {
                    List<String> names = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                                    .in(MajorLevelCustom::getId, levelIds))
                            .stream().map(MajorLevelCustom::getCustomName).distinct().collect(Collectors.toList());
                    info.setLevelNames(names);
                }
            }
            return info;
        }).collect(Collectors.toList());
        return combinationInfoList;
    }

    private List<AgentCommissionListVo.AgentBonusInfo> getBonusInfoList(Long planId) {
        List<AgentCommission> bonusList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getIsActive, 1)
                .eq(AgentCommission::getCommissionType, 2));
        List<AgentCommissionListVo.AgentBonusInfo> bonusInfoList = bonusList.stream().map(commissionInfo -> {
            AgentCommissionListVo.AgentBonusInfo bonusInfo = new AgentCommissionListVo.AgentBonusInfo();
            BeanUtils.copyProperties(commissionInfo, bonusInfo);
            List<Long> levelIds = agentCommissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                    .eq(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commissionInfo.getId())
            ).stream().map(AgentCommissionMajorLevelCustom::getFkMajorLevelCustomId).distinct().collect(Collectors.toList());
            bonusInfo.setLevelIds(levelIds);
            if (CollectionUtils.isNotEmpty(levelIds)) {
                List<String> names = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                                .in(MajorLevelCustom::getId, levelIds))
                        .stream().map(MajorLevelCustom::getCustomName).distinct().collect(Collectors.toList());
                bonusInfo.setLevelNames(names);
            }
            return bonusInfo;
        }).collect(Collectors.toList());
        return bonusInfoList;
    }

    private List<AgentCommissionListVo.AgentCommissionInfo> getCommonCommissionList(List<AgentCommission> commissionList) {
        if (CollectionUtils.isEmpty(commissionList)) {
            return new ArrayList<>();
        }
        List<CommissionMajorLevelVo> commissionMajorLevelVos = majorLevelCustomService.getCommissionMajorLevelList(commissionList.stream().map(AgentCommission::getId).collect(Collectors.toList()));
        Map<Long, List<CommissionMajorLevelVo>> commissionLevelGroup = commissionMajorLevelVos.stream()
                .collect(Collectors.groupingBy(CommissionMajorLevelVo::getCommissionId));
        List<AgentCommissionListVo.AgentCommissionInfo> infoList = commissionList.stream().map(commissionInfo -> {
            AgentCommissionListVo.AgentCommissionInfo info = new AgentCommissionListVo.AgentCommissionInfo();
            BeanUtils.copyProperties(commissionInfo, info);
            info.setLevelId(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getLevelId());
            info.setCustomName(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getCustomName());
            info.setCustomNameChn(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getCustomNameChn());
            return info;
        }).collect(Collectors.toList());
        return infoList;
    }

    private Map<Long, List<PlanTerritoryVo>> getPlanTerritoryMap(List<AgentCommissionPlan> plans) {
        if (CollectionUtils.isEmpty(plans)) {
            return new HashMap<>();
        }
        List<Long> planIds = plans.stream().map(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId).distinct().collect(Collectors.toList());
        // 1. 查询当前这些方案下的所有国家/大区数据
        List<InstitutionProviderCommissionPlanTerritory> territories = commissionPlanTerritoryMapper.selectList(
                new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .in(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, planIds)
        );

        // 2. 如果没有记录，直接返回空 Map
        if (CollectionUtils.isEmpty(territories)) {
            return new HashMap<>();
        }

        // 3. 提取所有国家ID（去重）
        List<Long> allCountryIds = territories.stream()
                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 4. 提取所有大区ID（去重）
        List<Long> allRegionIds = territories.stream()
                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 5. 批量查询国家信息（英文、中文名称）
        List<CountryVo> countryVos = institutionCenterMapper.queryCountryListByIds(CollectionUtils.isNotEmpty(allCountryIds) ? allCountryIds : Collections.singletonList(0L));

        // 6. 批量查询大区信息（英文、中文名称）
        List<RegionVo> regionVos = institutionCenterMapper.queryRegionListByIds(CollectionUtils.isNotEmpty(allRegionIds) ? allRegionIds : Collections.singletonList(0L));

        // 7. 构造国家ID → 英文名称映射
        Map<Long, String> countryNameMap = countryVos.stream()
                .collect(Collectors.toMap(
                        CountryVo::getCountryId,
                        CountryVo::getName,
                        (oldValue, newValue) -> newValue
                ));

        // 8. 构造国家ID → 中文名称映射
        Map<Long, String> countryNameChnMap = countryVos.stream()
                .collect(Collectors.toMap(
                        CountryVo::getCountryId,
                        CountryVo::getNameChn,
                        (oldValue, newValue) -> newValue
                ));

        // 9. 构造大区ID → 英文名称映射
        Map<Long, String> regionNameMap = regionVos.stream()
                .collect(Collectors.toMap(
                        RegionVo::getRegionId,
                        RegionVo::getName,
                        (oldValue, newValue) -> newValue
                ));

        // 10. 构造大区ID → 中文名称映射
        Map<Long, String> regionNameChnMap = regionVos.stream()
                .collect(Collectors.toMap(
                        RegionVo::getRegionId,
                        RegionVo::getNameChn,
                        (oldValue, newValue) -> newValue
                ));

        // 11. 分组返回：Map<方案ID, List<PlanTerritoryVo>>
        Map<Long, List<PlanTerritoryVo>> result = territories.stream()
                .collect(Collectors.groupingBy(
                        // 11.1 外层按方案ID分组
                        InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId,
                        Collectors.collectingAndThen(
                                // 11.2 内层按 isInclude 类型进一步分组
                                Collectors.groupingBy(InstitutionProviderCommissionPlanTerritory::getIsInclude),
                                innerMap -> innerMap.entrySet().stream()
                                        .sorted(Map.Entry.comparingByKey()) // 按 isInclude 升序
                                        .map(entry -> {
                                            Integer isInclude = entry.getKey(); // 当前规则类型
                                            List<InstitutionProviderCommissionPlanTerritory> items = entry.getValue();

                                            PlanTerritoryVo vo = new PlanTerritoryVo();
                                            vo.setIsInclude(isInclude);

                                            // 11.3 设置规则描述（中英文）
                                            TerritoryRuleEnum ruleEnum = TerritoryRuleEnum.getEnumByCode(isInclude);
                                            vo.setDescription(ruleEnum != null ? ruleEnum.getMsg() : "");
                                            vo.setDescriptionChn(ruleEnum != null ? ruleEnum.getMsgChn() : "");

                                            // 11.4 提取国家ID列表（非空）
                                            List<Long> countryIds = items.stream()
                                                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
                                                    .filter(Objects::nonNull)
                                                    .distinct()
                                                    .collect(Collectors.toList());
                                            vo.setTerritoryIds(countryIds);

                                            // 11.5 设置国家名称（中英文，逗号拼接）
                                            if (CollectionUtils.isNotEmpty(countryIds)) {
                                                String countryNames = countryIds.stream()
                                                        .map(countryNameMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setTerritories(countryNames);

                                                String countryNamesChn = countryIds.stream()
                                                        .map(countryNameChnMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setTerritoriesChn(countryNamesChn);
                                            }

                                            // 11.6 提取大区ID列表（非空）
                                            List<Long> regionIds = items.stream()
                                                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId)
                                                    .filter(Objects::nonNull)
                                                    .distinct()
                                                    .collect(Collectors.toList());

                                            // 11.7 设置大区名称（中英文，逗号拼接）
                                            if (CollectionUtils.isNotEmpty(regionIds)) {
                                                String regionNames = regionIds.stream()
                                                        .map(regionNameMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setRegions(regionNames);

                                                String regionNamesChn = regionIds.stream()
                                                        .map(regionNameChnMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setRegionsChn(regionNamesChn);
                                            }

                                            return vo;
                                        })
                                        .collect(Collectors.toList())
                        )
                ));

        return result;
    }

//    private Map<Long, List<PlanTerritoryVo>> getPlanTerritoryMap(List<AgentCommissionPlan> plans) {
//        if (CollectionUtils.isEmpty(plans)) {
//            return new HashMap<>();
//        }
//        List<Long> providerCommissionPlanIds = plans.stream().map(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId).distinct().collect(Collectors.toList());
//        List<InstitutionProviderCommissionPlanTerritory> territories = commissionPlanTerritoryMapper
//                .selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                        .in(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanIds));
//        if (CollectionUtils.isEmpty(territories)) {
//            return new HashMap<>();
//        }
//        List<Long> allCountryIds = territories.stream().map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId).distinct().collect(Collectors.toList());
//        List<CountryVo> countryVos = institutionCenterMapper.queryCountryListByIds(allCountryIds);
//        Map<Long, String> territoryNameMap = countryVos.stream()
//                .collect(Collectors.toMap(
//                        CountryVo::getCountryId,
//                        CountryVo::getName,
//                        (oldValue, newValue) -> newValue));
//        Map<Long, String> territoryNameChnMap = countryVos.stream()
//                .collect(Collectors.toMap(
//                        CountryVo::getCountryId,
//                        CountryVo::getNameChn,
//                        (oldValue, newValue) -> newValue));
//        // 按方案ID和is_include分组，构造PlanTerritoryVo列表
//        Map<Long, List<PlanTerritoryVo>> result = territories.stream()
//                .collect(Collectors.groupingBy(
//                        // 外层分组：按方案ID
//                        InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId,
//                        Collectors.collectingAndThen(
//                                // 内层分组：按is_include值分组
//                                Collectors.groupingBy(InstitutionProviderCommissionPlanTerritory::getIsInclude),
//                                innerMap -> innerMap.entrySet().stream()
//                                        .sorted(Map.Entry.comparingByKey()).map(entry -> {
//                                            // 构造PlanTerritoryVo对象
//                                            PlanTerritoryVo vo = new PlanTerritoryVo();
//                                            vo.setIsInclude(entry.getKey());
//                                            vo.setDescription(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(entry.getKey()))
//                                                    ? TerritoryRuleEnum.getEnumByCode(entry.getKey()).getMsg() : "");
//                                            vo.setDescriptionChn(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(entry.getKey()))
//                                                    ? TerritoryRuleEnum.getEnumByCode(entry.getKey()).getMsgChn() : "");
//                                            // 提取国家ID列表（过滤null值）
//                                            List<Long> territoryIds = entry.getValue().stream()
//                                                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
//                                                    .filter(Objects::nonNull)
//                                                    .collect(Collectors.toList());
//                                            vo.setTerritoryIds(territoryIds);
//                                            if (CollectionUtils.isNotEmpty(territoryIds)) {
//                                                String countryNames = territoryIds.stream()
//                                                        .filter(territoryNameMap::containsKey)
//                                                        .map(territoryNameMap::get)
//                                                        .collect(Collectors.joining(", "));
//                                                vo.setTerritories(countryNames);
//                                                String countryNamesChn = territoryIds.stream()
//                                                        .filter(territoryNameChnMap::containsKey)
//                                                        .map(territoryNameChnMap::get)
//                                                        .collect(Collectors.joining(", "));
//                                                vo.setTerritoriesChn(countryNamesChn);
//                                            }
//                                            //如果是auOnshore和UKOnshore,不展示国家
//                                            if (TerritoryRuleEnum.ON_SHORE.getCode().equals(entry.getKey())
//                                                    || TerritoryRuleEnum.UK_SHORE.getCode().equals(entry.getKey())) {
//                                                vo.setTerritories(StringUtils.EMPTY);
//                                                vo.setTerritoriesChn(StringUtils.EMPTY);
//                                            }
//                                            return vo;
//                                        }).collect(Collectors.toList())
//                        )
//                ));
//        return result;
//    }

}
