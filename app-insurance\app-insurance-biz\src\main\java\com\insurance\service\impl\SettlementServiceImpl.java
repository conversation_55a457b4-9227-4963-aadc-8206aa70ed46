package com.insurance.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.insurance.entity.InsuranceOrderSettlement;
import com.insurance.enums.SettlementStatusEnum;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.mapper.InsuranceOrderSettlementMapper;
import com.insurance.service.SettlementService;
import com.insurance.util.ExchangeRateUtils;
import com.insurance.util.PartnerUserUtils;
import com.insurance.vo.settlement.PayablePlanVo;
import com.insurance.vo.settlement.RateDetail;
import com.insurance.vo.settlement.SettlementOrderStatisticsVo;
import com.insurance.vo.settlement.SettlementOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class SettlementServiceImpl extends ServiceImpl<InsuranceOrderSettlementMapper, InsuranceOrderSettlement> implements SettlementService {

    @Autowired
    private InsuranceOrderMapper orderMapper;
    @Autowired
    private InsuranceOrderSettlementMapper settlementMapper;
    @Autowired
    private ExchangeRateUtils exchangeRateUtils;

    @Override
    public List<SettlementOrderVo> getSettlementOrderList(Integer settlementStatus, String date) {
        //settlementStatus-1:可结算;2结算中;4已结算
        //可结算-》status_settlement=1
        //结算中-》status_settlement=2/3
        //已结算-》status_settlement=4
        List<SettlementOrderVo> orders = orderMapper.selectOrderBySettlementStatus(Objects.isNull(settlementStatus) ? 1 : settlementStatus,
                PartnerUserUtils.getCurrentAgentId(), date);
        List<Integer> progressStatus = Arrays.asList(SettlementStatusEnum.AGENT_CONFIRMED.getCode(), SettlementStatusEnum.FINANCE_CONFIRMED.getCode());
        Map<Long, PayablePlanVo> finalPlanMap = getPlanMap(orders);
        orders.stream().forEach(order -> {
            if (progressStatus.contains(order.getSettlementStatus())) {
                order.setSettlementStatus(SettlementStatusEnum.AGENT_CONFIRMED.getCode());
            }
            BigDecimal commissionRate = finalPlanMap.getOrDefault(order.getPayablePlanId(), new PayablePlanVo()).getCommissionRate();
            if (Objects.isNull(commissionRate)) {
                commissionRate = new BigDecimal("15");
            }
            order.setCommissionRate(commissionRate);
            order.setCommissionAmount(order.getInsuranceAmount()
                    .multiply(commissionRate.divide(new BigDecimal("100").setScale(2, RoundingMode.HALF_UP)))
                    .setScale(2, RoundingMode.HALF_UP));
        });
        return orders;
    }


    @Override
    public SettlementOrderStatisticsVo getSettlementOrderStatistics(String date) {
        SettlementOrderStatisticsVo result = new SettlementOrderStatisticsVo();
        List<SettlementOrderVo> allOrdetList = settlementMapper.selectSettlementOrderByStatusAndDate(PartnerUserUtils.getCurrentAgentId(),
                Arrays.asList(SettlementStatusEnum.AGENT_CONFIRMED.getCode(), SettlementStatusEnum.FINANCE_CONFIRMED.getCode(), SettlementStatusEnum.COMPLETED.getCode()), date);

        Set<String> currencyTypeSet = allOrdetList.stream().map(SettlementOrderVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //查询汇率-目标币种是人民币
        Map<String, BigDecimal> exchangeRateMap = new HashMap<>();
        currencyTypeSet.stream().forEach(currencyType -> {
            //查询汇率
            RateDetail rateDetail = exchangeRateUtils.getRateDetail(currencyType, "CNY");
            exchangeRateMap.put(currencyType, rateDetail.getRate());
        });

        //已结算金额-所选日期内的所有处于结算完成状态的结算单金额总和
        List<SettlementOrderVo> completeList = allOrdetList.stream().filter(order -> order.getSettlementStatus().equals(SettlementStatusEnum.COMPLETED.getCode()))
                .collect(Collectors.toList());
        BigDecimal completeAmount = countTargetAmount(exchangeRateMap, completeList);


        //可结算金额-所选日期内的所有不是结算完成状态的订单金额总和，包括已提交结算但未完成的订单
        //代理确认+财务确认
        List<Integer> unCompleteStatusList = Arrays.asList(SettlementStatusEnum.AGENT_CONFIRMED.getCode(), SettlementStatusEnum.FINANCE_CONFIRMED.getCode());
        List<SettlementOrderVo> unCompleteList = allOrdetList.stream().filter(order -> unCompleteStatusList.contains(order.getSettlementStatus()))
                .collect(Collectors.toList());
        BigDecimal unCompleteAmount = countTargetAmount(exchangeRateMap, unCompleteList);

        //已提交结算金额-已提交的结算单金额
        //代理确认
        List<SettlementOrderVo> submittedList = allOrdetList.stream().filter(order -> order.getSettlementStatus().equals(SettlementStatusEnum.AGENT_CONFIRMED.getCode()))
                .collect(Collectors.toList());
        BigDecimal submittedAmount = countTargetAmount(exchangeRateMap, submittedList);

        result.setAlreadySettledAmount(completeAmount);
        result.setUnSettledAmount(unCompleteAmount);
        result.setSubmittedAmount(submittedAmount);
        return result;
    }

    private BigDecimal countTargetAmount(Map<String, BigDecimal> exchangeRateMap, List<SettlementOrderVo> orderList) {

        Map<Long, PayablePlanVo> finalPlanMap = getPlanMap(orderList);
        //按币种分组统计下单总金额-按照结算比例计算
//        Map<String, BigDecimal> currencyAmountMap = orderList.stream()
//                .filter(vo -> Objects.nonNull(vo.getFkCurrencyTypeNum()) && Objects.nonNull(vo.getInsuranceAmount()))
//                .collect(Collectors.groupingBy(
//                        SettlementOrderVo::getFkCurrencyTypeNum,
//                        Collectors.reducing(BigDecimal.ZERO, SettlementOrderVo::getInsuranceAmount, BigDecimal::add)));

        Map<String, BigDecimal> currencyAmountMap = orderList.stream()
                .filter(vo -> Objects.nonNull(vo.getFkCurrencyTypeNum())
                        && Objects.nonNull(vo.getInsuranceAmount()) && Objects.nonNull(vo.getPayablePlanId()))
                .collect(Collectors.groupingBy(
                        SettlementOrderVo::getFkCurrencyTypeNum,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                vo -> {
                                    BigDecimal rate = Optional.ofNullable(finalPlanMap.get(vo.getPayablePlanId()))
                                            .map(PayablePlanVo::getCommissionRate)
                                            .orElse(new BigDecimal("15"));
                                    return vo.getInsuranceAmount()
                                            .multiply(rate.divide(new BigDecimal("100").setScale(2, RoundingMode.HALF_UP)))
                                            .setScale(2, RoundingMode.HALF_UP);
                                },
                                BigDecimal::add)
                ));

        //计算每个币种转成人名币后的总额
        AtomicReference<BigDecimal> targetAmountRef = new AtomicReference<>(BigDecimal.ZERO);
        currencyAmountMap.keySet().forEach(currencyType -> {
            BigDecimal originAmount = currencyAmountMap.getOrDefault(currencyType, BigDecimal.ZERO);
            BigDecimal rate = exchangeRateMap.get(currencyType);
            if (Objects.isNull(rate)) {
                log.error("佣金统计,未查询到汇率-{}", currencyType);
                throw new InsuranceGlobalException("获取汇率失败");
            }
            BigDecimal amount = originAmount.multiply(rate);
            targetAmountRef.updateAndGet(prev -> prev.add(amount));
        });
        BigDecimal targetAmount = targetAmountRef.get().setScale(2, RoundingMode.HALF_UP);
        return targetAmount;
    }


    private Map<Long, PayablePlanVo> getPlanMap(List<SettlementOrderVo> orderList) {
        Map<Long, PayablePlanVo> planMap = new HashMap<>();
        List<Long> payableIds = orderList.stream().map(SettlementOrderVo::getPayablePlanId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(payableIds)) {
            planMap = settlementMapper.getPayablePlanList(payableIds)
                    .stream()
                    .collect(Collectors.toMap(
                            PayablePlanVo::getPayablePlanId,
                            Function.identity()));
        }
        return planMap;
    }

}
