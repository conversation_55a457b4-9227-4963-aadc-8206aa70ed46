package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.work.MFeedbackOrderDto;
import com.partner.entity.MFeedbackOrderEntity;
import com.partner.entity.UFeedbackOrderTypeEntity;
import com.partner.vo.work.MFeedbackOrderDetailVo;
import com.partner.vo.work.MFeedbackOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_feedback_order】的数据库操作Mapper
* @createDate 2025-05-23 10:16:24
* @Entity com.partner.entity.MFeedbackOrder
*/
@Mapper
@DS("aisplatformdb")
public interface MFeedbackOrderMapper extends BaseMapper<MFeedbackOrderEntity> {

    IPage<MFeedbackOrderVo> selectListPage(Page page, @Param("query") MFeedbackOrderDto params);


    MFeedbackOrderDetailVo getMFeedbackDetail(Long id);


    List<UFeedbackOrderTypeEntity> getUFeedbackOrderType();


}




