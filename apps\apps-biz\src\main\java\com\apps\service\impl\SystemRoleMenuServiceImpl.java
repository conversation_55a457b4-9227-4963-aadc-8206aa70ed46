package com.apps.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.dto.system.SaveRoleMenuDto;
import com.apps.api.entity.SystemRoleMenuEntity;
import com.apps.api.entity.SystemUserMenuEntity;
import com.apps.api.entity.SystemUserRoleEntity;
import com.apps.api.vo.system.MenuTreeVo;
import com.apps.mapper.SystemRoleMenuMapper;
import com.apps.mapper.SystemUserMenuMapper;
import com.apps.mapper.SystemUserRoleMapper;
import com.apps.service.SystemMenuService;
import com.apps.service.SystemRoleMenuService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SystemRoleMenuServiceImpl extends ServiceImpl<SystemRoleMenuMapper, SystemRoleMenuEntity> implements SystemRoleMenuService {

    private final SystemRoleMenuMapper systemRoleMenuMapper;
    private final SystemMenuService menuService;
    private final SystemUserRoleMapper userRoleMapper;
    private final SystemUserMenuMapper userMenuMapper;

    @Override
    public List<MenuTreeVo> getRoleMenu(Long roleId) {
        List<Long> idList = systemRoleMenuMapper.selectList(new LambdaQueryWrapper<SystemRoleMenuEntity>()
                        .eq(SystemRoleMenuEntity::getFkRoleId, roleId))
                .stream().map(SystemRoleMenuEntity::getFkMenuId).collect(Collectors.toList());
        return menuService.getMenuTree(idList);
    }

    @Override
    public List<MenuTreeVo> getUserMenu(Long userId) {
        return menuService.getMenuTree(getUserMenuIds(userId));
    }

    @Override
    public void saveRoleMenu(SaveRoleMenuDto roleMenuDto) {
        if (CollectionUtils.isEmpty(roleMenuDto.getMenuIds())) {
            this.remove(new LambdaQueryWrapper<SystemRoleMenuEntity>().eq(SystemRoleMenuEntity::getFkMenuId, roleMenuDto.getRoleId()));
            return;
        }
        //先查询角色目前已有的菜单权限
        List<Long> currentMenuIds = systemRoleMenuMapper.selectList(new LambdaQueryWrapper<SystemRoleMenuEntity>()
                        .eq(SystemRoleMenuEntity::getFkRoleId, roleMenuDto.getRoleId()))
                .stream().map(SystemRoleMenuEntity::getFkMenuId).collect(Collectors.toList());
        //获取新增的菜单权限
        List<Long> addMenuIds = roleMenuDto.getMenuIds().stream()
                .filter(menuId -> !currentMenuIds.contains(menuId)).collect(Collectors.toList());
        //获取需要删除的菜单权限
        List<Long> delMenuIds = currentMenuIds.stream()
                .filter(menuId -> !roleMenuDto.getMenuIds().contains(menuId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addMenuIds)) {
            List<SystemRoleMenuEntity> saveList = addMenuIds.stream()
                    .map(menu -> {
                        SystemRoleMenuEntity roleMenu = new SystemRoleMenuEntity();
                        roleMenu.setFkRoleId(roleMenuDto.getRoleId());
                        roleMenu.setFkMenuId(menu);
                        roleMenu.setPermission(1);
                        roleMenu.setGmtCreate(LocalDateTime.now());
                        roleMenu.setGmtModified(LocalDateTime.now());
                        return roleMenu;
                    }).collect(Collectors.toList());
            this.saveBatch(saveList);
        }
        if (CollectionUtils.isNotEmpty(delMenuIds)) {
            this.remove(new LambdaQueryWrapper<SystemRoleMenuEntity>()
                    .eq(SystemRoleMenuEntity::getFkRoleId, roleMenuDto.getRoleId())
                    .in(SystemRoleMenuEntity::getFkMenuId, delMenuIds));
        }

    }

    @Override
    public List<Long> getUserMenuIds(Long userId) {
        List<Long> menuIdList = Collections.emptyList();
        //获取用户角色
        List<SystemUserRoleEntity> userRoleEntities = userRoleMapper.selectList(new LambdaQueryWrapper<SystemUserRoleEntity>()
                .eq(SystemUserRoleEntity::getFkUserId, userId));
        if (CollectionUtils.isNotEmpty(userRoleEntities)) {
            List<Long> roleIds = userRoleEntities.stream().map(SystemUserRoleEntity::getFkRoleId).collect(Collectors.toList());
            menuIdList = systemRoleMenuMapper.selectList(new LambdaQueryWrapper<SystemRoleMenuEntity>()
                            .in(SystemRoleMenuEntity::getFkRoleId, roleIds))
                    .stream().map(SystemRoleMenuEntity::getFkMenuId).collect(Collectors.toList());
        }
        List<Long> userMenuIdList = userMenuMapper.selectList(new LambdaQueryWrapper<SystemUserMenuEntity>()
                        .eq(SystemUserMenuEntity::getFkUserId, userId))
                .stream().map(SystemUserMenuEntity::getFkMenuId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userMenuIdList)) {
            menuIdList.addAll(userMenuIdList);
        }
        return menuIdList;
    }
}
