
package com.insurance.controller;


import com.alibaba.fastjson.JSONObject;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.insurance.config.RedisService;
import com.insurance.constant.RedisConstant;
import com.insurance.entity.InsuranceOrder;
import com.insurance.enums.OrderStatusEnum;
import com.insurance.event.publisher.SendNotifyMessageEventPublisher;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.service.CreditCardService;
import com.insurance.service.InsuranceOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.Objects;

@RestController
@Slf4j
@Inner(false)
@RequestMapping("/feign")
@Tag(description = "远程管理", name = "远程管理")
public class FeignController {

    @Autowired
    private InsuranceOrderService orderService;
    @Autowired
    private CreditCardService creditCardService;
    @Autowired
    private SendNotifyMessageEventPublisher eventPublisher;
    @Autowired
    private InsuranceOrderMapper orderMapper;
    @Autowired
    private RedisService redisService;

    @Operation(summary = "发送下单请求")
    @Inner(false)
    @GetMapping("/order/sendRequest")
    public R sendRequest() {
        log.info("发送下单请求");
        orderService.sendRequest(null);
        return R.ok();
    }

    @Operation(summary = "还款提醒")
    @Inner(false)
    @GetMapping("/creditCard/sendRepaymentNotify")
    public R sendRepaymentNotify() {
        log.info("发送信用卡还款提醒==================>");
        creditCardService.sendRepaymentNotify(2);
        return R.ok();
    }

    @Operation(summary = "出账提醒")
    @Inner(false)
    @GetMapping("/creditCard/sendStatementNotify")
    public R sendStatementNotify() {
        log.info("发送信用卡出账提醒==================>");
        creditCardService.sendRepaymentNotify(1);
        return R.ok();
    }

    @Operation(summary = "额度提醒")
    @Inner(false)
    @GetMapping("/creditCard/sendQuotaRemindNotify")
    public R sendQuotaRemindNotify() {
        log.info("发送信用卡额度提醒==================>");
        creditCardService.sendQuotaRemindNotify();
        return R.ok();
    }

    @Operation(summary = "消息通知测试")
    @Inner(false)
    @GetMapping("/notifyTest")
    public R notifyTest(String orderNo, String code, Long creditCardId) {
        log.info("消息通知测试==========》");
        eventPublisher.publishPartnerUserOfflineEvent(creditCardId, code, orderNo, new Date());
        return R.ok();
    }

    @Operation(summary = "保存保险保单号-自动化下单兜底处理")
    @Inner(false)
    @PostMapping("/order/saveInsuranceNum")
    public R saveInsuranceNum(@RequestBody @Valid OrderMsg orderMsg) {
        log.info("自动化下单兜底处理===========》,保存订单号信息:{}", JSONObject.toJSONString(orderMsg));
        InsuranceOrder order = orderMapper.selectByOrderNum(orderMsg.getOrderNo());
        if (Objects.isNull(order) || order.getOrderStatus() > OrderStatusEnum.PROGRESSING.getCode()) {
            log.error("订单不存在或者订单状态不在处理范围,订单号:{},订单信息:{}", JSONObject.toJSONString(orderMsg),
                    Objects.nonNull(order) ? JSONObject.toJSONString(order) : "");
            return R.failed("订单不存在或者订单状态不在处理范围");
        }
        orderService.handleSuccessOrder(orderMsg);
        return R.ok();
    }

    @Operation(summary = "删除下单中缓存信息-自动化下单兜底处理")
    @Inner(false)
    @PostMapping("/order/removeProgressingKey")
    public R removeProgressingKey(@RequestBody @Valid OrderMsg orderMsg) {
        log.info("自动化下单兜底处理-删除下单中缓存信息===========》,请求订单信息:{}", JSONObject.toJSONString(orderMsg));
        redisService.zRemove(RedisConstant.ORDER_PROGRESSING_KEY, orderMsg.getOrderNo());
        return R.ok();
    }
}
