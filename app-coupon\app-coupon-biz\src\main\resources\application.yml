server:
  port: 6010

spring:
  servlet:
    multipart:
      # 单个文件的最大大小
      max-file-size: 5MB
  application:
    name: app-coupon-biz
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:************}:${NACOS_PORT:8852}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


annex-save-path: /work/coupon_center/
#annex-save-path: E:/work/project/coupon_center/hti-java-work/app-coupon/app-coupon-biz/src/main/resources/files/

#配置日志
logging:
  config: classpath:log/<EMAIL>@.xml