package com.insurance.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.insurance.dto.email.RecipientInfo;
import com.insurance.entity.MailTemplate;
import com.insurance.enums.EmailTemplateType;
import com.insurance.mapper.MailTemplateMapper;
import com.insurance.service.NotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/7/30
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class NotifyServiceImpl implements NotifyService {

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired
    private MailTemplateMapper mailTemplateMapper;

    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;

    @Override
    public void sendSmsNotify(List<RecipientInfo> phones) {
        log.info("==================>信用卡短信通知");
    }

    @Override
    public void sendEmailNotify(EmailTemplateType templateType, List<RecipientInfo> emails) {
        log.info("开始发送邮件，模板类型：{}，收件人数：{}", templateType.name(), emails.size());

        if (CollectionUtils.isEmpty(emails)) {
            log.warn("邮件发送失败：收件人为空");
            return;
        }

        MailTemplate mailTemplate = mailTemplateMapper.selectOne(new LambdaQueryWrapper<MailTemplate>().eq(MailTemplate::getTypeKey, templateType.getCode()), false);
        if (Objects.isNull(mailTemplate)) {
            throw new IllegalArgumentException("未找到邮件模板，code=" + templateType.getCode());
        }

        for (RecipientInfo to : emails) {
            // 校验是否包含所有必填变量
            for (String key : templateType.getRequiredPlaceholders()) {
                if (!to.getParams().containsKey(key)) {
                    throw new IllegalArgumentException("缺少模板参数：" + key);
                }
            }
            String subject = replaceTemplate(mailTemplate.getTitle(), to.getParams());
            String content = replaceTemplate(mailTemplate.getEmailTemplate(), to.getParams());
            sendSingleEmail(to.getContract(), subject, fromEmail, content);
        }
    }

    private void sendSingleEmail(String to, String subject, String from, String content) {
        try {
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setFrom(from);
            helper.setText(content, true);
            javaMailSender.send(message);
            log.info("邮件发送成功：收件人={}, 主题={}", to, subject);
        } catch (Exception e) {
            log.error("邮件发送失败：收件人={}, 错误={}", to, e.getMessage(), e);
        }
    }

    private String replaceTemplate(String template, Map<String, String> replacements) {
        if (StringUtils.isBlank(template)) return template;
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            template = template.replace("#{" + entry.getKey() + "}", entry.getValue());
        }
        return template;
    }


    //额度不足:通知人、卡尾号四位、限制金额、日期
    //支付失败:通知人、订单号、时间、卡尾号四位、日期
    //出账提醒：通知人、卡尾号四位、日期
    //还款提醒：通知人、卡尾号四位、日期


}
