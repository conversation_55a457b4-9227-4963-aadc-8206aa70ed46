package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.TeamDataSumDto;
import com.partner.dto.student.paramsmapper.StudentApportionParams;
import com.partner.entity.MPartnerUserEntity;
import com.partner.entity.MPayablePlanEntity;
import com.partner.vo.agent.PartnerUserAgentVo;
import com.partner.vo.my.MPayablePlanMyDetailVo;
import com.partner.vo.student.StudentApportionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_partner_user】的数据库操作Mapper
* @createDate 2024-12-20 14:13:45
* @Entity com.partner.entity.MPartnerUser
*/
@Mapper
public interface MPartnerUserMapper extends BaseMapper<MPartnerUserEntity> {

    /**
     * 查询分配人员列表
     * @param agentId
     * @return
     */
    List<StudentApportionVo> getApportionPartnerUser(@Param("agentId") Long agentId);

    int getTeamPeopleCount(TeamDataSumDto params);



    BigDecimal getLevelAmount(TeamDataSumDto params);

    List<MPayablePlanMyDetailVo> getSelfAmount(TeamDataSumDto params);

    BigDecimal getAllAmount(TeamDataSumDto params);

    List<MPayablePlanMyDetailVo> getAgentMPayablePlan(TeamDataSumDto params);

    /**
     * 根据系统用户ID查询伙伴用户列表
     * @param userId
     * @return
     */
    List<PartnerUserAgentVo> selectPartnerUserAgentList(@Param("userId") Long userId);

}




