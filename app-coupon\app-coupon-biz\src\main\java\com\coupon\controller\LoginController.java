package com.coupon.controller;

import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.coupon.dto.EnrollUserDto;
import com.coupon.service.impl.LoginServiceImpl;
import com.coupon.service.impl.MobileServiceImpl;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/login")
@Inner(value = false)
public class LoginController {
    @Resource
    private LoginServiceImpl loginService;

    @Resource
    private MobileServiceImpl mobileService;

    @PostMapping("/enrollUser")
    public R enrollUser(@RequestBody EnrollUserDto enrollUserDto) throws Exception {
        return loginService.enrollUser(enrollUserDto);
    }

//    @PostMapping("/login")
//    public R login(@RequestBody EnrollUserDto enrollUserDto) throws Exception {
//        return loginService.login(enrollUserDto);
//    }
//
//    @PostMapping("/sendSmsCode")
//    public R sendSmsCode(String mobile) throws Exception {
//        return mobileService.sendSmsCode(mobile);
//    }

}
