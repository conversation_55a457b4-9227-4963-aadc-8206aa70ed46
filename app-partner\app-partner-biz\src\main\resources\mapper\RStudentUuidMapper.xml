<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RStudentUuidMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.RStudentUuidEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkStudentId" column="fk_student_id" jdbcType="BIGINT"/>
            <result property="fkStudentUuid" column="fk_student_uuid" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_student_id,fk_student_uuid,
        gmt_create,gmt_create_user,gmt_modified,
        gmt_modified_user
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.partner.entity.RStudentUuidEntity" useGeneratedKeys="true">
        insert into ais_sale_center.r_student_uuid
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fkStudentId != null">fk_student_id,</if>
            <if test="fkStudentUuid != null">fk_student_uuid,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtCreateUser != null">gmt_create_user,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="gmtModifiedUser != null">gmt_modified_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="fkStudentId != null">#{fkStudentId,jdbcType=BIGINT},</if>
            <if test="fkStudentUuid != null">#{fkStudentUuid,jdbcType=VARCHAR},</if>
            <if test="gmtCreate != null">#{gmtCreate,jdbcType=TIMESTAMP},</if>
            <if test="gmtCreateUser != null">#{gmtCreateUser,jdbcType=VARCHAR},</if>
            <if test="gmtModified != null">#{gmtModified,jdbcType=TIMESTAMP},</if>
            <if test="gmtModifiedUser != null">#{gmtModifiedUser,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <select id="selectByUUID"  resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ais_sale_center.r_student_uuid
        WHERE  fk_student_uuid = #{UUID}
    </select>
    <select id="selecteBatchByUUIDs" resultType="com.partner.entity.RStudentUuidEntity">
        SELECT
        <include refid="Base_Column_List" />
        FROM ais_sale_center.r_student_uuid
        WHERE  fk_student_uuid IN
        <foreach collection="UUIDs" item="UUID" index="index" open="(" separator="," close=")">
            #{UUID}
        </foreach>

    </select>

</mapper>
