package com.partner.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-10 11:15:20
 */

@Data
@TableName("r_payable_plan_settlement_installment")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_payable_plan_settlement_installment ")
public class RPayablePlanSettlementInstallmentEntity extends Model<RPayablePlanSettlementInstallmentEntity>{

  @Schema(description = "应付计划结算分期表Id")
  private Long id;
 

  @Schema(description = "财务结算汇总批次号")
  private String numSettlementBatch;
 
/*
  @Schema(description = "操作GUID")
  private String optGuid;*/
 

  @Schema(description = "应付计划Id")
  private Long fkPayablePlanId;
 

  @Schema(description = "收款单子项Id（比对这个收款记录id，如果存在，不需要再创建）")
  private Long fkReceiptFormItemId;
 

  @Schema(description = "付款单子项Id")
  private Long fkPaymentFormItemId;
 

  @Schema(description = "发票和应收计划关系Id（预付时记录发票和应收计划关系Id）")
  private Long fkInvoiceReceivablePlanId;
 

  @Schema(description = "发票Id（预付时记录和发票关系）")
  private Long fkInvoiceId;
 

  @Schema(description = "预计支付金额")
  private BigDecimal amountExpect;
 

  @Schema(description = "预计手续费金额")
  private BigDecimal serviceFeeExpect;
 

  @Schema(description = "实际支付金额（初始）")
  private BigDecimal amountActualInit;
 

  @Schema(description = "实际手续费金额（初始）")
  private BigDecimal serviceFeeActualInit;
 

  @Schema(description = "实际支付金额（合并）")
  private BigDecimal amountActual;
 

  @Schema(description = "实际手续费金额（合并）")
  private BigDecimal serviceFeeActual;
 

  @Schema(description = "帐号导出时间")
  private LocalDateTime accountExportTime;
 

  @Schema(description = "是否回滚，0否/1是")
  private Boolean isRollBack;
 

  @Schema(description = "回滚时间")
  private LocalDateTime rollBackTime;
 

  @Schema(description = "结算代理Id（第四提交到第五步时记录，结算代理id快照）")
  private Long fkAgentIdSettlement;
 

  @Schema(description = "学生代理合同账户Id")
  private Long fkAgentContractAccountId;
 

  @Schema(description = "币种编号（代理账户）")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "状态：0=未处理/1=处理中/2=完成")
  private Integer status;
 

  @Schema(description = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
  private Integer statusSettlement;
 

  @Schema(description = "预审状态：提差异-1/未审核0/已审核1")
  private Integer statusReview;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
