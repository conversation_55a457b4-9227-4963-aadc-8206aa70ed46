package com.fzh.job.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;

@Data
@Schema(description = "JOB基础参数")
public class BaseParamDto {

    @Schema(description = "代理ID")
    private Long agentId;

    @Schema(description = "上个月第一天日期")
    LocalDate firstDayOfLastMonth;

    @Schema(description = "当月第一天日期")
    LocalDate firstDayOfToMonth ;



    private String baseCode;


}
