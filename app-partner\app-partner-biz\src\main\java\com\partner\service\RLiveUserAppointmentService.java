package com.partner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.RLiveUserAppointmentDto;
import com.partner.entity.RLivePartnerUserAppointmentEntity;
import com.partner.vo.AppointmentVo;

import java.util.List;

public interface RLiveUserAppointmentService extends IService<RLivePartnerUserAppointmentEntity> {
    /**
     * 直播预约
     * @param dto
     * @return
     */
    boolean userAppointment(RLiveUserAppointmentDto dto);

    /**
     * 取消直播预约
     * @param dto
     * @return
     */
    boolean cancelAppointment(RLiveUserAppointmentDto dto);


    /**
     * 直播预约 列表
     * @param liveId
     * @return
     */
    List<AppointmentVo> getAppointmentList(Long liveId);
}
