<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MAppStudentOfferItemMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MAppStudentOfferItemEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkAppStudentId" column="fk_app_student_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionId" column="fk_institution_id" jdbcType="BIGINT"/>
            <result property="fkInstitutionCourseId" column="fk_institution_course_id" jdbcType="BIGINT"/>
            <result property="openingTime" column="opening_time" jdbcType="DATE"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
    <update id="updateInfoById">
        UPDATE ais_sale_center.m_app_student_offer_item
        SET fk_area_country_id=#{fkAreaCountryId},fk_institution_id=#{fkInstitutionId},
            fk_institution_course_id=#{fkInstitutionCourseId},opening_time=#{openingTime}
        where  id=#{id}
    </update>


</mapper>
