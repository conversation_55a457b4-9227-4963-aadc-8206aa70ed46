<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.UExchangeRateMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.UExchangeRateEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNumFrom" column="fk_currency_type_num_from" jdbcType="VARCHAR"/>
            <result property="fkCurrencyTypeNumTo" column="fk_currency_type_num_to" jdbcType="VARCHAR"/>
            <result property="getDate" column="get_date" jdbcType="DATE"/>
            <result property="exchangeRate" column="exchange_rate" jdbcType="DECIMAL"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_currency_type_num_from,fk_currency_type_num_to,
        get_date,exchange_rate,gmt_create,
        gmt_create_user,gmt_modified,gmt_modified_user
    </sql>
</mapper>
