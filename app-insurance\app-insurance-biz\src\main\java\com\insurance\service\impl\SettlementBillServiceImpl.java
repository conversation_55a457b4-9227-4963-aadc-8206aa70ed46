package com.insurance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.util.SecurityUtils;
import com.insurance.dto.file.AppFileCenter;
import com.insurance.dto.settlement.SubmitSettlementDto;
import com.insurance.entity.*;
import com.insurance.enums.SettlementStatusEnum;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.mapper.*;
import com.insurance.service.MediaAndAttachedService;
import com.insurance.service.SettlementBillItemService;
import com.insurance.service.SettlementBillService;
import com.insurance.util.ExchangeRateUtils;
import com.insurance.util.OrderNoUtil;
import com.insurance.util.PartnerUserUtils;
import com.insurance.util.PdfExportUtils;
import com.insurance.vo.settlement.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class SettlementBillServiceImpl extends ServiceImpl<SettlementBillMapper, SettlementBill> implements SettlementBillService {


    @Autowired
    private SaleCenterMapper saleCenterMapper;
    @Autowired
    private InsuranceOrderSettlementMapper settlementMapper;
    @Autowired
    private InsuranceOrderMapper orderMapper;
    @Autowired
    private SettlementBillMapper billMapper;
    @Autowired
    private SettlementBillItemService billItemService;
    @Autowired
    private SettlementBillSignatureMapper billSignatureMapper;
    @Autowired
    private SettlementBillItemMapper billItemMapper;
    @Autowired
    private ExchangeRateUtils exchangeRateUtils;
    @Autowired
    private MediaAndAttachedService attachedService;
    @Autowired
    private AppFileCenterMapper appFileCenterMapper;
    @Autowired
    private PdfExportUtils pdfExportUtils;

    @Override
    public Map<String, List<AgentAccountVo>> getAgentAccountList() {
        List<AgentAccountVo> agentAccountList = saleCenterMapper.getAgentAccountList(PartnerUserUtils.getCurrentAgentId());
        return agentAccountList.stream()
                .collect(Collectors.groupingBy(
                        AgentAccountVo::getCurrencyTypeNum,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(AgentAccountVo::getIsDefault, Comparator.nullsLast(Comparator.reverseOrder())))
                                        .collect(Collectors.toList())
                        )
                ));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitSettlement(SubmitSettlementDto submitSettlementDto) {
        if (CollectionUtils.isEmpty(submitSettlementDto.getOrderSettlementIds())) {
            throw new InsuranceGlobalException("请选择要提交的订单!");
        }
        //有待结算的不能提交
        checkSubmitSettlement(PartnerUserUtils.getCurrentAgentId());

        //1：生成结算账单明细项数据
        List<InsuranceOrderSettlement> settlementList = settlementMapper.selectList(new LambdaQueryWrapper<InsuranceOrderSettlement>()
                .in(InsuranceOrderSettlement::getId, submitSettlementDto.getOrderSettlementIds()));
        List<Long> orderIdList = settlementList.stream().map(InsuranceOrderSettlement::getFkInsuranceOrderId).collect(Collectors.toList());
        Map<Long, InsuranceOrder> orderMap = orderMapper
                .selectList(new LambdaQueryWrapper<InsuranceOrder>()
                        .in(InsuranceOrder::getId, CollectionUtils.isEmpty(orderIdList) ? Arrays.asList(0L) : orderIdList))
                .stream()
                .collect(Collectors.toMap(
                        InsuranceOrder::getId,
                        Function.identity(),
                        (o1, o2) -> o1.getGmtCreate().after(o2.getGmtCreate()) ? o1 : o2));

        //查询应付计划
        List<Long> payablePlanIds = settlementList.stream().map(InsuranceOrderSettlement::getFkPayablePlanId).collect(Collectors.toList());
        Map<Long, PayablePlanVo> planMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(payablePlanIds)) {
            planMap = settlementMapper.getPayablePlanList(payablePlanIds)
                    .stream()
                    .collect(Collectors.toMap(
                            PayablePlanVo::getPayablePlanId,
                            Function.identity()));
        }
        //创建结算单明细
        Map<Long, PayablePlanVo> finalPlanMap = planMap;
        List<SettlementBillItem> billItemList = settlementList.stream().map(settlement -> {
            InsuranceOrder insuranceOrder = orderMap.getOrDefault(settlement.getFkInsuranceOrderId(), new InsuranceOrder());
            SettlementBillItem billItem = SettlementBillItem.builder()
                    .fkInsuranceOrderSettlementId(settlement.getId())
                    .fkCurrencyTypeNumActual(insuranceOrder.getFkCurrencyTypeNum())
                    //默认兑换支付金额和实际支付金额一样
                    .amountActual(Objects.nonNull(insuranceOrder.getInsuranceAmount()) ? insuranceOrder.getInsuranceAmount() : BigDecimal.ZERO)
                    //手续费默认为0
                    .serviceFeeActual(BigDecimal.ZERO)
                    .serviceFeeExchange(BigDecimal.ZERO)
                    .fkCurrencyTypeNumExchange(submitSettlementDto.getCurrencyTypeNum())
                    .exchangeRate(BigDecimal.ONE)
                    .build();
            //计算兑换支付金额-amountExchange-兑换支付金额=实际支付金额*结算比例
            BigDecimal commissionRate = finalPlanMap.getOrDefault(settlement.getFkPayablePlanId(), new PayablePlanVo()).getCommissionRate();
            if (Objects.isNull(commissionRate)) {
                //默认15%
                commissionRate = new BigDecimal("15");
            }
            BigDecimal settlementAmount = billItem.getAmountActual().multiply(commissionRate.divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
            billItem.setAmountExchange(settlementAmount);
            // 币种不一样根据汇率计算金额
            if (!insuranceOrder.getFkCurrencyTypeNum().equals(submitSettlementDto.getCurrencyTypeNum())) {
                RateDetail rateDetail = exchangeRateUtils.getRateDetail(insuranceOrder.getFkCurrencyTypeNum(), submitSettlementDto.getCurrencyTypeNum());
                if (Objects.isNull(rateDetail) || Objects.isNull(rateDetail.getRate())) {
                    log.error("获取汇率异常,汇率结果为空:{}", JSONObject.toJSONString(rateDetail));
                    throw new InsuranceGlobalException("获取汇率失败");
                }
                BigDecimal amountExchange = billItem.getAmountActual().multiply(commissionRate.divide(new BigDecimal("100"))
                        .setScale(2, RoundingMode.HALF_UP)).multiply(rateDetail.getRate());
                billItem.setAmountExchange(amountExchange);
                billItem.setExchangeRate(rateDetail.getRate());
            }
            return billItem;
        }).collect(Collectors.toList());

        //计算对账单总额
        BigDecimal totalAmount = billItemList.stream()
                .map(SettlementBillItem::getAmountExchange)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //2：结算账单数据落库
        SettlementBill settlementBill = SettlementBill.builder()
                .fkAgentId(PartnerUserUtils.getCurrentAgentId())
                .fkAgentContractAccountId(submitSettlementDto.getAgentAccountId())
                .fkCurrencyTypeNum(submitSettlementDto.getCurrencyTypeNum())
                .amount(totalAmount)
                .build();
        settlementBill.setGmtCreate(new Date());
        settlementBill.setGmtModified(new Date());
        settlementBill.setGmtCreateUser(SecurityUtils.getUser().getLoginId());
        settlementBill.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
        billMapper.insert(settlementBill);
        Long billId = settlementBill.getId();

        //3：结算账单明细数据落库
        billItemList.stream().forEach(item -> {
            item.setFkSettlementBillId(billId);
            item.setGmtCreate(new Date());
            item.setGmtModified(new Date());
            item.setGmtCreateUser(SecurityUtils.getUser().getLoginId());
            item.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
        });
        billItemService.saveBatch(billItemList);

        //4：结算账单签名数据落库
        SettlementBillSignature billSignature = SettlementBillSignature.builder()
                .fkSettlementBillId(billId)
                .signature(submitSettlementDto.getSignature())
                .build();
        billSignature.setGmtCreate(new Date());
        billSignature.setGmtModified(new Date());
        billSignature.setGmtCreateUser(SecurityUtils.getUser().getLoginId());
        billSignature.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
        billSignatureMapper.insert(billSignature);

        //5:修改订单结算表状态和生成对应的批次号
        String batchNo = OrderNoUtil.generateSettlementBatchNo();
        settlementMapper.update(new LambdaUpdateWrapper<InsuranceOrderSettlement>()
                .set(InsuranceOrderSettlement::getStatusSettlement, SettlementStatusEnum.AGENT_CONFIRMED.getCode())
                .set(InsuranceOrderSettlement::getFkNumOptBatch, batchNo)
                .set(InsuranceOrderSettlement::getGmtModified, new Date())
                .set(InsuranceOrderSettlement::getGmtModifiedUser, SecurityUtils.getUser().getLoginId())
                .in(InsuranceOrderSettlement::getId, submitSettlementDto.getOrderSettlementIds()));
    }

    @Override
    public SettlementBillDetailVo getSettlementBillDetail(Long id) {
        SettlementBill settlementBill = billMapper.selectById(id);
        if (Objects.isNull(settlementBill)) {
            throw new InsuranceGlobalException("结算账单不存在!");
        }

        //结算账单明细
        List<SettlementBillItemVo> billItems = billItemMapper.selectSettlementBillItemListByBillId(id);
        List<Long> payablePlanIds = billItems.stream().map(SettlementBillItemVo::getPayablePlanId).collect(Collectors.toList());
        Map<Long, PayablePlanVo> planMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(payablePlanIds)) {
            planMap = settlementMapper.getPayablePlanList(payablePlanIds)
                    .stream()
                    .collect(Collectors.toMap(
                            PayablePlanVo::getPayablePlanId,
                            Function.identity()));
        }
        //填充结算比例和结算金额
        Map<Long, PayablePlanVo> finalPlanMap = planMap;
        billItems.stream().forEach(item -> {
            item.setSettlementAmount(BigDecimal.ZERO);
            BigDecimal commissionRate = finalPlanMap.getOrDefault(item.getPayablePlanId(), new PayablePlanVo()).getCommissionRate();
            if (Objects.isNull(commissionRate)) {
                //默认15%
                commissionRate = new BigDecimal("15");
            }
            item.setCommissionRate(commissionRate.setScale(2, RoundingMode.HALF_UP));
            BigDecimal settlementAmount = item.getInsuranceAmount().multiply(commissionRate.divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
            item.setSettlementAmount(settlementAmount);
            item.setCommissionRateStr(commissionRate.setScale(2, RoundingMode.HALF_UP) + "%");
        });
        //订单币种结算总额
        BigDecimal originSettlementAmount = billItems.stream()
                .map(SettlementBillItemVo::getSettlementAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(4, RoundingMode.HALF_UP);

        //代理账户
        AgentAccountVo agentAccount = saleCenterMapper.getAgentAccount(settlementBill.getFkAgentContractAccountId());

        //签名
        SettlementBillSignature billSignature = billSignatureMapper.selectOne(new LambdaQueryWrapper<SettlementBillSignature>().eq(SettlementBillSignature::getFkSettlementBillId, id));

        SettlementBillDetailVo detail = SettlementBillDetailVo.builder()
                .settlementBillId(id)
                .settlementCurrencyTypeNum(settlementBill.getFkCurrencyTypeNum())
                .totalSettlementAmount(originSettlementAmount)
                .rate(null)
                .originTotalSettlementAmount(originSettlementAmount)
                .orderCurrencyTypeNum(CollectionUtils.isNotEmpty(billItems) ? billItems.get(0).getFkCurrencyTypeNum() : null)
                .signature(Objects.nonNull(billSignature) ? billSignature.getSignature() : null)
                .billItems(billItems)
                .agentAccount(agentAccount)
                .build();
        //结算币种不一样-获取汇率
        if (Objects.nonNull(detail.getSettlementCurrencyTypeNum()) &&
                !detail.getOrderCurrencyTypeNum().equals(agentAccount.getCurrencyTypeNum())) {
            RateDetail rateDetail = exchangeRateUtils.getRateDetail(detail.getOrderCurrencyTypeNum(), detail.getSettlementCurrencyTypeNum());
            if (Objects.isNull(rateDetail) || Objects.isNull(rateDetail.getRate())) {
                log.error("获取汇率异常,汇率结果为空:{}", JSONObject.toJSONString(rateDetail));
                throw new InsuranceGlobalException("获取汇率失败");
            }
            BigDecimal totalSettlementAmount = detail.getOriginTotalSettlementAmount().multiply(rateDetail.getRate()).setScale(4, RoundingMode.HALF_UP);
            detail.setTotalSettlementAmount(totalSettlementAmount);
            detail.setRate(rateDetail.getRate());
        }
        return detail;

    }

    @Override
    public List<SettlementBill> getSettlementBillList() {
        return billMapper.selectList(new LambdaQueryWrapper<SettlementBill>()
                .eq(SettlementBill::getFkAgentId, PartnerUserUtils.getCurrentAgentId())
                .orderByDesc(SettlementBill::getGmtCreate));
    }

    @Override
    public List<HistoricalBillVo> getHistoricalBillList(Date startDate, Date endDate) {
        List<HistoricalBillVo> billList = billMapper.selectList(new LambdaQueryWrapper<SettlementBill>()
                        .eq(SettlementBill::getFkAgentId, PartnerUserUtils.getCurrentAgentId())
                        .ge(Objects.nonNull(startDate), SettlementBill::getGmtCreate, startDate)
                        .le(Objects.nonNull(endDate), SettlementBill::getGmtCreate, endDate)
                        .orderByDesc(SettlementBill::getGmtCreate))
                .stream()
                .map(bill -> HistoricalBillVo.builder()
                        .id(bill.getId())
                        .totalSettlementAmount(bill.getAmount())
                        .currencyTypeNum(bill.getFkCurrencyTypeNum())
                        .gmtCreate(bill.getGmtCreate())
                        .build())
                .collect(Collectors.toList());
        //填充订单数和审批状态-审核状态:2待审核;3-结算中;4-已完成
        billList.stream().forEach(bill -> {
            List<SettlementBillItem> billItemList = billItemMapper.selectHistoricalSettlementItemListByBillId(bill.getId());
            bill.setOrderCount(billItemList.size());
            if (CollectionUtils.isNotEmpty(billItemList)) {
                bill.setApprovalStatus(billItemList.get(0).getApprovalStatus());
            }
        });
        return billList;
    }

    @Override
    @SneakyThrows
    public void downloadSettlementBill(Long id, HttpServletResponse response) {
        //如果已经上传了,直接从腾讯云下载
        List<MediaAndAttached> attachedList = attachedService.getBaseMapper().selectList(new LambdaQueryWrapper<MediaAndAttached>()
                .eq(MediaAndAttached::getFkTableName, "m_settlement_bill")
                .eq(MediaAndAttached::getFkTableId, id)
                .orderByDesc(MediaAndAttached::getGmtCreate));
        if (CollectionUtils.isNotEmpty(attachedList)) {
            MediaAndAttached mediaAndAttached = attachedList.get(0);
            AppFileCenter appFileCenter = appFileCenterMapper.selectAppFileByGuid(mediaAndAttached.getFkFileGuid());
            if (Objects.nonNull(appFileCenter)) {
                attachedService.downloadFile(appFileCenter, response);
                return;
            }
        }
        //没有上传或者找不到文件,重新生成并且上传和下载
        SettlementBillDetailVo billDetail = getSettlementBillDetail(id);
        pdfExportUtils.downloadPdf(billDetail, response, true);
    }

    /**
     * 提交结算前置校验
     * 如果当前代理有待结算的佣金订单,不能提交
     *
     * @param agentId
     */
    private void checkSubmitSettlement(Long agentId) {
        Integer count = settlementMapper.selectSettlementProgressOrder(agentId);
        if (count > 0) {
            throw new InsuranceGlobalException("当前有结算中的佣金订单,请在佣金订单结算完成后再提交!");
        }
    }
}
