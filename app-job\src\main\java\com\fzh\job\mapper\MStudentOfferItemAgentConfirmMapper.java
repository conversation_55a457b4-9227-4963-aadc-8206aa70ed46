package com.fzh.job.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fzh.job.dto.BaseParamDto;
import com.partner.entity.MStudentOfferItemAgentConfirmEntity;
import com.partner.vo.job.SystemUserJobVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student_offer_item_agent_confirm】的数据库操作Mapper
* @createDate 2025-02-20 09:52:38
* @Entity com.get.aispartnercenter.entity.MStudentOfferItemAgentConfirm
*/
@Mapper
public interface MStudentOfferItemAgentConfirmMapper extends BaseMapper<MStudentOfferItemAgentConfirmEntity> {

    public void insertAffirmOfferItem(BaseParamDto params);

    public List<Long> getAllAgentId();

    public List<SystemUserJobVo> getAllAgentIdSendMessage();



}




