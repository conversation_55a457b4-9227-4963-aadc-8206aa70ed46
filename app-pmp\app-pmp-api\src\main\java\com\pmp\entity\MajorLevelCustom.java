package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("u_major_level_custom")
public class MajorLevelCustom extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "自定义名称")
    private String customName;

    @Schema(description = "自定义名称（中文）")
    private String customNameChn;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @Schema(description = "是否通用等级：0否/1是")
    private Boolean isGeneral;

    @Schema(description = "是否激活：0否/1是")
    private Integer isActive;

    @Schema(description = "父用户自定义课程等级Id")
    private Long fkMajorLevelCustomIdParent;
}
