<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pmp.mapper.AgentCommissionPlanMapper">


    <select id="selectPlanIds" resultType="java.lang.Long">
        SELECT p.id
        FROM m_agent_commission_plan p
        WHERE p.approval_status = 2 and p.is_active = 1 and STR_TO_DATE(#{startDate}, '%Y-%m-%d') &gt;=
        DATE(p.start_time)
        AND (
        p.is_timeless = 1
        OR
        (p.is_timeless = 0
        AND STR_TO_DATE(#{startDate}
        , '%Y-%m-%d') &lt;= DATE(p.end_time))
        )
        AND p.fk_institution_provider_id in
        <foreach item="item" collection="providerIds" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND p.id IN (SELECT fk_agent_commission_plan_id
        FROM r_agent_commission_plan_company
        WHERE fk_company_id = #{companyId})
        <if test="type == 2">
            AND p.fk_agent_commission_type_id IN (SELECT fk_agent_commission_type_id
                                                          FROM r_agent_commission_type_agent
                                                          WHERE fk_agent_id =
            #{agentId}
            )
        </if>
        <if test="type == 1">
            and
            (p.fk_agent_commission_type_id is null or p.fk_agent_commission_type_id &lt; 1)
        </if>
    </select>

    <select id="getTerritoryInfoVo" resultType="com.pmp.vo.commission.TerritoryInfoVo">
        select territory, territory_chn, course, course_chn
        from m_institution_provider_commission_plan
        where id = #{planId}
    </select>


    <select id="getCurrentUserProviderIds" resultType="java.lang.Long">
        SELECT distinct (fk_institution_provider_id)
        FROM ais_institution_center.r_institution_provider_institution rpi
        INNER JOIN ais_institution_center.m_institution i ON rpi.fk_institution_id = i.id
        WHERE
        rpi.fk_institution_provider_id IN (
        <foreach collection="providerIds" item="providerId" separator=",">
            #{providerId}
        </foreach>
        )
        AND rpi.is_active = 1
        AND i.is_active = 1
        AND FIND_IN_SET(#{companyId}, rpi.fk_company_ids)
    </select>
</mapper>

        <!--        AND (-->
        <!--        (-->
        <!--        p.is_gobal = 0-->
        <!--        AND p.fk_agent_commission_type_id IN (SELECT fk_agent_commission_type_id-->
        <!--        FROM r_agent_commission_type_agent-->
        <!--        WHERE fk_agent_id = #{agentId})-->
        <!--        )-->
        <!--        OR-->
        <!--        (-->
        <!--        p.is_gobal = 1-->
        <!--        AND NOT EXISTS (SELECT 1-->
        <!--        FROM r_agent_commission_type_agent-->
        <!--        WHERE fk_agent_id = #{agentId})-->
        <!--        )-->
        <!--        )-->