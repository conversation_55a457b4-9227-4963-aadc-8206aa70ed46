package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.util.HttpUtils;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.ContractDto;
import com.partner.dto.MAgentContractDto;
import com.partner.dto.ais.CreateContractPdfDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.*;
import com.partner.enums.AgentContractApprovalStatusEnum;
import com.partner.enums.AgentNatureEnum;
import com.partner.enums.FileUploadEnum;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.*;
import com.partner.service.FileService;
import com.partner.service.MAgentContractService;
import com.partner.service.PartnerFileService;
import com.partner.util.SecureEncryptUtil;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.contract.AgentContractDetailVo;
import com.partner.vo.contract.AgentContractInfoVo;
import com.partner.vo.contract.AgentContractVo;
import com.partner.vo.file.UploadFileParam;
import com.partner.vo.file.UploadFileVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【m_agent_contract】的数据库操作Service实现
 * @createDate 2025-01-08 11:21:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MAgentContractServiceImpl extends ServiceImpl<MAgentContractMapper, MAgentContractEntity>
        implements MAgentContractService {


    private final MAgentContractMapper mAgentContractMapper;

    private final FileService fileService;

    private final SMediaAndAttachedMapper sMediaAndAttachedMapper;

    private final MFilePartnerMapper filePartnerMapper;

    private final RAgentContractSignatureMapper rAgentContractSignatureMapper;

    private final MAgentMapper agentMapper;
    private final PartnerFileService partnerFileService;
    @Resource
    private HttpUtils httpUtils;

    @Override
    public Page<AgentContractInfoVo> getContractPage(Page page, MAgentContractDto params) {
//        List<AgentContractVo> contractvolist = new ArrayList<>();
//        FzhUser fzhUser = SecurityUtils.getUser();
//        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),
//                fzhUser.getFkFromPlatformCode(), fzhUser.getId());
//
//        params.setFkAgentId(userinfo.getAgentId());
//
//        // 查询合同
//        IPage<AgentContractVo> ipageData = mAgentContractMapper.getContractPage(page, params);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        // 当前时间
        LocalDateTime now = LocalDateTime.now();
        params.setFkAgentId(UserInfoParamsUtils.getCurrentAgentId());
        IPage<AgentContractInfoVo> iPage = mAgentContractMapper.selectContractPage(page, params);
        iPage.getRecords().stream().forEach(contract -> {
            contract.setContractStatus(getContractStatus(contract));
        });
        page.setRecords(iPage.getRecords());
        page.setTotal(iPage.getTotal());
        page.setSize(iPage.getSize());
        page.setCurrent(iPage.getCurrent());
        page.setPages(iPage.getPages());
        return page;
    }

    @Override
    public AgentContractVo getContract(MAgentContractDto params) {

        AgentContractVo agentContractVo = new AgentContractVo();

        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),
                fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        if (ObjectUtil.isEmpty(userinfo)) {
            // UUID数据不存在!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,
                    PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":代理不存在!");
        }

        if (ObjectUtil.isEmpty(params.getId())) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,
                    PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":合同ID不能为空!");
        }

        params.setFkAgentId(userinfo.getAgentId());

        agentContractVo = mAgentContractMapper.selectByAgentId(params);

        agentContractVo.setContractType(0);

        SMediaAndAttachedEntity sMediaParams = new SMediaAndAttachedEntity();
        sMediaParams.setFkTableId(params.getId());
        sMediaParams.setTypeKey("sale_contract_file");
        sMediaParams.setFkTableName("m_agent_contract");
        SMediaAndAttachedEntity attacheEntity = sMediaAndAttachedMapper.selectSaleOne(sMediaParams);// 查询合同附件
        if (ObjectUtil.isNotEmpty(attacheEntity)) {
            MFilePartnerEntity filePartner = filePartnerMapper.selectSaleFileOne(attacheEntity);
            agentContractVo.setFile(filePartner);
            agentContractVo.setContractType(1);

        }

        SMediaAndAttachedEntity sMediaSealParams = new SMediaAndAttachedEntity();
        sMediaSealParams.setFkTableId(params.getId());
        sMediaSealParams.setTypeKey("sale_contract_seal_file");
        sMediaSealParams.setFkTableName("m_agent_contract");
        SMediaAndAttachedEntity attacheSealEntity = sMediaAndAttachedMapper.selectSaleOne(sMediaSealParams);// 查询公司合同附件
        if (ObjectUtil.isNotEmpty(attacheSealEntity)) {
            MFilePartnerEntity fileSeal = filePartnerMapper.selectSaleFileOne(attacheSealEntity);
            agentContractVo.setSealFile(fileSeal);
            agentContractVo.setContractType(2);
        }
        RAgentContractSignatureEntity singeType = rAgentContractSignatureMapper
                .selectOne(new LambdaQueryWrapper<RAgentContractSignatureEntity>()
                        .eq(RAgentContractSignatureEntity::getFkAgentContractId, agentContractVo.getId()));
        if (ObjectUtil.isNotEmpty(singeType)) {
            agentContractVo.setSingeType(2);
        } else {
            agentContractVo.setSingeType(1);
        }

        Long fkAgentId = agentContractVo.getFkAgentId();
        if (ObjectUtil.isNotNull(fkAgentId)) {
            MAgentEntity mAgentEntity = this.agentMapper.selectById(fkAgentId);
            if (ObjectUtil.isNotNull(mAgentEntity)) {
                agentContractVo.setName(mAgentEntity.getName());
            }
        }

        return agentContractVo;
    }


    @Override
    public AgentContractDetailVo getContractDetail(Long contractId) {
        AgentContractDetailVo detail = mAgentContractMapper.selectContractDetail(contractId);
        if (Objects.nonNull(detail)) {
            //合同状态
            detail.setContractStatus(getContractStatus(detail));
            //是否已上传盖章合同-仅针对于公司性质的合同
            if (detail.getNature().toString().equals(AgentNatureEnum.COMPANY.getCode())) {
                SMediaAndAttachedEntity mediaAndAttached = SMediaAndAttachedEntity.builder()
                        .fkTableId(contractId)
                        .typeKey("sale_contract_seal_file")
                        .fkTableName("m_agent_contract")
                        .build();
                SMediaAndAttachedEntity attacheSealEntity = sMediaAndAttachedMapper.selectSaleOne(mediaAndAttached);
                if (ObjectUtil.isNotEmpty(attacheSealEntity)) {
                    MFilePartnerEntity fileSeal = filePartnerMapper.selectSaleFileOne(attacheSealEntity);
                    detail.setSealFileInfo(fileSeal);
                    detail.setIsUploadStampContract(Objects.isNull(fileSeal) ? 0 : 1);
                }
            }
            //是否已上传合同签名:0否1是
            RAgentContractSignatureEntity contractSignature = rAgentContractSignatureMapper.selectOne(new LambdaQueryWrapper<RAgentContractSignatureEntity>()
                    .eq(RAgentContractSignatureEntity::getFkAgentContractId, contractId), false);
            detail.setIsUploadSignature(Objects.isNull(contractSignature) ? 0 : 1);

            //如果合同审核失败 可以重新上传签名和盖章合同
            if (detail.getContractStatus().equals(AgentContractApprovalStatusEnum.REJECTED.getCode())) {
                detail.setIsUploadStampContract(0);
                detail.setIsUploadSignature(0);
            }
        }
        return detail;
    }

    @Override
    public Integer getLatestAgentContractStatus() {
        AgentContractInfoVo latestAgentContract = mAgentContractMapper.getLatestAgentContractStatus(UserInfoParamsUtils.getCurrentAgentId());
        if (Objects.isNull(latestAgentContract)) {
            return AgentContractApprovalStatusEnum.NO_CONTRACT.getCode();
        }
        return getContractStatus(latestAgentContract);
    }

    @Override
    @SneakyThrows
    public CreateContractPdfDto getEncryptCreateContractPdfParam(Long contractId, Long agentId, Integer contractVsion, Integer contractTemplateMode) {
        String secretKey = SecureEncryptUtil.getCardSecretKey();
        String encryptContractId = SecureEncryptUtil.encrypt(contractId.toString(), secretKey);
        log.info("encryptContractId:{}", encryptContractId);
        String encryptAgentId = SecureEncryptUtil.encrypt(agentId.toString(), secretKey);
        log.info("encryptAgentId:{}", encryptAgentId);
        return CreateContractPdfDto.builder()
                .secret(secretKey)
                .encryptContractId(encryptContractId)
                .encryptAgentId(encryptAgentId)
                .contractVsion(contractVsion)
                .contractTemplateMode(contractTemplateMode)
                .build();
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void uploadContract(MultipartFile file, Long contractId,String customerFileName) {
        MAgentContractEntity contractEntity = this.getById(contractId);
        if (ObjectUtil.isEmpty(contractEntity)) {
            throw new PartnerExceptionInfo(500, "合同不存在");
        }
        Long fkAgentId = contractEntity.getFkAgentId();
        MAgentEntity mAgentEntity = this.agentMapper.selectById(fkAgentId);
        if (ObjectUtil.isEmpty(mAgentEntity)) {
            throw new PartnerExceptionInfo(500, "代理不存在");
        }

        // 查询公司合同附件-如果失败的可以重新上传
        if (contractEntity.getContractApprovalStatus().equals(AgentContractApprovalStatusEnum.REJECTED.getCode())) {
            //删除原本的
            sMediaAndAttachedMapper.deleteSaleCenterAttachByParams("sale_contract_seal_file", contractId, "m_agent_contract");
        }
//        SMediaAndAttachedEntity mediaAndAttached = SMediaAndAttachedEntity.builder()
//                .fkTableId(contractId)
//                .typeKey("sale_contract_seal_file")
//                .fkTableName("m_agent_contract")
//                .build();
//        SMediaAndAttachedEntity attacheSealEntity = sMediaAndAttachedMapper.selectSaleOne(mediaAndAttached);
//        if (ObjectUtil.isNotEmpty(attacheSealEntity)) {
//            throw new PartnerExceptionInfo(500, "盖章合同文件已存在");
//        }

        //上传文件
        UploadFileVo uploadFileVo;
        try {
            uploadFileVo = partnerFileService.upload(file, true, FileUploadEnum.SALE_CENTER_CONTRACT_ATTACH.getCode(), customerFileName);
            //保存到对应的媒体库和文件库
            UploadFileParam uploadFileParam = UploadFileParam.builder()
                    .fileDb(StringUtils.substringBefore(FileUploadEnum.SALE_CENTER_CONTRACT_ATTACH.getFileCenter(), "."))
                    .fileTable(StringUtils.substringAfter(FileUploadEnum.SALE_CENTER_CONTRACT_ATTACH.getFileCenter(), "."))
                    .mediaDb(StringUtils.substringBefore(FileUploadEnum.SALE_CENTER_CONTRACT_ATTACH.getMediaTable(), "."))
                    .mediaTable(StringUtils.substringAfter(FileUploadEnum.SALE_CENTER_CONTRACT_ATTACH.getMediaTable(), "."))
                    .tableId(contractId)
                    .tableName("m_agent_contract")
                    .typeKey("sale_contract_seal_file")
                    .uploadTogether(Boolean.TRUE)
                    .mediaInfo(uploadFileVo)
                    .build();
            partnerFileService.saveFileAndMedia(uploadFileParam, false);
        } catch (Exception e) {
            log.error("上传文件失败:{}", e.getMessage());
            throw new PartnerExceptionInfo(500, "文件上传失败");
        }
        if (AgentNatureEnum.COMPANY.getCode().equals(mAgentEntity.getNature())) {
            // 公司上传附件, 更改合同状态
            contractEntity.setContractApprovalStatus(AgentContractApprovalStatusEnum.PENDING_APPROVAL.getCode());
            this.updateById(contractEntity);
        }

    }


    /**
     * 代理商确认签名
     *
     * @param contractDto 合同数据传输对象，包含合同ID和签名信息
     * @throws PartnerExceptionInfo 当合同不存在、代理不 存在、合同已签名或签名失败时抛出
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void agentConfirmSignature(ContractDto contractDto) {
        log.info("开始处理代理商确认签名，合同ID：{}", contractDto.getContractId());

        // 参数校验
        validateContractDto(contractDto);

        // 业务数据校验
        MAgentContractEntity contractEntity = validateAndGetContract(contractDto.getContractId());
        MAgentEntity agentEntity = validateAndGetAgent(contractEntity.getFkAgentId());
//        validateSignatureNotExists(contractDto.getContractId());

        //如果合同是审核失败的,需要重新上传签名,需要删除原本的签名
        if (contractEntity.getContractApprovalStatus().equals(AgentContractApprovalStatusEnum.REJECTED.getCode())) {
            List<RAgentContractSignatureEntity> existSignature = rAgentContractSignatureMapper.selectList(new LambdaQueryWrapper<RAgentContractSignatureEntity>()
                    .eq(RAgentContractSignatureEntity::getFkAgentContractId, contractDto.getContractId()));
            if (CollectionUtils.isNotEmpty(existSignature)) {
                rAgentContractSignatureMapper.deleteByIds(existSignature.stream().map(RAgentContractSignatureEntity::getId).collect(Collectors.toList()));
            }
        }
        // 创建签名记录
        createSignatureRecord(contractDto);

        // 根据代理商性质更新合同状态
        updateContractStatusByAgentNature(contractEntity, agentEntity);

        log.info("代理商确认签名完成，合同ID：{}", contractDto.getContractId());
    }

    /**
     * 校验合同数据传输对象
     *
     * @param contractDto 合同数据传输对象
     * @throws PartnerExceptionInfo 当参数为空或关键字段为空时抛出
     */
    private void validateContractDto(ContractDto contractDto) {
        if (ObjectUtil.isEmpty(contractDto)) {
            log.error("合同数据传输对象为空");
            throw new PartnerExceptionInfo(500, "合同数据不能为空");
        }

        if (ObjectUtil.isEmpty(contractDto.getContractId())) {
            log.error("合同ID为空");
            throw new PartnerExceptionInfo(500, "合同ID不能为空");
        }

        if (ObjectUtil.isEmpty(contractDto.getSignature())) {
            log.error("签名信息为空，合同ID：{}", contractDto.getContractId());
            throw new PartnerExceptionInfo(500, "签名信息不能为空");

        }
    }

    /**
     * 校验并获取合同实体
     *
     * @param contractId 合同ID
     * @return 合同实体
     * @throws PartnerExceptionInfo 当合同不存在时抛出
     */
    private MAgentContractEntity validateAndGetContract(Long contractId) {
        MAgentContractEntity contractEntity = this.getById(contractId);
        if (ObjectUtil.isEmpty(contractEntity)) {
            log.error("合同不存在，合同ID：{}", contractId);
            throw new PartnerExceptionInfo(500, "合同不存在");

        }
        return contractEntity;
    }

    /**
     * 校验并获取代理商实体
     *
     * @param agentId 代理商ID
     * @return 代理商实体
     * @throws PartnerExceptionInfo 当代理商不存在时抛出
     */
    private MAgentEntity validateAndGetAgent(Long agentId) {
        MAgentEntity agentEntity = this.agentMapper.selectById(agentId);
        if (ObjectUtil.isEmpty(agentEntity)) {
            log.error("代理商不存在，代理商ID：{}", agentId);
            throw new PartnerExceptionInfo(500, "代理不存在");
        }
        return agentEntity;
    }

    /**
     * 校验签名是否已存在
     *
     * @param contractId 合同ID
     * @throws PartnerExceptionInfo 当签名已存在时抛出
     */
    private void validateSignatureNotExists(Long contractId) {
        RAgentContractSignatureEntity existingSignature = rAgentContractSignatureMapper.selectOne(
                new LambdaQueryWrapper<RAgentContractSignatureEntity>()
                        .eq(RAgentContractSignatureEntity::getFkAgentContractId, contractId));
        if (ObjectUtil.isNotEmpty(existingSignature)) {
            log.error("合同已签名，合同ID：{}", contractId);
            throw new PartnerExceptionInfo(500, "合同已签名");

        }
    }

    /**
     * 创建签名记录
     *
     * @param contractDto 合同数据传输对象
     * @throws PartnerExceptionInfo 当签名失败时抛出
     */
    private void createSignatureRecord(ContractDto contractDto) {
        FzhUser fzhUser = SecurityUtils.getUser();
        RAgentContractSignatureEntity signatureEntity = new RAgentContractSignatureEntity();
        signatureEntity.setFkAgentContractId(contractDto.getContractId());
        signatureEntity.setSignature(contractDto.getSignature());
        signatureEntity.setGmtCreate(LocalDateTime.now());
        signatureEntity.setGmtCreateUser(fzhUser.getLoginId());

        int insertResult = rAgentContractSignatureMapper.insert(signatureEntity);
        if (insertResult <= 0) {
            log.error("签名记录创建失败，合同ID：{}", contractDto.getContractId());
            throw new PartnerExceptionInfo(500, "签名失败");
        }

        log.info("签名记录创建成功，合同ID：{}", contractDto.getContractId());
    }

    /**
     * 根据代理商性质更新合同状态
     * 非公司代理商：签名后直接进入待审核状态
     * 公司代理商：需要等待合同盖章回传后才能进入待审核状态
     *
     * @param contractEntity 合同实体
     * @param agentEntity    代理商实体
     */
    private void updateContractStatusByAgentNature(MAgentContractEntity contractEntity, MAgentEntity agentEntity) {
        String agentNature = agentEntity.getNature();
        // 如果性质非公司, 签名后直接进入待审核状态
        if (!AgentNatureEnum.COMPANY.getCode().equals(agentNature)) {
            log.info("个人代理商签名，更新合同状态为待审核，合同ID：{}", contractEntity.getId());
            contractEntity.setContractApprovalStatus(AgentContractApprovalStatusEnum.PENDING_APPROVAL.getCode());
            this.updateById(contractEntity);
        } else {
            log.info("公司代理商签名，等待合同盖章回传，合同ID：{}", contractEntity.getId());
        }
    }

    private Integer getContractStatus(AgentContractInfoVo contractInfo) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        // 当前时间
        LocalDateTime now = LocalDateTime.now();
        if (Objects.nonNull(contractInfo.getContractStatus()) && contractInfo.getContractStatus().equals(AgentContractApprovalStatusEnum.APPROVED.getCode())) {
            Date startDate = contractInfo.getStartTime();
            Date endDate = contractInfo.getEndTime();

            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                // 转换成 LocalDateTime（只保留到分钟）
                LocalDateTime startTime = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime endTime = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

                // 保留到分钟（格式化再解析）
                String nowStr = now.format(formatter);
                String startStr = startTime.format(formatter);
                String endStr = endTime.format(formatter);
                LocalDateTime formattedNow = LocalDateTime.parse(nowStr, formatter);
                LocalDateTime formattedStart = LocalDateTime.parse(startStr, formatter);
                LocalDateTime formattedEnd = LocalDateTime.parse(endStr, formatter);

                // 判断是否在有效期内
                if (!formattedNow.isBefore(formattedStart) && !formattedNow.isAfter(formattedEnd)) {
                    return AgentContractApprovalStatusEnum.EFFECTIVE.getCode(); // 生效中
                }
                if (formattedNow.isAfter(endTime)) {
                    return AgentContractApprovalStatusEnum.EXPIRED.getCode(); // 已过期
                }
                return contractInfo.getContractStatus();
            }
        }
        return contractInfo.getContractStatus();
    }

}
