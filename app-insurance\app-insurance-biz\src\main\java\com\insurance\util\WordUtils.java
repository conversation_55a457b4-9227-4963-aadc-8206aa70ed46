package com.insurance.util;

import cn.hutool.core.util.ObjectUtil;
import com.insurance.vo.settlement.AgentAccountVo;
import com.insurance.vo.settlement.SettlementBillDetailVo;
import com.insurance.vo.settlement.SettlementBillItemVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:word文档工具类
 */
@Slf4j
public class WordUtils {


    /**
     * 下载对账单-word
     *
     * @param settlementBillDetail
     */
    public static void downloadWord(SettlementBillDetailVo settlementBillDetail, HttpServletResponse response) throws IOException {
        BufferedOutputStream outputStream = null;
        try {
            outputStream = new BufferedOutputStream(response.getOutputStream());
            InputStream fis = WordUtils.class.getClassLoader().getResourceAsStream("template/SettlementBillTemplate.docx");
//            InputStream fis = WordUtils.class.getClassLoader().getResourceAsStream("template/test.docx");
            if (fis == null) {
                log.error("Word模板文件未找到，路径可能错误: template/SettlementBillTemplate.docx");
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "模板文件未找到");
                return;
            }

            XWPFDocument document = new XWPFDocument(fis);

            //第一个表格-订单明细
            XWPFTable table = document.getTables().get(0);
            if (ObjectUtil.isNotNull(table)) {
                // 我们只处理第一个表格
                initBillItems(table, settlementBillDetail.getBillItems());
            }
            //第二个表格-账户信息
            XWPFTable table1 = document.getTables().get(1);
            if (ObjectUtil.isNotNull(table)) {
                // 我们只处理第一个表格
                initAgentAccount(table1, settlementBillDetail.getAgentAccount(),
                        Objects.nonNull(settlementBillDetail.getTotalSettlementAmount()) ? settlementBillDetail.getTotalSettlementAmount() : BigDecimal.ZERO,
                        settlementBillDetail.getSettlementCurrencyTypeNum());
            }
            //第三个表格-签名
            XWPFTable table3 = document.getTables().get(3);
            if (ObjectUtil.isNotNull(table3) && StringUtils.isNoneBlank(settlementBillDetail.getSignature())) {
                // 添加图片
                initSignature(table3, settlementBillDetail.getSignature());
            }
            byte[] bytes = convertToByteArray(document);
            outputStream.write(bytes);
        } catch (Exception e) {
            outputStream.close();
            log.error("下载对账单-word异常");
            log.error("下载对账单-word异常信息:{}", e.getMessage());
        }
    }


    public static void initBillItems(XWPFTable table, List<SettlementBillItemVo> billItems) {
        // 获取表格的列数
        // 第2行定义了表格结构-表头
        int columnCount = table.getRow(2).getTableCells().size();
        // 动态添加多行数据，从第三行开始（索引为2）
        // 第三行的索引
        int startIndex = 2;
        int numNewRows = billItems.size();
        // 要添加的新行数
        XWPFTableRow firstRow = table.getRow(2);
        // 创建新行并设置内容
        for (int i = 0; i < numNewRows; i++) {
            SettlementBillItemVo item = billItems.get(i);
            XWPFTableRow newRow = firstRow;
            for (int col = 0; col < columnCount; col++) {
                if (col == 0) {
                    //保单号
                    setCellText(newRow.getCell(col), item.getInsuranceNum());
                } else if (col == 1) {
                    //保险公司
                    setCellText(newRow.getCell(col), item.getInsuranceCompanyName());
                } else if (col == 2) {
                    //保险产品
                    setCellText(newRow.getCell(col), item.getProductTypeName());
                } else if (col == 3) {
                    //学校姓名
                    setCellText(newRow.getCell(col), item.getInsurantName());
                } else if (col == 4) {
                    //生日
                    setCellText(newRow.getCell(col), formatDate(item.getInsurantBirthday()));
                } else if (col == 5) {
                    //保险购买日期
                    setCellText(newRow.getCell(col), formatDate(item.getGmtCreate()));
                } else if (col == 6) {
                    //保险生效日期
                    setCellText(newRow.getCell(col), formatDate(item.getInsuranceStartTime()));
                } else if (col == 7) {
                    //保险结束日期
                    setCellText(newRow.getCell(col), formatDate(item.getInsuranceEndTime()));
                } else if (col == 8) {
                    //币种
                    setCellText(newRow.getCell(col), item.getFkCurrencyTypeNum());
                } else if (col == 9) {
                    //保险金额
                    setCellText(newRow.getCell(col), item.getInsuranceAmount().toString());
                } else if (col == 10) {
                    //结算比例
                    setCellText(newRow.getCell(col), item.getCommissionRateStr());
                } else if (col == 11) {
                    //结算金额
                    setCellText(newRow.getCell(col), item.getSettlementAmount().toString());
                }
            }
            table.addRow(newRow, startIndex + i);
        }
        table.removeRow(startIndex + numNewRows);
    }


    public static void initAgentAccount(XWPFTable table, AgentAccountVo account, BigDecimal totalSettlementAmount, String settlementCurrencyTypeNum) {

        // 遍历每一行
        for (XWPFTableRow row : table.getRows()) {
            // 遍历每个单元格
            for (XWPFTableCell cell : row.getTableCells()) {
                // 遍历单元格中的段落
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        String text = run.getText(0);
                        if (text != null && text.contains("${Bank}")) {
                            run.setText(account.getBankName(), 0);
                        } else if (text != null && text.contains("${Branch}")) {
                            run.setText(account.getBankBranchName(), 0);
                        } else if (text != null && text.contains("${AccountName}")) {
                            run.setText(account.getBankAccount(), 0);
                        } else if (text != null && text.contains("${AccountNo}")) {
                            run.setText(account.getBankAccountNum(), 0);
                        } else if (text != null && text.contains("${BankAddress}")) {
                            run.setText(account.getBankAddress(), 0);
                        } else if (text != null && text.contains("${swiftCode}")) {
                            run.setText(account.getAreaCountryCode(), 0);
                        } else if (text != null && text.contains("${totalSettlementAmount}")) {
                            run.setText(totalSettlementAmount.toString() + (StringUtils.isNoneBlank(settlementCurrencyTypeNum) ?
                                    settlementCurrencyTypeNum : ""), 0);
                        }
                    }
                }
            }
        }

    }

    private static void initSignature(XWPFTable table, String signature) {

        // 遍历每一行
        for (XWPFTableRow row : table.getRows()) {
            // 遍历每个单元格
            for (XWPFTableCell cell : row.getTableCells()) {
                // 遍历单元格中的段落
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        String text = run.getText(0);
                        if (text != null && text.contains("${signature}")) {
                            // 插入签字图片
                            try {
                                // 解码 Base64 字符串，去掉 data URL 前缀（如果有）
                                byte[] imageBytes = Base64.getDecoder().decode(signature.split(",")[1]);
                                ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
                                run.addPicture(bis, Document.PICTURE_TYPE_PNG, "signature.png", Units.toEMU(200), Units.toEMU(100));
                                run.setText("", 0); // 清除原有文本
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            } catch (InvalidFormatException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
        }
    }

    public static void setCellText(XWPFTableCell cell, String text) {
        // 清除单元格内的所有段落
        while (cell.getParagraphs().size() > 0) {
            cell.removeParagraph(0);
        }
        // 创建新的段落并设置文本
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun run = paragraph.createRun();
        run.setText(text);
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        return sdf.format(date);
    }

    public static byte[] convertToByteArray(XWPFDocument document) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 将文档写入 ByteArrayOutputStream
            document.write(baos);

            // 获取字节数组
            return baos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            // 确保关闭 XWPFDocument 以释放资源
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


}

