package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.UExchangeRateEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【u_exchange_rate】的数据库操作Mapper
* @createDate 2025-01-20 14:14:23
* @Entity com.partner.entity.UExchangeRate
*/
@Mapper
public interface UExchangeRateMapper extends BaseMapper<UExchangeRateEntity> {

}




