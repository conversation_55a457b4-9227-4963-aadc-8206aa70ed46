<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.PartnerUserClientMapper">


    <select id="getClientList" resultType="com.insurance.entity.PartnerUserClient">
        select *
        from m_partner_user_client c
        <where>
            c.fk_partner_user_id = #{partnerUserId}
            <if test="keyword != null and keyword != ''">
                and
                (c.client_name like concat('%',#{keyword},'%') or c.email = #{keyword} or c.mobile = #{keyword})
            </if>
            <if test="progress != null and progress == 1">
                and
                ( exists (select 1 from m_insurance_order o where o.insurant_email = c.email and o.order_status = -2)
                    or not exists (select 1 from m_insurance_order o where o.insurant_email = c.email))
            </if>
            <if test="progress != null and progress == 2">
                and exists (select 1 from m_insurance_order o where o.insurant_email = c.email and o.order_status in (0,1,2))
            </if>
            order by c.id desc
        </where>
    </select>
</mapper>