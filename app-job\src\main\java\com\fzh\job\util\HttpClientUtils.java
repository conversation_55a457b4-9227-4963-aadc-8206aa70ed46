package com.fzh.job.util;

import com.alibaba.fastjson.JSON;
import com.fzh.job.service.impl.MLiveMessageSendServiceImpl;
import com.partner.wechat.params.SendMessageParams;
import com.partner.wechat.result.AccessTokenResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

public class HttpClientUtils {
    private static final Logger log = LoggerFactory.getLogger(HttpClientUtils.class);
    /**
     *
     * @param url
     * @param headerParams
     * @param sendMessageParams
     * @desc 订阅模板发送信息方法
     * @return
     */
    public static String sendMessagePostRequest(String url, Map<String, String> headerParams,
                                          SendMessageParams sendMessageParams) {
        RestTemplate client = new RestTemplate();
        //设置请求发送方式HttpMethod.GET、HttpMethod.DELETE等
        HttpMethod method = HttpMethod.POST;

        HttpHeaders headers = new HttpHeaders();
        //给请求头设置参数
        for (String key : headerParams.keySet()) {
            headers.add(key, headerParams.get(key));
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        //请求参数
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(sendMessageParams), headers);

        //执行HTTP请求，将返回的结构使用String 类格式化（可设置为对应返回值格式的类）
        ResponseEntity<String> response = client.exchange(url, method, requestEntity, String.class);
        log.info("推送模板消息参数:{};推送模板消息结果:{}",JSON.toJSONString(sendMessageParams), response.getBody());
        //返回类型也可以自动填充到实体类当中去，比如我自己创建了User类，当然字段名称要和返回字段一致
        return response.getBody();
    }


    /**
     * 向目的URL发送post请求
     *
     * @param url          目的url
     * @param headerParams 请求头参数	key：value
     * @param bodyParams   请求体参数 	key：value
     * @return
     */
    public static String sendPostRequest(String url, Map<String, String> headerParams,
                                         Map<String, String> bodyParams) {
        RestTemplate client = new RestTemplate();
        //新建Http头，add方法可以添加参数
        HttpHeaders headers = new HttpHeaders();
        //给请求头设置参数
        for (String key : headerParams.keySet()) {
            headers.add(key, headerParams.get(key));
        }
        //设置请求发送方式HttpMethod.GET、HttpMethod.DELETE等
        HttpMethod method = HttpMethod.POST;
        // 设置提交方式这里设置成application/json格式
        headers.setContentType(MediaType.APPLICATION_JSON);
        //将请求头部和参数合成一个请求
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(bodyParams, headers);
        //执行HTTP请求，将返回的结构使用String 类格式化（可设置为对应返回值格式的类）
        ResponseEntity<String> response = client.exchange(url, method, requestEntity, String.class);
        //返回类型也可以自动填充到实体类当中去，比如我自己创建了User类，当然字段名称要和返回字段一致
        return response.getBody();
    }

}
