package com.insurance.config;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class JsonRedisService {

    @Autowired
    @Qualifier("jsonRedisTemplate")
    private RedisTemplate<String, Object> jsonRedisTemplate;

    /**
     * 存对象为 JSON
     */
    public void setObjectAsJson(String key, Object value, long timeout, TimeUnit unit) {
        try {
            jsonRedisTemplate.opsForValue().set(key, value, timeout, unit);
        } catch (Exception e) {
            log.error("Redis 存储 JSON 失败，key:{} value:{} error:{}", key, value, e.getMessage(), e);
        }
    }

    public <T> T getObjectFromJson(String key, Class<T> clazz) {
        try {
            Object obj = jsonRedisTemplate.opsForValue().get(key);
            if (obj == null) {
                return null;
            }
            // obj 可能是 LinkedHashMap，先转成 JSON 字符串，再转目标对象
            String jsonStr = JSONObject.toJSONString(obj);
            return JSONObject.parseObject(jsonStr, clazz);
        } catch (Exception e) {
            log.error("Redis 获取 JSON 失败，key:{} error:{}", key, e.getMessage(), e);
        }
        return null;
    }


    /**
     * 获取对象，默认返回 Object
     */
    public Object getObject(String key) {
        try {
            return jsonRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Redis 获取 JSON 失败，key:{} error:{}", key, e.getMessage(), e);
            return null;
        }
    }

}
