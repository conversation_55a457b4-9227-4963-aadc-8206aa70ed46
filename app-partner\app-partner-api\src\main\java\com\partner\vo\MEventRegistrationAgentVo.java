package com.partner.vo;

import com.partner.entity.EventEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MEventRegistrationAgentVo  extends EventEntity {


    @Schema(description="学生代理Id")
    private Long fkAgentId;
    @Schema(description="学生代理")
    private String agentName;
    @Schema(description="伙伴用户Id")
    private Long fkPartnerUserId;
    @Schema(description="伙伴用户")
    private String partnerUserName;
    @Schema(description="枚举状态：0待定/1参加/2不参加")
    private Integer status;
    @Schema(description="状态名称")
    private String statusName;


    public String getStatusName() {
        if(status!=null){
            if(status==0){
                statusName="待定";
            }else if(status==1){
                statusName="参加";
            } else if (status==2) {
                statusName="不参加";
            }
        }

        return statusName;
    }
}
