server:
  port: 3000

spring:
  application:
    name: auth
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:************}:${NACOS_PORT:8852}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - nacos:<EMAIL>@.yml
      - nacos:${spring.application.name}-@profiles.active@.yml

#配置日志
logging:
  config: classpath:log/<EMAIL>@.xml

#rocketmq:
#  # 配置 NameServer 地址
#  name-server: ************:9876
#  # 生产者分组
#  producer:
#    group: user_offline_topic
#    # 发送超时时间（毫秒）
#    send-message-timeout: 3000
#    # 生产者发送失败的最大重试次数
#    retry-times-when-send-failed: 3
#  consumer:
#    # 消费者分组
#    group: user_offline_consumer_group
#    # 消息最大重试次数（超出后进入死信队列）
#    max-reconsume-times: 3
#    # 开启消息轨迹
#    enable-msg-trace: true