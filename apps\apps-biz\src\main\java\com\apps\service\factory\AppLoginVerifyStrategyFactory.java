package com.apps.service.factory;

import com.apps.service.strategy.AppLoginVerifyStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 渠道策略工厂类
 *
 * <AUTHOR>
 * @since 2024-07-10 15:50
 */
@Component
public class AppLoginVerifyStrategyFactory {

    @Resource
    public Map<String, AppLoginVerifyStrategy> appLoginVerifyStrategyHashMap = new HashMap<>();

    public AppLoginVerifyStrategy getLoginVerifyStrategy(String code) {
        return appLoginVerifyStrategyHashMap.get(code);
    }
}