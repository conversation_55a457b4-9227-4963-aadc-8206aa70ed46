<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.FinanceMapper">


    <select id="getCommissionAffirmPage" resultType="com.partner.vo.finance.CommissionAffirmVo">
        SELECT
        offerItem.opening_time AS openingTime,
        (
         SELECT rStudentOfferItemStep.gmt_create COETime FROM    ais_sale_center.r_student_offer_item_step rStudentOfferItemStep
        WHERE  offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id AND rStudentOfferItemStep.fk_student_offer_item_step_id  IN(6,10)
        ORDER BY  rStudentOfferItemStep.fk_student_offer_item_step_id ASC LIMIT 1
        ) AS COETime,
        offerItem.id AS offerItemId,
        offerItem.tuition_amount AS tuitionAmount,
        offerItemUUID.fk_student_offer_item_uuid AS offerItemUUID,
        student.name AS studentsName,
        student.birthday AS birthday,
        institution.name                  AS institutionName,
        offerItem.fk_currency_type_num    AS fkCurrencyTypeNum,
        institution.name_chn              AS institutionNameChn,
        institutionCourse.name AS courseName,
        offerItemStep.step_name AS stepName,
        uAreaCountry.name  AS  areaCountryName,
        uAreaCountry.name_chn AS  areaCountryNameChn
        FROM ais_sale_center.m_student_offer_item offerItem
        INNER JOIN
        (
        SELECT  offerItem.id AS itemId,min(rStudentOfferItemStep.gmt_create) AS gmt_create FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionSqlRolePage"/>
        INNER JOIN  ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
        WHERE    rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10) GROUP BY offerItem.id
        ) a  ON  a.itemId=offerItem.id
        INNER JOIN  ais_sale_center.m_student student ON student.id = offerItem.fk_student_id
        INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
        INNER JOIN ais_sale_center.u_student_offer_item_step AS offerItemStep  ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
        INNER JOIN ais_sale_center.r_student_offer_item_uuid  offerItemUUID    ON offerItem.id=offerItemUUID.fk_student_offer_item_id
        LEFT JOIN  ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
        LEFT JOIN  app_partner_center.m_student_offer_item_agent_confirm offerItemAgentConfirm ON offerItemAgentConfirm.fk_student_offer_item_id=offerItem.id
        LEFT JOIN  ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id

        WHERE  offerItem.fk_student_offer_item_step_id  &lt;&gt; 9 AND YEAR(a.gmt_create)=#{query.year} AND MONTH(a.gmt_create)=#{query.month}

        <if test="query.type!=null and query.type==0">
            and offerItemAgentConfirm.id is null
        </if>
        <if test="query.type!=null and query.type==1">
            and offerItemAgentConfirm.is_system_confirmed=0 ORDER BY offerItemAgentConfirm.gmt_create DESC
        </if>
        <if test="query.type!=null and query.type==2">
            and offerItemAgentConfirm.is_system_confirmed=1 ORDER BY offerItemAgentConfirm.gmt_create DESC
        </if>

    </select>


    <select id="getAgentSettlementList" resultType="com.partner.vo.finance.AgentSettlementOfferItemVo">
        SELECT

            mPayablePlan.id  AS payablePlanId,
            agent.id AS agentId,
            agent.name  AS agentName,
            studentUuid.fk_student_uuid AS studentUUID,
            student.name AS studentName,
            student.birthday AS birthday,
            a.fk_agent_contract_account_id,
            a.fk_currency_type_num AS accountCurrencyTypeNum,
            a.status_settlement,
            a.is_roll_back AS rollBack,
            uAreaCountry.name_chn AS countryName,
            institution.name                  AS institutionName,
            institution.name_chn              AS institutionNameChn,
            institutionCourse.name AS courseName,
            offerItem.opening_time AS openingTime,
            offerItem.defer_opening_time AS defer_opening_time,
            offerItem.id AS offerItemId,
            offerItem.gmt_create,
            (
            SELECT rStudentOfferItemStep.gmt_create  FROM    ais_sale_center.r_student_offer_item_step rStudentOfferItemStep
            WHERE  offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id AND rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,10)
               ORDER BY  rStudentOfferItemStep.fk_student_offer_item_step_id ASC
            LIMIT 1
            ) AS COETime,
            mPayablePlan.fk_currency_type_num  AS fkCurrencyTypeNum,
            IFNULL(mPayablePlan.payable_amount, 0 )  AS payableAmount,
            IFNULL(a.serviceFeeActual, 0 )  AS serviceFeeActual,
            IFNULL(a.amount_actual, 0 )  AS  amountActual,
            (SELECT IFNULL( SUM( mPaymentFormItem.amount_payable ), 0 ) + IFNULL( SUM( mPaymentFormItem.amount_exchange_rate ), 0 ) from ais_finance_center.m_payment_form mPaymentForm
                                                                                                                                             INNER JOIN  ais_finance_center.m_payment_form_item AS mPaymentFormItem ON mPaymentFormItem.fk_payment_form_id = mPaymentForm.id AND mPaymentForm.status = 1
             WHERE mPaymentFormItem.fk_payable_plan_id = mPayablePlan.id  )  AS paidAmount,
            (
            SELECT  CASE MAX( mReceivablePlan.receivable_amount ) - IFNULL(SUM( mReceiptFormItem.amount_receivable ),0) - IFNULL(SUM( mReceiptFormItem.amount_exchange_rate ),0)
            WHEN SUM( mReceivablePlan.receivable_amount ) THEN  0
            WHEN 0 THEN 2 ELSE 1 END AS STATUS
            FROM ais_sale_center.m_receivable_plan mReceivablePlan
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mReceiptFormItem ON mReceiptFormItem.fk_receivable_plan_id = mReceivablePlan.id
            LEFT JOIN ais_finance_center.m_receipt_form AS mReceiptForm ON mReceiptForm.id = mReceiptFormItem.fk_receipt_form_id
            WHERE
                mReceivablePlan.id = mPayablePlan.fk_receivable_plan_id
                AND mReceivablePlan.STATUS = 1
                AND mReceiptForm.status = 1
                AND mReceiptForm.settlement_status = 1
            ) AS status



        FROM ais_sale_center.m_student_offer_item offerItem
                 INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
                 INNER JOIN  ais_sale_center.m_agent agent ON offerItem.fk_agent_id = agent.id
                 INNER JOIN  ais_sale_center.m_payable_plan  mPayablePlan ON mPayablePlan.fk_type_target_id = offerItem.id AND mPayablePlan.fk_type_key = 'm_student_offer_item'
                 INNER JOIN  ais_sale_center.m_student student ON student.id = offerItem.fk_student_id
                 INNER JOIN
             <!-- 实际支付金额 -->
             (SELECT
                  installmentPayablePlan.fk_payable_plan_id,
                  installmentPayablePlan.fk_agent_contract_account_id,
                  installmentPayablePlan.fk_currency_type_num,
                  installmentPayablePlan.status_settlement,
                  CASE WHEN installmentPayablePlan.status = 0 THEN SUM(installmentPayablePlan.amount_actual) ELSE MAX(installmentPayablePlan.amount_actual) END amount_actual,
                  CASE WHEN installmentPayablePlan.status = 0 THEN SUM(installmentPayablePlan.service_fee_actual) ELSE MAX(installmentPayablePlan.service_fee_actual) END serviceFeeActual,
                  MAX( CASE WHEN installmentPayablePlan.fk_receipt_form_item_id IS NULL OR  installmentPayablePlan.fk_receipt_form_item_id = 0 THEN installmentPayablePlan.gmt_create ELSE NULL END ) AS settlementCreateTime,
                  CASE WHEN COUNT(fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark,
                  MAX(installmentPayablePlan.account_export_time) AS account_export_time,
                  MIN(installmentPayablePlan.is_roll_back) AS is_roll_back,
                  SUM( IFNULL(mReceiptFormItem.amount_receivable, 0) ) AS amount_receivable,
                  MAX(installmentPayablePlan.fk_receipt_form_item_id) AS maxFkReceiptFormItemId,
                  GROUP_CONCAT(installmentPayablePlan.id) AS settlementIds,
                  IFNULL(MAX(rInvoiceReceivablePlan.is_pay_in_advance), 0) AS is_pay_in_advance
              FROM  ais_finance_center.r_payable_plan_settlement_installment installmentPayablePlan
                        LEFT JOIN ais_finance_center.m_receipt_form_item  mReceiptFormItem ON mReceiptFormItem.id = installmentPayablePlan.fk_receipt_form_item_id
                        LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rInvoiceReceivablePlan ON rInvoiceReceivablePlan.id = installmentPayablePlan.fk_invoice_receivable_plan_id
              WHERE  installmentPayablePlan.status_settlement = 1   GROUP BY installmentPayablePlan.fk_payable_plan_id, installmentPayablePlan.fk_agent_contract_account_id,installmentPayablePlan.status_settlement
             ) a ON a.fk_payable_plan_id = mPayablePlan.id
                 LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id
                 LEFT JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
                 LEFT JOIN  ais_sale_center.r_student_uuid studentUuid  ON studentUuid.fk_student_id=student.id
        WHERE   offerItem.is_follow_hidden = 0
          AND mPayablePlan.STATUS = 1 AND agent.is_active = 1   and agent.id=#{query.agentId}



    </select>
    <select id="selectstatusSettlement" resultType="java.lang.Integer">
        SELECT COUNT(*) AS  num
        from m_settlement_bill mSettlementBill
                 INNER JOIN m_settlement_bill_item  mSettlementBillItem
                            ON mSettlementBill.id=mSettlementBillItem.fk_settlement_bill_id
                 INNER JOIN r_settlement_bill_settlement_installment rSettlementBill ON rSettlementBill.fk_settlement_bill_id=mSettlementBill.id
                 INNER JOIN ais_finance_center.r_payable_plan_settlement_installment  rPayablePlanSettlement
                            ON rSettlementBill.fk_payable_plan_settlement_installment_id=rPayablePlanSettlement.id

        WHERE mSettlementBill.ID IN(SELECT MAX(ID) FROM m_settlement_bill WHERE fk_agent_id=#{agentId}) AND rPayablePlanSettlement.status_settlement IN(2,3)

    </select>
    <select id="getReceivablePlan" resultType="com.partner.entity.MReceivablePlanEntity">
        select  mReceivablePlan.*  from ais_sale_center.m_receivable_plan  mReceivablePlan
        WHERE mReceivablePlan.fk_type_key='m_student_offer_item' AND mReceivablePlan.fk_type_target_id  IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>