package com.payment.util;

import com.payment.config.PayConfig;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.util.*;

/**
 * @Author:Oliver
 * @Date: 2025/7/15
 * @Version 1.0
 * @apiNote:微信支付工具类
 */
@Slf4j
public class WeChatPayUtils {

    /**
     * 创建微信支付配置
     *
     * @param config
     * @return
     * @throws Exception
     */
    public static Config createWxPayConfig(PayConfig config) throws Exception {
        Objects.requireNonNull(config.getWxPrivateKeyPath(), "微信私钥路径不能为空");
        Objects.requireNonNull(config.getWxPublicKeyPath(), "微信公钥路径不能为空");

        // 加载私钥
        ClassPathResource privateKeyResource = new ClassPathResource(config.getWxPrivateKeyPath());
        PrivateKey privateKey;
        try (InputStream inputStream = privateKeyResource.getInputStream()) {
            privateKey = PemUtil.loadPrivateKey(inputStream);
        }

        // 加载微信平台公钥
        ClassPathResource publicKeyResource = new ClassPathResource(config.getWxPublicKeyPath());
        PublicKey publicKey;
        try (InputStream inputStream = publicKeyResource.getInputStream()) {
            publicKey = PemUtil.loadPublicKey(inputStream);
        }

        return new RSAPublicKeyConfig.Builder()
                .merchantId(config.getMchId())
                .merchantSerialNumber(config.getMchSerialNo())
                .privateKey(privateKey)
                .apiV3Key(config.getApiV3Key())
                .publicKey(publicKey)
                .publicKeyId(config.getWxPlatformCertId())
                .build();
    }

    /**
     * 创建微信支付服务
     * @param config
     * @return
     */
    public static JsapiService createJsapiService(Config config) {
        return new JsapiService.Builder().config(config).build();
    }

    /**
     * 构建微信支付参数
     *
     * @param prepayId
     * @param appId
     * @param privateKeyPath
     * @return
     * @throws Exception
     */
    public static Map<String, String> buildPayParams(String prepayId, String appId, String privateKeyPath) throws Exception {
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = UUID.randomUUID().toString().replaceAll("-", "");
        String pkg = "prepay_id=" + prepayId;
        String signType = "RSA";

        String message = String.join("\n", appId, timeStamp, nonceStr, pkg, "");
        log.info(message);

        // 读取私钥
        PrivateKey privateKey = PemUtil.loadPrivateKey(new ClassPathResource(privateKeyPath).getInputStream());

        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initSign(privateKey);
        sign.update(message.getBytes(StandardCharsets.UTF_8));
        String paySign = Base64.getEncoder().encodeToString(sign.sign());

        Map<String, String> result = new HashMap<>();
        result.put("timeStamp", timeStamp);
        result.put("nonceStr", nonceStr);
        result.put("package", pkg);
        result.put("signType", signType);
        result.put("paySign", paySign);
        return result;
    }
}
