package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RStudentOfferItemUuidEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【r_student_offer_item_uuid】的数据库操作Mapper
* @createDate 2025-01-03 10:45:59
* @Entity com.partner.entity.RStudentOfferItemUuid
*/
@Mapper
public interface RStudentOfferItemUuidMapper extends BaseMapper<RStudentOfferItemUuidEntity> {
    RStudentOfferItemUuidEntity selectByUUID(@Param("uuid") String uuid);


}




