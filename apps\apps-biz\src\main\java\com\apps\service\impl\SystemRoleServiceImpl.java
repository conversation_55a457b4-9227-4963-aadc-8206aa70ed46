package com.apps.service.impl;

import com.apps.api.dto.partner.UpdateUserRoleDto;
import com.apps.api.dto.system.SaveRoleDto;
import com.apps.api.entity.*;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.exception.AppsGlobalException;
import com.apps.mapper.*;
import com.apps.rocketmq.msg.UserOfflineDto;
import com.apps.rocketmq.producer.UserOfflineProducer;
import com.apps.service.SystemRoleService;
import com.apps.util.RedisUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.constant.CommonConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class SystemRoleServiceImpl extends ServiceImpl<SystemRoleMapper, SystemRoleEntity> implements SystemRoleService {

    private final SystemUserMapper systemUserMapper;

    private final SystemRoleMapper systemRoleMapper;

    private final SystemUserRoleMapper userRoleMapper;

    private final SystemRoleMenuMapper roleMenuMapper;

    private final SystemUserPlatformLoginMapper platformLoginMapper;

    private final UserOfflineProducer userOfflineProducer;

    private final RedisUtil redisUtil;

    @Override
    public void saveRole(SaveRoleDto roleDto) {
        checkRole(roleDto);
        if (Objects.isNull(roleDto.getId())) {
            SystemRoleEntity role = new SystemRoleEntity();
            BeanUtils.copyProperties(roleDto, role);
            role.setIsDelFlag(Integer.parseInt(CommonConstants.STATUS_NORMAL));
            role.setGmtCreate(LocalDateTime.now());
            role.setGmtModified(LocalDateTime.now());
            systemRoleMapper.insert(role);
            return;
        }
        SystemRoleEntity systemRoleEntity = systemRoleMapper.selectById(roleDto.getId());
        if (Objects.nonNull(systemRoleEntity)) {
            BeanUtils.copyProperties(roleDto, systemRoleEntity);
            systemRoleEntity.setGmtModified(LocalDateTime.now());
            systemRoleMapper.updateById(systemRoleEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delRole(Long roleId) {
        SystemRoleEntity role = systemRoleMapper.selectById(roleId);
        if (Objects.nonNull(role)) {
            Long userCount = userRoleMapper.selectCount(new LambdaQueryWrapper<SystemUserRoleEntity>()
                    .eq(SystemUserRoleEntity::getFkRoleId, roleId));
            if (userCount > 0) {
                throw new AppsGlobalException(GlobExceptionEnum.SYS_ROLE_BIND_USER);
            }
            role.setIsDelFlag(Integer.parseInt(CommonConstants.STATUS_DEL));
            role.setGmtModified(LocalDateTime.now());
            systemRoleMapper.updateById(role);
            //删除权限
            List<Long> delIds = roleMenuMapper.selectList(new LambdaQueryWrapper<SystemRoleMenuEntity>()
                            .eq(SystemRoleMenuEntity::getFkRoleId, roleId))
                    .stream().map(SystemRoleMenuEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delIds)) {
                roleMenuMapper.deleteBatchIds(delIds);
            }
        }
    }

    @Override
    public List<Map<String, Object>> getRoleList(Long platformId, String platformCode) {
        return systemRoleMapper.selectList(new LambdaQueryWrapper<SystemRoleEntity>()
                .eq(SystemRoleEntity::getFkPlatformId, platformId)
                .eq(SystemRoleEntity::getFkPlatformCode, platformCode)).stream().map(role -> {
            Map<String, Object> map = new HashMap<>();
            map.put("roleId", role.getId());
            map.put("roleName", role.getRoleName());
            map.put("roleCode", role.getRoleCode());
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * 修改用户角色并删除用户缓存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserRole(UpdateUserRoleDto updateUserRoleDto) {
        // 获取用户信息
        Long userId = updateUserRoleDto.getUserId();

        // 1. 更新用户角色
        LambdaQueryWrapper<SystemUserRoleEntity> systemUserRoleEntityLambdaQueryWrapper = new LambdaQueryWrapper<SystemUserRoleEntity>()
                .eq(SystemUserRoleEntity::getFkUserId, userId);
        List<SystemUserRoleEntity> systemUserRoleEntities = this.userRoleMapper.selectList(systemUserRoleEntityLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(systemUserRoleEntities)) {
            // 删除原有角色关联
            this.userRoleMapper.delete(systemUserRoleEntityLambdaQueryWrapper);
        }

        // 创建新的角色关联
        SystemUserRoleEntity userRole = SystemUserRoleEntity.builder()
                .fkUserId(userId)
                .fkRoleId(updateUserRoleDto.getRoleId())
                .gmtCreateUser(updateUserRoleDto.getCreateUser())
                .gmtCreate(LocalDateTime.now())
                .gmtModifiedUser(updateUserRoleDto.getCreateUser())
                .gmtModified(LocalDateTime.now())
                .build();
        this.userRoleMapper.insert(userRole);
    }

    /**
     * 删除缓存并下线
     *
     * @param userId
     */
    @Override
    public void delUserCache(Long userId) {
        SystemUserEntity systemUser = systemUserMapper.selectById(userId);
        if (Objects.isNull(systemUser)) {
            log.error("未找到用户信息,用户ID:{}", userId);
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        // 事务成功提交后，再删除缓存
        redisUtil.del(systemUser.getFkTenantId() + ":" + systemUser.getFkFromPlatformCode() + ":" + userId);
        //发送下线消息
        SystemUserPlatformLoginEntity platformLogin = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getFkUserId, userId));
        if (Objects.nonNull(platformLogin)) {
            UserOfflineDto userOfflineDto = new UserOfflineDto(Arrays.asList(platformLogin.getLoginId()), systemUser.getFkFromPlatformId());
            userOfflineProducer.sendUserOfflineMessage(userOfflineDto);
        }
    }

    @Override
    public void userOfferLine(List<String> loginIds) {
        log.info("用户下线处理,用户信息:{}", loginIds);
        if (CollectionUtils.isEmpty(loginIds)) {
            log.info("用户下线处理,用户信息为空");
            return;
        }
        Map<Long, List<SystemUserPlatformLoginEntity>> listMap = platformLoginMapper.selectList(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                        .in(SystemUserPlatformLoginEntity::getLoginId, loginIds))
                .stream()
                .filter(item -> Objects.nonNull(item.getFkPlatformId()))
                .collect(Collectors.groupingBy(SystemUserPlatformLoginEntity::getFkPlatformId));
        listMap.forEach((platformId, list) -> {
            List<String> ids = list.stream().map(SystemUserPlatformLoginEntity::getLoginId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                UserOfflineDto userOfflineDto = new UserOfflineDto(ids, platformId);
                userOfflineProducer.sendUserOfflineMessage(userOfflineDto);
            }
        });
    }

    private void checkRole(SaveRoleDto role) {
        //判断角色名称
        Long nameCount = systemRoleMapper.selectCount(new LambdaQueryWrapper<SystemRoleEntity>()
                .eq(SystemRoleEntity::getRoleName, role.getRoleName())
                .eq(SystemRoleEntity::getFkPlatformId, role.getFkPlatformId())
                .eq(SystemRoleEntity::getFkPlatformCode, role.getFkPlatformCode())
                .eq(SystemRoleEntity::getIsDelFlag, Integer.parseInt(CommonConstants.STATUS_NORMAL))
                .ne(Objects.nonNull(role.getId()), SystemRoleEntity::getId, role.getId()));
        if (nameCount > 0) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_ROLE_NAME_EXIST);
        }
        //判断角色编码
        Long codeCount = systemRoleMapper.selectCount(new LambdaQueryWrapper<SystemRoleEntity>()
                .eq(SystemRoleEntity::getRoleCode, role.getRoleCode())
                .eq(SystemRoleEntity::getFkPlatformId, role.getFkPlatformId())
                .eq(SystemRoleEntity::getFkPlatformCode, role.getFkPlatformCode())
                .eq(SystemRoleEntity::getIsDelFlag, Integer.parseInt(CommonConstants.STATUS_NORMAL))
                .ne(Objects.nonNull(role.getId()), SystemRoleEntity::getId, role.getId()));
        if (codeCount > 0) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_ROLE_CODE_EXIST);
        }
    }
}
