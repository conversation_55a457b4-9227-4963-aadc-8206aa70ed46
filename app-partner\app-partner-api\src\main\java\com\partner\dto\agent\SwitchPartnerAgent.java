package com.partner.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/7/7
 * @Version 1.0
 * @apiNote:切换代理
 */
@Data
public class SwitchPartnerAgent {

    @Schema(description = "伙伴用户id")
    @NotNull(message = "伙伴用户id不能为空")
    private Long partnerUserId;

    @Schema(description = "代理id")
    @NotNull(message = "代理id不能为空")
    private Long agentId;
}
