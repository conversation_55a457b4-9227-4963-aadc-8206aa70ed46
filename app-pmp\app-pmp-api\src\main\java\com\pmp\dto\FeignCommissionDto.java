package com.pmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/3/5  16:28
 * @Version 1.0 学校佣金查询参数 pmp2.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeignCommissionDto {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "查询日期")
    private Date startDate;

    @Schema(description = "学校ID")
    @NotNull(message = "学校ID不能为空")
    private Long institutionId;

    @Schema(description = "代理ID")
    @NotNull(message = "代理ID不能为空")
    private Long agentId;

    @Schema(description = "分公司ID")
    @NotNull(message = "分公司ID不能为空")
    private Long companyId;

    @Schema(description = "高佣学校平台")
    private String highCommissionCode;
}
