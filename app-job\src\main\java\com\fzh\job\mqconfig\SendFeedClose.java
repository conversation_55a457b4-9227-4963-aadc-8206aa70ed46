package com.fzh.job.mqconfig;

import com.partner.mqmessage.FeedCloseMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class SendFeedClose {


    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producerfeedclose.group:send_feed_close_message_topic}")
    private String TOPIC;


    /**
     * 发送异步消息
     * @param -消息体
     */
    public void sendCommissionMessage(FeedCloseMessage messageParams){
        log.info("发送反馈信息，messageParams={},topic={}", messageParams, TOPIC);

        rocketMQTemplate.asyncSend(TOPIC, messageParams,new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送反馈信息成功，messageParams={}，消息ID={}",messageParams, sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送反馈信息失败，messageParams={}，异常信息={}", messageParams, throwable.getMessage());
            }
        });

    }


}
