package com.insurance.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/5/7
 * @Version 1.0
 * @apiNote:信用卡加密数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreditCardEncryptionData {

    @Schema(description = "加密卡号")
    private String encryptedCardNumber;

    @Schema(description = "卡号尾号")
    private String endingNumber;

    @Schema(description = "加密密钥")
    private String secretKey;

    @Schema(description = "原始卡号")
    private String cardNumber;

    /**
     * 判断关键字段是否都不为空
     * @return true: 三个关键字段都不为空；false: 任意一个为空
     */
    public boolean isValid() {
        return isNotBlank(encryptedCardNumber)
                && isNotBlank(endingNumber)
                && isNotBlank(secretKey);
    }

    private boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }

}
