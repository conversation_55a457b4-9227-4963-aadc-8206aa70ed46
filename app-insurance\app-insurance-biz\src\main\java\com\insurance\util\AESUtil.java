package com.insurance.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Date;
import java.util.UUID;

public class AESUtil {
    private static final String AES_CIPHER = "AES/CBC/PKCS5Padding";

    public static byte[] encrypt(byte[] plainBytes, byte[] keyBytes, byte[] ivBytes) throws Exception {
        Cipher cipher = Cipher.getInstance(AES_CIPHER);
        SecretKeySpec key = new SecretKeySpec(keyBytes, 0, 16, "AES");
        IvParameterSpec iv = new IvParameterSpec(ivBytes);
        cipher.init(Cipher.ENCRYPT_MODE, key, iv);
        return cipher.doFinal(plainBytes);
    }

    public static byte[] decrypt(byte[] cipherBytes, byte[] keyBytes, byte[] ivBytes) throws Exception {
        Cipher cipher = Cipher.getInstance(AES_CIPHER);
        SecretKeySpec key = new SecretKeySpec(keyBytes, 0, 16, "AES");
        IvParameterSpec iv = new IvParameterSpec(ivBytes);
        cipher.init(Cipher.DECRYPT_MODE, key, iv);
        return cipher.doFinal(cipherBytes);
    }
}
