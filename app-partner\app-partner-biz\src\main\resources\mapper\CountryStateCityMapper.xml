<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.CountryStateCityMapper">

    <select id="getCountryCombox" resultType="com.partner.vo.base.CountryBaseCombox">
        SELECT id AS areaCountryId,name_chn AS areaCountryName FROM ais_institution_center.u_area_country
                           WHERE  FIND_IN_SET(13, public_level)
                 ORDER BY view_order DESC
    </select>

    <select id="getAllPubCountryCombox" resultType="com.partner.vo.base.CountryBaseCombox">
        SELECT id AS areaCountryId,name_chn AS areaCountryName FROM ais_institution_center.u_area_country
        WHERE  FIND_IN_SET(1, public_level) OR FIND_IN_SET(13, public_level)
        ORDER BY view_order DESC

    </select>


    <select id="getStateCombox" resultType="com.partner.vo.base.StateBaseCombox">
        SELECT id AS areaStateId,name_chn AS areaStateName  FROM ais_institution_center.u_area_state WHERE fk_area_country_id=#{areaCountryId}
                 ORDER BY view_order DESC
    </select>
    <select id="getCityCombox" resultType="com.partner.vo.base.CityBaseCombox">
        SELECT id AS areaCityId,name_chn AS areaCityName FROM ais_institution_center.u_area_city WHERE fk_area_state_id=#{areaStateId}
                 ORDER BY view_order  DESC
    </select>

    <select id="getEducationCombox" resultType="com.partner.vo.base.BaseCombox">
        SELECT id,type_name as name,type_name_chn as nameChn FROM ais_sale_center.u_student_education_level_type ORDER BY view_order DESC
    </select>
    <select id="getInstitutionList" resultType="com.partner.vo.base.BaseCombox">
        SELECT id,
               NAME,
               name_chn,
               concat(
                       IF
                       (is_active = 0, '【无效】', ''),
                       CASE
                           WHEN IFNULL(name_chn, '') = '' THEN
                               NAME
                           ELSE CONCAT(NAME, '（', name_chn, '）')
                           END
               ) AS fullName,is_active status
        FROM ais_institution_center.m_institution  where is_active=1
                        <if test="areaCountryId!=null ">
                            AND fk_area_country_id=#{areaCountryId}
                        </if>
                        <if test="areaStateId!=null ">
                            AND fk_area_state_id=#{areaStateId}
                        </if>
        ORDER BY is_active DESC, name ASC
    </select>
    <select id="getInstitutionCourseList" resultType="com.partner.vo.base.CourseCombox">

        SELECT id AS courseId,name AS courseName,name_chn AS courseNameChn

        FROM ais_institution_center.m_institution_course
        where is_active=1
        <if test="institutionId!=null ">
            AND fk_institution_id=#{institutionId}
        </if>
    </select>
    <select id="getInstitutionListSearch" resultType="com.partner.vo.base.BaseCombox">
        SELECT mInstitution.id,
               mInstitution.NAME,
               mInstitution.name_chn,
        concat(
        IF
        (mInstitution.is_active = 0, '【无效】', ''),
        CASE
        WHEN IFNULL(mInstitution.name_chn, '') = '' THEN
            mInstitution.NAME
        ELSE CONCAT(mInstitution.NAME, '（', mInstitution.name_chn, '）')
        END
        ) AS fullName,mInstitution.is_active status
        FROM ais_institution_center.m_institution mInstitution
        INNER JOIN ais_institution_center.r_institution_provider_institution AS rInstitutionProviderInstitution ON rInstitutionProviderInstitution.fk_institution_id = mInstitution.id
        INNER JOIN ais_institution_center.m_institution_provider AS mInstitutionProvider ON mInstitutionProvider.id = rInstitutionProviderInstitution.fk_institution_provider_id
        INNER JOIN ais_institution_center.r_institution_provider_company AS rInstitutionProviderCompany ON rInstitutionProviderCompany.fk_institution_provider_id = mInstitutionProvider.id

        where mInstitution.is_active=1 AND mInstitutionProvider.is_active=1
          AND rInstitutionProviderCompany.fk_company_id=#{fkCompanyId}
        AND mInstitution.fk_area_country_id=#{areaCountryId}

        <if test="institutionName!=null and institutionName!= ''">
        AND (mInstitution.name_chn like CONCAT('%', #{institutionName}, '%')
                OR mInstitution.name like CONCAT('%', #{institutionName}, '%')
                OR CONCAT(trim(mInstitution.name),trim(mInstitution.name_chn)) like CONCAT('%', trim(#{institutionName}), '%')
                OR CONCAT(trim(mInstitution.name), '（',trim(mInstitution.name_chn), '）') like CONCAT('%', trim(#{institutionName}), '%')
                OR mInstitution.id in(
                    select DISTINCT standard_term from ais_mail_center.u_synonym_reference WHERE table_name='m_institution' AND LOWER(INPUT_TERM)=LOWER(
                            #{institutionName}                                                                                                            )
                )
            )
        </if>
        GROUP BY mInstitution.id
        ORDER BY mInstitution.is_active DESC, mInstitution.name ASC

    </select>
    <select id="getAreaCode" resultType="com.partner.vo.combox.AreaCountryVo">
        SELECT
            GROUP_CONCAT( areaCodeValue ) as areaCodeValue,
            area_code
        FROM
            (
                SELECT
                    CONCAT( name_chn, ' +', area_code ) AS areaCodeValue,
                    area_code
                FROM
                    ais_institution_center.u_area_country
                WHERE
                    area_code IS NOT NULL
                  AND area_code != ''
                    <if test="areaCodeValue!=null and areaCodeValue!= ''">
                        AND (name_chn LIKE CONCAT('%', #{areaCodeValue}, '%')  OR area_code LIKE CONCAT('%', #{areaCodeValue}, '%') )
                    </if>
                ORDER BY
                    view_order DESC
            ) a
        GROUP BY
            a.area_code


    </select>



</mapper>