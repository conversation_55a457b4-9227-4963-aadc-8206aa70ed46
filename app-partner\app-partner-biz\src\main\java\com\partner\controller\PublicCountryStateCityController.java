package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.base.InstitutionCourseParamsDto;
import com.partner.dto.base.InstitutionSearchDto;
import com.partner.dto.student.AreaCountryDto;
import com.partner.enums.TypeEnum;
import com.partner.service.CountryStateCityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(description = "countryStateCity" , name = "小程序-(国家)(地区)(城市)(基础combox)-public接口" )
@RestController
@RequestMapping("/public/countryStateCity")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
@Inner(value = false)
public class PublicCountryStateCityController {
    private final CountryStateCityService countryStateCityService;


    @Operation(summary = "小程序-国家下拉框" , description = "小程序-国家下拉框" )
    @SysLog("小程序-国家下拉框" )
    @GetMapping("/getCountryCombox" )
    public R getCountryCombox(@ParameterObject CountryStateCityParamsDto dto){
        return R.ok(countryStateCityService.getCountryCombox(dto));
    }

    @Operation(summary = "小程序-所有公开国家下拉框" , description = "小程序-所有公开国家下拉框" )
    @SysLog("小程序-所有公开国家下拉框" )
    @GetMapping("/getAllPubCountryCombox" )
    public R getAllPubCountryCombox(@ParameterObject CountryStateCityParamsDto dto){
        return R.ok(countryStateCityService.getAllPubCountryCombox(dto));
    }


    @Operation(summary = "小程序-地区下拉框" , description = "小程序-地区下拉框" )
    @SysLog("小程序-地区下拉框" )
    @GetMapping("/getStateCombox" )
    public R getStateCombox(@ParameterObject CountryStateCityParamsDto dto){
        return R.ok(countryStateCityService.getStateCombox(dto));
    }

    @Operation(summary = "小程序-城市下拉框" , description = "小程序-城市下拉框" )
    @SysLog("小程序-城市下拉框" )
    @GetMapping("/getCityCombox" )
    public R getCityCombox(@ParameterObject CountryStateCityParamsDto dto){
        return R.ok(countryStateCityService.getCityCombox(dto));
    }


    @Operation(summary = "小程序-学历下拉框" , description = "小程序-学历下拉框" )
    @SysLog("小程序-学历下拉框" )
    @GetMapping("/getEducationCombox" )
    public R getEducationCombox(){
        return R.ok(countryStateCityService.getEducationCombox());
    }


    @Operation(summary = "小程序-在读/毕业学校名称" , description = "小程序-在读/毕业学校名称" )
    @SysLog("小程序-在读/毕业学校名称" )
    @GetMapping("/getInstitutionList" )
    public R getInstitutionList(CountryStateCityParamsDto dto){
        return R.ok(countryStateCityService.getInstitutionList(dto));
    }

    @Operation(summary = "小程序-课程" , description = "小程序-课程" )
    @SysLog("小程序-课程" )
    @GetMapping("/getInstitutionCourseList" )
    public R getInstitutionCourseList(@Valid InstitutionCourseParamsDto dto){
        return R.ok(countryStateCityService.getInstitutionCourseList(dto));
    }







    @Operation(summary = "学历情况备注-项目说明下拉", description = "")
    @GetMapping("getEducationProjectSelect")
    public R getEducationProjectSelect() {
        return R.ok(TypeEnum.getBaseCombox(TypeEnum.EDUCATION_PROJECT));
    }

    /**
     * 学历情况备注-学位情况
     *
     * @return
     * @
     */
    @Operation(summary = "学历情况备注-学位情况下拉", description = "")
    @GetMapping("getEducationDegreeSelect")
    public R getEducationDegreeSelect() {
        return R.ok(TypeEnum.getBaseCombox(TypeEnum.EDUCATION_DEGREE));
    }

    @Operation(summary ="高中成绩枚举下拉")
    @GetMapping("getGrades")
    public R getGrades() {
        return R.ok(TypeEnum.getBaseCombox(TypeEnum.HIGH_SCHOOL_GRADES));
    }

    @Operation(summary ="本科成绩枚举下拉")
    @GetMapping("getBachelorGrades")
    public R getBachelorGrades() {
        return R.ok(TypeEnum.getBaseCombox(TypeEnum.UNDERGRADUATE_ACHIEVEMENT));
    }

    @Operation(summary ="英语成绩枚举下拉")
    @GetMapping("getEnglishGrades")
    public R getEnglishGrades() {
        return R.ok(TypeEnum.getBaseCombox(TypeEnum.English_subtype));
    }




    @Operation(summary = "小程序-申请计划搜索-毕业学校名称" , description = "小程序-申请计划搜索-毕业学校名称" )
    @SysLog("小程序-申请计划搜索-毕业学校名称" )
    @PostMapping("/getInstitutionListSearch" )
    public R getInstitutionListSearch(@RequestBody InstitutionSearchDto dto){
        return R.ok(countryStateCityService.getInstitutionListSearch(dto));
    }

    @Operation(summary ="小程序-国家区号")
    @PostMapping("getAreaCode")
    public R getAreaCode(@RequestBody AreaCountryDto params) {
        return R.ok(countryStateCityService.getAreaCode(params));
    }


}
