package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_institution_commission_label")
public class InstitutionCommissionLabel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "公司Id")
    private Long fkCompanyId;

    @Schema(description = "学校主键Id")
    private Long fkInstitutionId;

    @Schema(description = "标签Id")
    private Long fkLabelId;
}
