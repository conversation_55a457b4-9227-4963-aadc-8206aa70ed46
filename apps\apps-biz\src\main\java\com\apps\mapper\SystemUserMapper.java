package com.apps.mapper;

import com.apps.api.entity.SystemUserEntity;
import com.apps.api.vo.InviteTemplateVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;


/**
 * <AUTHOR>
 */
@Mapper
public interface SystemUserMapper extends BaseMapper<SystemUserEntity> {

    /**
     * 查询最大id
     * @return
     */
    Long selectMaxId();

    /**
     * 获取邀请模板
     * @return
     */
    InviteTemplateVo getInviteTemplate();


    /**
     * 获取重置密码模板
     * @return
     */
    InviteTemplateVo getResetPasswordTemplate();

    /***
     * 根据类型获取模板
     * @param type 1:邀请模板 2:重置密码 3:新增伙伴用户
     * @return
     */
    InviteTemplateVo selectTemplateByType(Integer type);
}




