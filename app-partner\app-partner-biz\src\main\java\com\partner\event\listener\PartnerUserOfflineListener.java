package com.partner.event.listener;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.feign.RemoteAppsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.partner.config.RedisService;
import com.partner.constant.RedisConstant;
import com.partner.entity.MPartnerUserEntity;
import com.partner.event.PartnerUserOfflineEvent;
import com.partner.mapper.MPartnerUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/6/27
 * @Version 1.0
 * @apiNote:伙伴用户下线监听器
 */
@Service
@Slf4j
public class PartnerUserOfflineListener {

    @Autowired
    private RedisService redisService;
    @Autowired
    private RemoteAppsService appsService;
    @Autowired
    private MPartnerUserMapper partnerUserMapper;

    /**
     * 监听伙伴用户下线事件
     *
     * @param event
     */
    @Async("partnerUserOfflineTaskExecutor")
    @EventListener
    public void onPartnerUserOffline(PartnerUserOfflineEvent event) {
        log.info("监听到伙伴用户下线事件：{}", event);
        log.info("当前线程名称：{}", Thread.currentThread().getName());
        if (CollectionUtils.isEmpty(event.getPartnerUserIds())) {
            return;
        }

        List<MPartnerUserEntity> partnerUsers = partnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>()
                .in(MPartnerUserEntity::getId, event.getPartnerUserIds()));

        List<Long> systemUserIds = partnerUsers.stream().map(MPartnerUserEntity::getFkUserId).collect(Collectors.toList());

        for (Long systemUserId : systemUserIds) {
            //删除伙伴用户信息缓存
            log.info("删除伙伴用户信息缓存,系统用户ID：{}", systemUserId);
            redisService.del(RedisConstant.PARTNER_USERINFO_KEY_PREFIX + systemUserId);
        }

        if (event.getIsOffline()) {
            List<String> loginIds = partnerUsers.stream().map(MPartnerUserEntity::getFkUserLoginId).distinct().collect(Collectors.toList());
            log.info("调用apps下线用户 : {}", loginIds);
            appsService.userOfferLine(loginIds);
        }
    }
}
