package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.insurance.entity.CreditCard;
import com.insurance.entity.CreditCardStatement;
import com.insurance.entity.InsuranceOrder;


/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
public interface CreditCardStatementService extends IService<CreditCardStatement> {

    /**
     * 创建信用卡
     *
     * @param creditCard 信用卡
     * @param order      保险订单
     */
    void createCreditCardStatement(CreditCard creditCard,
                                   InsuranceOrder order,
                                   Integer businessType,
                                   String targetKey,
                                   Long targetId,
                                   Integer status);

    //todo 交易记录
    //todo 新增交易记录
    //todo 支出记录
}
