package com.partner.dto.offeritem;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "删除申请计划草稿箱子")
public class MAppStudentOfferItemDeleteDto {

    @Schema(description = "appOfferItemid")
    @NotNull(message = "草稿申请id不能为空!")
    private int id;

    @Schema(description = "申请草稿学生Id")
    @NotNull(message = "申请草稿学生Id不能空!")
    private Long fkAppStudentId;



}
