package com.partner.util;

import com.partner.constant.RedisConstant;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/24
 * @Version 1.0
 * @apiNote:Redis key
 */
public class RedisKeyUtils {

    /**
     * 构建用户信息缓存的 Redis Key
     */
    public static String buildUserInfoKey(Long tenantId, String platformCode, Long loginUserId, String uuid) {
        return tenantId + ":" + platformCode + ":" + loginUserId + "-" + uuid;
    }

    /**
     * 构建 partner 用户代理信息缓存 Key
     */
    public static String buildPartnerAgentKey(Long loginUserId, String uuid) {
        return RedisConstant.PARTNER_USERINFO_AGENT_KEY_PREFIX  + loginUserId + "-" + uuid;
    }
}
