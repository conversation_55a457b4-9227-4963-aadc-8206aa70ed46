package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_institution_provider_commission_plan_territory")
public class InstitutionProviderCommissionPlanTerritory extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学校提供商佣金方案Id")
    private Long fkInstitutionProviderCommissionPlanId;

    @Schema(description = "业务国家Id")
    private Long fkAreaCountryId;

    @Schema(description = "大区Id")
    private Long fkAreaRegionId;

    @Schema(description = "是否包括：1包括/2casebycase/3AU onshore/-1除外")
    private Integer isInclude;
}
