package com.payment.factory.impl;

import com.payment.config.PayConfig;
import com.payment.dto.common.PayRequest;
import com.payment.dto.wechat.WxOrderRequestDto;
import com.payment.entity.InsuranceOrderMpPayment;
import com.payment.enums.PayTypeEnum;
import com.payment.factory.strategy.PayStrategy;
import com.payment.service.MpPaymentService;
import com.payment.service.wx.WxPayService;
import com.payment.vo.PayResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote: 微信JSAPI/小程序下单支付策略
 */
@Component("WX_JSAPI")
@Slf4j
public class WXJSAPIPayStrategy implements PayStrategy {

    @Autowired
    private WxPayService payService;
    @Autowired
    private MpPaymentService mpPaymentService;

    @Override
    public PayResponse unifiedOrder(PayRequest request, PayConfig config) {
        log.info("=======================微信JSAPI/小程序下单支付");
        WxOrderRequestDto orderRequestDto = WxOrderRequestDto.builder()
                .outTradeNo(request.getOrderNo())
                .amount(1)
                .openid(request.getOpenId())
                .description(request.getDescription())
                .notifyTopic(request.getNotifyTopic())
                .build();
        Map<String, String> wxOrderMap = payService.createWxOrder(orderRequestDto, config);

        //创建系统订单支付记录
//        mpPaymentService.createMpPayment(InsuranceOrderMpPayment.createMpPaymentByPayRequest(request));
        return PayResponse.success(PayTypeEnum.WX_JSAPI.getCode(), request.getOrderNo(), wxOrderMap);
    }
}
