package com.insurance.event.listener;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.insurance.dto.email.RecipientInfo;
import com.insurance.entity.CreditCard;
import com.insurance.entity.CreditCardReminder;
import com.insurance.entity.InsuranceOrder;
import com.insurance.enums.EmailTemplateType;
import com.insurance.enums.NotifyMessageTypeEnum;
import com.insurance.enums.RemindTypeEnum;
import com.insurance.event.SendNotifyMessageEvent;
import com.insurance.mapper.CreditCardMapper;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.service.CreditCardReminderService;
import com.insurance.service.NotifyService;
import com.insurance.util.DateFormatUtils;
import com.insurance.util.EncryptionUtils;
import com.insurance.util.SecureEncryptUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/6/27
 * @Version 1.0
 * @apiNote:发送信用卡通知信息
 */
@Service
@Slf4j
public class SendNotifyMessageEventListener {

    @Autowired
    private CreditCardReminderService cardReminderService;
    @Autowired
    private CreditCardMapper creditCardMapper;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private EncryptionUtils encryptionUtils;
    @Autowired
    private InsuranceOrderMapper orderMapper;

    /**
     * 监听伙伴用户下线事件
     *
     * @param event
     */
    @Async("sendNotifyMessageTaskExecutor")
    @EventListener
    public void onSendNotifyMessage(SendNotifyMessageEvent event) {
        log.info("监听到发送信用卡通知信息：{}", JSONObject.toJSONString(event));

        CreditCardReminder reminder = cardReminderService.getCreditCardReminderByCreditCard(event.getCreditCardId());
        if (Objects.isNull(reminder) || CollectionUtils.isEmpty(reminder.getCreditCardReminderNotifierList())) {
            log.info("未设置通知信息，信用卡ID: {}", event.getCreditCardId());
            return;
        }

        NotifyMessageTypeEnum typeEnum = NotifyMessageTypeEnum.getEnumByCode(event.getMessageType());
        if (Objects.isNull(typeEnum)) return;

        handleReminder(reminder, typeEnum, event);
    }

    @SneakyThrows
    private void handleReminder(CreditCardReminder reminder, NotifyMessageTypeEnum typeEnum, SendNotifyMessageEvent event) {

        log.info("开始处理提醒类型 [{}], 配置ID: {}", typeEnum.getMsg(), reminder.getId());
        // 通知未启用
        Integer enabled = typeEnum.getEnabledGetter().apply(reminder);
        if (Objects.isNull(enabled) || enabled == 0) {
            log.warn("未开启提醒类型 [{}], 配置ID: {}", typeEnum.getMsg(), reminder.getId());
            return;
        }
        CreditCard creditCard = creditCardMapper.selectById(reminder.getFkCreditCardId());
        if (Objects.isNull(creditCard)) {
            log.error("未找到信用卡信息，信用卡ID: {}", reminder.getFkCreditCardId());
            return;
        }

        //邮件参数填充

        String secret = encryptionUtils.getSecret();
        String cardNum = SecureEncryptUtil.decrypt(creditCard.getCardNum(), secret);
        //卡尾号
        String lastDigits = desensitize(cardNum);
        //日期
        String date = DateFormatUtils.formatCurrentDateToChinese();
        //订单号
        String orderNo = event.getOrderNo();
        //卡额度
        BigDecimal quotaLimit = reminder.getQuotaLimit();
        //支付时间
        String payDate = DateFormatUtils.formatDateTime(event.getPayTime());
        //保单交易金额
        String tradeAmount;
        if (StringUtils.isNotBlank(orderNo)) {
            InsuranceOrder insuranceOrder = orderMapper.selectByOrderNum(orderNo);
            if (Objects.nonNull(insuranceOrder)) {
                tradeAmount = insuranceOrder.getFkCurrencyTypeNum() + insuranceOrder.getInsuranceAmount();
            } else {
                tradeAmount = Strings.EMPTY;
                log.error("发送交易提醒异常,未找到保单信息，订单号: {}", orderNo);
            }
        } else {
            tradeAmount = Strings.EMPTY;
        }

        // 额度提醒特殊判断
        if (typeEnum == NotifyMessageTypeEnum.QUOTA_REMIND) {
            if (creditCard == null || creditCard.getCurrentAmount().compareTo(reminder.getQuotaLimit()) > 0) {
                log.info("额度未超出提醒额度，不发送邮件提醒：当前额度:{},限制额度:{}", creditCard.getCurrentAmount(), reminder.getQuotaLimit());
                return;
            }
        }

        String remindTypeStr = typeEnum.getTypeGetter().apply(reminder);
        if (StringUtils.isBlank(remindTypeStr)) {
            log.warn("未设置提醒类型 [{}], 配置ID: {}", typeEnum.getMsg(), reminder.getId());
            return;
        }

        List<RecipientInfo> emails = reminder.getCreditCardReminderNotifierList().stream()
                .map(notifier -> {
                    if (StringUtils.isBlank(notifier.getEmail())) {
                        return null;
                    }

                    Map<String, String> params = new HashMap<>();
                    params.put("lastDigits", lastDigits);
                    params.put("date", date);
                    params.put("orderNo", orderNo);
                    params.put("limitAmount", quotaLimit.toString());
                    params.put("payDate", payDate);
                    params.put("businessTypeName", "保险订单");
                    params.put("tradeAmount", tradeAmount);
                    params.put("name", notifier.getName());

                    return RecipientInfo.builder()
                            .contract(notifier.getEmail())
                            .params(params)
                            .build();
                }).filter(Objects::nonNull).collect(Collectors.toList());

        List<RecipientInfo> phones = reminder.getCreditCardReminderNotifierList().stream()
                .map(notifier -> {
                    if (StringUtils.isBlank(notifier.getEmail())) {
                        return null;
                    }

                    Map<String, String> params = new HashMap<>();
                    params.put("lastDigits", lastDigits);
                    params.put("date", date);
                    params.put("orderNo", orderNo);
                    params.put("limitAmount", quotaLimit.toString());
                    params.put("payDate", payDate);
                    params.put("businessTypeName", "保险订单");
                    params.put("tradeAmount", tradeAmount);
                    params.put("name", notifier.getName());

                    return RecipientInfo.builder()
                            .contract(notifier.getMobile())
                            .params(params)
                            .build();
                }).filter(Objects::nonNull).collect(Collectors.toList());

        for (String type : remindTypeStr.split(",")) {
            int code = Integer.parseInt(type.trim());
            if (code == RemindTypeEnum.EMAIL.getCode()) {
                log.info("开始发送邮件提醒：{}", typeEnum.getCode());
                notifyService.sendEmailNotify(EmailTemplateType.getEnumByCode(typeEnum.getCode()), emails);
            } else if (code == RemindTypeEnum.SMS.getCode()) {
                notifyService.sendSmsNotify(phones);
            }
        }
    }

    public static String desensitize(String input) {
        if (input == null || input.length() <= 4) {
            return input;
        }
        int length = input.length();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length - 4; i++) {
            sb.append("*");
        }
        sb.append(input.substring(length - 4));
        return sb.toString();
    }


}
