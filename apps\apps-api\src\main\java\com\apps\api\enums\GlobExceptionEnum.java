package com.apps.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 全局异常信息枚举
 */

@Getter
@AllArgsConstructor
public enum GlobExceptionEnum {
    SYSTEM_ERROR(500, "系统错误"),
    SYS_ACCOUNT_NOT_EXISTS(40001, "账户不存在"),
    SYS_ACCOUNT_PASSWORD_ERROR(40002, "密码错误"),
    SYS_USER_NOT_EXISTS(40003, "用户不存在"),
    VERIFICATION_CODE_NOT_GAIN(40004, "未获取验证码"),
    VERIFICATION_CODE_ERROR(40005, "验证码错误"),
    MP_SECRET_NOT_EXISTS(40006, "未找到对应的appSecret"),
    MP_GET_OPENID_ERROR(40007, "微信获取openid失败"),
    MP_USER_DECRYPT_ERROR(40008, "解密用户信息失败"),
    SYS_CREATE_USER_ERROR(40009, "创建用户失败"),
    SYS_CREATE_USER_LOGIN_INFO_ERROR(40010, "创建用户登录信息失败"),
    SYS_USER_LOCK(40011, "用户已锁定"),
    SYS_USER_DELETE(40012, "用户已删除"),
    MP_GET_USER_PHONE_ERROR(40013, "获取用户手机号失败"),
    MP_GET_ACCESS_TOKEN_ERROR(40014, "获取accessToken失败"),
    SMS_CODE_SEND_ERROR(40015, "验证码发送失败"),
    SYS_ROLE_NAME_EXIST(40016, "角色名称已存在"),
    SYS_ROLE_CODE_EXIST(40017, "角色code已存在"),
    SYS_ROLE_BIND_USER(40018, "改角色已经绑定用户"),
    REMOTE_SERVICE_NOT_FIND(40019, "未找到第三方用户信息接口服务"),
    REMOTE_SERVICE_NO_RESULT(40020, "调用第三方服务失败"),
    PLATFORM_NOT_EXIST(40021, "平台不存在"),
    EMAIL_ALREADY_REGISTERED(40022, "邮箱已注册"),
    PLATFORM_CODE_ERROR(40023, "平台Code错误"),
    ROLE_NOT_EXIST(40025, "角色不存在"),
    MOBILE_ALREADY_REGISTERED(40026, "手机已注册"),
    UPDATE_PASSWORD_CONFIRM_ERROR(40027, "两次输入的密码不一致"),
    UPDATE_PASSWORD_OLD_PASSWORD_ERROR(40028, "原密码不正确"),
    ;

    private Integer code;

    private String msg;

    public static String getEnumByCode(Integer code) {
        for (GlobExceptionEnum value : GlobExceptionEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

    public static GlobExceptionEnum getGlobExceptionEnum(Integer code) {
        for (GlobExceptionEnum value : GlobExceptionEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
