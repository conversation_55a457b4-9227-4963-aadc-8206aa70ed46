package com.partner.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 请求头工具类
 */
@Slf4j
public class RequestHeaderUtils {

    /**
     * 从请求头中获取 uuid
     *
     * @param userId 当前登录用户ID（可用于记录日志）
     * @return uuid 值（不能为空，否则抛异常）
     */
    public static String getUuidOrThrow(Long userId) {
        String uuid = null;
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            uuid = request.getHeader("uuid");
        }
        if (StringUtils.isBlank(uuid)) {
            log.error("获取请求头 uuid 为空, loginUserId: {}", userId);
            throw new RuntimeException("获取请求头uuid失败");
        }
        log.info("===========>获取请求头 uuid: {}", uuid);
        return uuid;
    }

    /**
     * 获取 uuid（可为空）
     *
     * @return uuid 或 null
     */
    public static String getUuidNullable() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            return request.getHeader("uuid");
        }
        return null;
    }
}
