package com.apps.api.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseUserInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户Id")
    private Long userId;

    @Schema(description = "租户Id")
    private Long tenantId;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "代理Id")
    private Long agentId;
}
