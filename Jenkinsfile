pipeline {
     agent {
    kubernetes {
        inheritFrom "jenkins-agent:v8"
        yaml """
kind: Pod
metadata:
  name: jenkins-slave
spec:
  affinity:
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchExpressions:
              - key: jenkins
                operator: In
                values: [slave]
          topologyKey: kubernetes.io/hostname
  containers:
  - name: jnlp
    image: "************/library/jenkins-agent:v8"
    resources:
      limits:
        memory: "2048Mi"
        cpu: "2"
      requests:
        memory: "2048Mi"
        cpu: "1"
    volumeMounts:
      - name: docker-cmd
        mountPath: /usr/bin/docker
      - name: docker-sock
        mountPath: /var/run/docker.sock
      - name: maven-cache
        mountPath: /root/.m2
  volumes:
    - name: docker-cmd
      hostPath:
        path: /usr/bin/docker
    - name: docker-sock
      hostPath:
        path: /var/run/docker.sock
    - name: maven-cache
      persistentVolumeClaim:
        claimName: jenkins-k8s-data-pvc
  env:
    - name: JAVA_OPTS
      value: "-Xmx2048m -XX:+UseG1GC"
"""
        }

      }

    parameters {
        choice(name: 'service_name', choices: ['app-coupon','app-insurance','app-partner','app-payment','apps', 'app-pmp', 'app-job','auth','gateway', 'upms'], description: '请选择服务')
    }

    environment {
        GITHUB_CREDENTIAL_ID = 'fzhharborid'
        KUBECONFIG_CREDENTIAL_ID = '0691e53c-7065-4756-a465-e0791002c55c'
        REGISTRY = '************'
        DOCKERHUB_NAMESPACE = 'zxl_docker_9394'
    }

    stages {
        stage ('checkout scm') {
            steps {
                script {
                    switch(service_name) {
                        case "app-coupon":
                            projectName = "app-coupon/app-coupon-biz"
                            sh 'echo build app-coupon'
                            break
                        case "app-insurance":
                            projectName = "app-insurance/app-insurance-biz"
                            sh 'echo build app-insurance'
                            break
                        case "app-partner":
                            projectName = "app-partner/app-partner-biz"
                            sh 'echo build app-partner'
                            break
                        case "app-payment":
                            projectName = "app-payment/app-payment-biz"
                            sh 'echo build app-payment'
                            break
                        case "app-pmp":
                            projectName = "app-pmp/app-pmp-biz"
                            sh 'echo build app-pmp'
                            break
                        case "app-job":
                            projectName = "app-job"
                            sh 'echo build app-job'
                            break
                        case "apps":
                            projectName = "apps/apps-biz"
                            sh 'echo build apps'
                            break
                        case "gateway":
                            projectName = "gateway"
                            sh 'echo build gateway'
                            break
                        case "auth":
                            projectName = "auth"
                            sh 'echo build auth'
                            break
                        case "upms":
                            projectName = "upms/upms-biz"
                            sh 'echo build upms'
                            break
                    }
                }
            }
        }

        stage ('build & push') {
            steps {
              script {
                checkout(scm)
                def commitHash = env.GIT_COMMIT
                def now = new Date()
                def gitHash=commitHash[0..7]
                def VERSION="$gitHash-${now.format('yyyyMMdd-HHmmss')}"
                echo "Latest commit hash: ${commitHash}-${now.format('yyyyMMdd-HH:mm:ss')}"
                sh " mvn clean -U "
//                 sh " rm -rf /root/.m2/repository/com/fzh/*"
                sh " mvn -T 4 clean package -pl $projectName -am -e -U -Dmaven.test.skip=true "
                sh " cd ${pwd()}/$projectName && docker build -f ./Dockerfile . -t $REGISTRY/hti-java-app/$projectName-$Branch:$VERSION "
                sh " docker login -u admin -p Fzhharbor2024 $REGISTRY"
                sh " docker push $REGISTRY/hti-java-app/$projectName-$Branch:$VERSION"
              }
            }
        }
    }
}
