package com.pmp.util;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.pmp.vo.commission.AgentCommissionListVo;
import com.pmp.vo.commission.TerritoryInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/7/8
 * @Version 1.0
 * @apiNote:方案明细合并
 */
@Slf4j
public class TerritoryInfoMerger {

    /**
     * 合并 TerritoryInfoVo 列表（按业务规则）
     *
     * @param inputList 原始列表（每个 TerritoryInfoVo 中可能有重复的 AgentCommissionInfo）
     * @return 合并去重后的列表
     */
    public static List<TerritoryInfoVo> merge(List<TerritoryInfoVo> inputList) {
        if (CollectionUtils.isEmpty(inputList)) {
            return Collections.emptyList();
        }

        // 使用 Map 分组（key: 6 个字段拼接，value: TerritoryInfoVo）
        Map<String, TerritoryInfoVo> mergedMap = new LinkedHashMap<>();

        for (TerritoryInfoVo item : inputList) {
            String key = buildGroupKey(item);

            // 查找是否已有相同分组
            TerritoryInfoVo existing = mergedMap.get(key);
            if (existing == null) {
                // 初始化一个新的 list（避免引用同一个对象）
                TerritoryInfoVo newItem = BeanUtil.copyProperties(item, TerritoryInfoVo.class);
                if (CollectionUtils.isNotEmpty(item.getAgentCommissionInfoList())){
                    newItem.setAgentCommissionInfoList(new ArrayList<>(item.getAgentCommissionInfoList()));
                    mergedMap.put(key, newItem);
                }
            } else {
                // 合并佣金列表，并去重
                List<AgentCommissionListVo.AgentCommissionInfo> mergedList = new ArrayList<>(existing.getAgentCommissionInfoList());
                mergedList.addAll(item.getAgentCommissionInfoList());
                existing.setAgentCommissionInfoList(removeDuplicateCommission(mergedList));
            }
        }
        return new ArrayList<>(mergedMap.values());
    }

    /**
     * 构造分组 key（6个字段）
     */
    private static String buildGroupKey(TerritoryInfoVo vo) {
        String rawKey = safeJson(vo.getTerritories()) + "|" +
                defaultStr(vo.getTerritory()) + "|" +
                defaultStr(vo.getCourse()) + "|" +
                defaultStr(vo.getRemark());
        log.info("buildGroupKey: {}", rawKey);
        // 返回 MD5 之后的 Key（32位）
        return DigestUtils.md5DigestAsHex(rawKey.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 去重相同 AgentCommissionInfo（按 7 个字段判断相等）
     */
    private static List<AgentCommissionListVo.AgentCommissionInfo> removeDuplicateCommission(List<AgentCommissionListVo.AgentCommissionInfo> list) {
        return list.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                TerritoryInfoMerger::buildCommissionKey,
                                Function.identity(),
                                (existing, replace) -> existing,
                                LinkedHashMap::new
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    /**
     * 构造佣金去重 key（7 个字段）
     * 等级ID+等级名称+佣金+佣金单位+后续佣金+课程+后续佣金单位
     */
    private static String buildCommissionKey(AgentCommissionListVo.AgentCommissionInfo info) {
        return info.getLevelId() + "|" +
                defaultStr(info.getCustomName()) + "|" +
                safeStr(info.getCommission()) + "|" +
                defaultStr(info.getCommissionUnit()) + "|" +
                safeStr(info.getFollowCommission()) + "|" +
                defaultStr(info.getCourse()) + "|" +
                defaultStr(info.getFollowCommissionUnit());
    }

    private static String defaultStr(String str) {
        return str == null ? "" : str.trim()
                .replaceAll("\\s+", "")  // 去除所有空格
                .replaceAll("　", "")    // 去除全角空格
                .toLowerCase();          // 忽略大小写
    }

    private static String safeStr(BigDecimal value) {
        return value == null ? "0" : value.stripTrailingZeros().toPlainString();
    }

    private static String safeJson(Object obj) {
        return obj == null ? "[]" : JSON.toJSONString(obj);
    }
}
