package com.partner.vo.student;

import com.partner.entity.MStudentEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MStudentDetailTmpVo extends MStudentEntity {

    @Schema(description = "跟进人")
    private String followName;

    private String partnerUserName;

    private String partnerUserNameEn;

    private String nationalityName;
    private String areaCountryNamePassport;

    @Schema(description = "学历等级类型(国内)")
    private String domesticEducationName;

    @Schema(description = "草稿箱的学生id")
    private Long  fkAppStudentId;
    @Schema(description = "0非partner新增学生,1 可以加申")
    private int  partnerType;






    public String getFollowName() {
        if(partnerUserNameEn!=null && partnerUserNameEn.length()>0){
            followName=partnerUserNameEn;
        } else if (partnerUserName!=null && partnerUserName.length()>0) {
            followName=partnerUserName;
        }
        return followName;
    }

    public int getPartnerType() {
        if(fkAppStudentId!=null){
            partnerType=1;
        }else {
            partnerType=0;
        }
        return partnerType;
    }







}
