package com.apps.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 日期工具类,此工具类未提供的功能，建议使用joda-time来实现
 *
 * <AUTHOR>
 * @version 1.0
 */
@SuppressWarnings("all")
public class DateUtil {

    /**
     * 一天的秒数
     */
    public final static long ONE_DAY_SECONDS = 86400;

    /**
     * 一天的毫秒数
     */
    public final static long ONE_DAY_MILL_SECONDS = 86400000;


    /**
     * 日期格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String EXCEL_DATE_FORMAT = "yydc_MM_dd_HH_mm_ss";

    /**
     * 日期格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式：yyyyMMdd
     */
    public static final String SHORT_FORMAT = "yyyyMMdd";

    /**
     * 日期格式：yyyyMMddHHmmss
     */
    public static final String LONG_FORMAT = "yyyyMMddHHmmss";

    /**
     * 日期格式：yyyyMMddHHmmssSSS
     */
    public static final String LONG_SSS_FORMAT = "yyyyMMddHHmmssSSS";

    /**
     * 日期格式：yyMMddHHmmssSSS
     */
    public static final String LONG_MINI_SSS_FORMAT = "yyMMddHHmmssSSS";


    /**
     * 日期格式：yyyy-MM-dd
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static final String DATE_FORMAT_2 = "yyyy/MM/dd";

    public static final String DATE_FORMAT_2_ALL = "yyyy/MM/dd HH:mm:ss";

    /**
     * "yyyy-MM-dd"
     */
    public static final String defaultDatePattern = DATE_FORMAT;

    /**
     * 日期格式：HHmmss
     */
    public static final String TIME_FORMAT = "HHmmss";

    public static final String TIME_FORMAT_COLON = "HH:mm:ss";

    /**
     * 日期格式：yyyy年MM月dd日
     */
    public static final String CHINESE_FORMAT = "yyyy年MM月dd日";

    /**
     * 日期格式：yyyy-MM-dd HH:mm
     */
    public static final String NO_SECOND_FORMAT = "yyyy-MM-dd HH:mm";

    public static final String CHINESE_SHORT_FORMAT_TIME = "HH小时mm分ss秒";

    /**
     * 默认英文格式
     */
    public static final String DEFUALT_EN_FORTMAT = "MMM d,yyyy hh:mm:ss aa";

    /**
     * 星期数组
     */
    public static final String dayNames[] = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};

    /**
     * 日期格式：2020-02-15T00:00:00
     */
    public static final String DEFAULT_UTC_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

    public static final String DEFAULT_TIME = "HH:mm";

    /**
     * "yyyy-MM-dd"
     */
    public static SimpleDateFormat dateFormatter = new SimpleDateFormat(defaultDatePattern);

    /**
     * 获取当前时间
     *
     * @return yyyy-MM-dd HH:mm:ss
     */
    public static String getCurrentDate() {
        return dateToString(DEFAULT_DATE_FORMAT, new Date());
    }

    /**
     * 获取当前时段
     *
     * @return yyyy-MM-dd HH:mm:ss
     */
    public static String getCurrentTime() {
        return dateToString(DEFAULT_TIME, new Date());
    }

    /**
     * 获取当前时间
     *
     * @return yyyy-MM-dd HH:mm:ss
     */
    public static String getToday() {
        return dateToString(DATE_FORMAT, new Date());
    }

    /**
     * 获取当前时间
     *
     * @return yydc_MM_dd_HH_mm_ss
     */
    public static String getExcelDate() {
        return dateToString(EXCEL_DATE_FORMAT, new Date());
    }

    public static long getTime() {
        return Long.valueOf(new Date().getTime() / 100);
    }

    /**
     * 获取当前UTC时间
     *
     * @return
     */
    public static String getUTCTime(String format) {
        Calendar cal = Calendar.getInstance();
        //获得时区和GMT-0的时间差,偏移量
        int offset = cal.get(Calendar.ZONE_OFFSET);
        //获得夏令时时差
        int dstoff = cal.get(Calendar.DST_OFFSET);
        cal.add(Calendar.MILLISECOND, -(offset + dstoff));
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(cal.getTime());
    }

    /**
     * 根据传入得本地时间获得这个对应得UTC时间
     *
     * @param localDate
     * @param format
     * @return
     */
    public static String getUTCTimeByLocalTime(String localDate, String format) {
        Calendar cal = Calendar.getInstance();
        //获得时区和GMT-0 的时间差,偏移量
        int offset = cal.get(Calendar.ZONE_OFFSET);
        //获得夏令时时差
        int dstoff = cal.get(Calendar.DST_OFFSET);
        DateFormat sdf = new SimpleDateFormat(format);
        try {
            Date parse = sdf.parse(localDate);
            Date date = new Date(parse.getTime() - (offset + dstoff));//获得当前是UTC时区的时间毫秒值
            return sdf.format(date);
        } catch (ParseException e) {

        }
        return "";
    }

    /**
     * 按照指定格式获取当前时间
     *
     * @param format 例：yyyy-MM-dd HH:mm:ss
     * @return String
     */
    public static String getCurrentDate(String format) {
        return dateToString(format, new Date());
    }

    public static Date getCurrentDateTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        int hours = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hours);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        return calendar.getTime();
    }

    public static int getDateHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 根据传递的格式，获取指定日期之前或者之后多少天的日期
     *
     * @param date   指定的时间
     * @param offset 日期偏移量，正数表示延后，负数表示天前
     * @param format 格式,当format为null时默认格式：yyyy-MM-dd HH:mm:ss
     * @return String
     */
    public static String getDateByOffset(Date date, int offset, String format) {
        if (format == null || "".equals(format)) {
            format = DEFAULT_DATE_FORMAT;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, offset);
        return dateToString(format, calendar.getTime());
    }

    /**
     * 获取指定日期之前或者之后多少天的日期
     *
     * @param date   指定的时间
     * @param offset 日期偏移量，正数表示延后，负数表示天前
     * @return Date
     */
    public static Date getDateByOffset(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, offset);
        return calendar.getTime();
    }

    public static Date getDayOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当天开始时间 如 2016-01-18 00:00:00
     *
     * @return Date
     */
    public static Date getDayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定某天开始时间
     *
     * @return Date
     */
    public static Date getDayStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定某天开始时间
     *
     * @return Date
     */
    public static Date getDayStart(String strDate) {
        Date date = stringToDate(strDate, DATE_FORMAT);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当天结束时间 如 2016-01-18 23:59:59
     *
     * @return Date
     */
    public static Date getDayEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获取指定某天结束时间 如 2016-01-18 23:59:59
     *
     * @return Date
     */
    public static Date getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获得当前年份
     *
     * @return int
     */
    public static int getYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获得指定日期的年份
     *
     * @param date 日期
     * @return int
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获得当前月份
     *
     * @return int
     */
    public static int getMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获得指定日期的月份，1-12
     *
     * @param date 日期
     * @return int
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获得当前天
     *
     * @return int
     */
    public static int getDay() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获得指定日期的天
     *
     * @param date
     * @return
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获得星期
     *
     * @return String
     */
    public static String getWeek() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (dayOfWeek < 0) {
            dayOfWeek = 0;
        }
        return dayNames[dayOfWeek];
    }

    /**
     * 获得指定日期的星期
     *
     * @return String
     */
    public static String getWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (dayOfWeek < 0) {
            dayOfWeek = 0;
        }
        return dayNames[dayOfWeek];
    }

    /**
     * 计算指定时间几天之后的时间
     *
     * @param date 日期
     * @param days 天数
     * @return Date
     */
    public static Date addDays(String date, long days) {
        return addSeconds(stringToDate(date, DATE_FORMAT), days * ONE_DAY_SECONDS);
    }

    /**
     * 计算指定时间几天之后的时间
     *
     * @param date 日期
     * @param days 天数
     * @return Date
     */
    public static Date addDays(Date date, long days) {
        return addSeconds(date, days * ONE_DAY_SECONDS);
    }

    /**
     * 计算指定时间几小时之后的时间
     *
     * @param date  日期
     * @param hours 小时
     * @return Date
     */
    public static Date addHours(Date date, long hours) {
        return addMinutes(date, hours * 60);
    }

    /**
     * 计算指定时间几分钟之后的时间
     *
     * @param date    日期
     * @param minutes 分钟
     * @return Date
     */
    public static Date addMinutes(Date date, long minutes) {
        return addSeconds(date, minutes * 60);
    }

    /**
     * 计算指定时间几秒之后的时间
     *
     * @param date 日期
     * @param secs 秒
     * @return Date
     */
    public static Date addSeconds(Date date, long secs) {
        return new Date(date.getTime() + (secs * 1000));
    }

    /**
     * 取得两个日期间隔秒数（日期1-日期2）
     *
     * @param one 日期1
     * @param two 日期2
     * @return long 间隔秒数
     */
    public static long getDiffSeconds(Date one, Date two) {
        return getDiffMilliSeconds(one, two) / 1000;
    }

    /**
     * 取得两个日期间隔毫秒数（日期1-日期2）
     *
     * @param one 日期1
     * @param two 日期2
     * @return long 间隔毫秒数
     */
    public static long getDiffMilliSeconds(Date one, Date two) {
        Calendar sysDate = new GregorianCalendar();
        sysDate.setTime(one);
        Calendar failDate = new GregorianCalendar();
        failDate.setTime(two);
        return sysDate.getTimeInMillis() - failDate.getTimeInMillis();
    }

    /**
     * 取得两个日期间隔毫秒数（日期1-日期2）
     *
     * @param one 日期1
     * @param two 日期2
     * @return long 间隔毫秒数
     */
    public static long getDiffMilliSeconds2(Date one, Date two) {
        return one.getTime() - two.getTime();
    }

    /**
     * 取得两个日期间隔分钟数（日期1-日期2）
     *
     * @param one 日期1
     * @param two 日期2
     * @return long 间隔分钟数
     */
    public static long getDiffMinutes(Date one, Date two) {
        Calendar sysDate = new GregorianCalendar();
        sysDate.setTime(one);
        Calendar failDate = new GregorianCalendar();
        failDate.setTime(two);
        return (sysDate.getTimeInMillis() - failDate.getTimeInMillis()) / (60 * 1000);
    }

    /**
     * 取得两个日期的间隔天数（日期1-日期2）
     *
     * @param one 日期1
     * @param two 日期2
     * @return long 间隔天数
     */
    public static long getDiffDays(Date one, Date two) {
        Calendar sysDate = new GregorianCalendar();
        sysDate.setTime(one);
        Calendar failDate = new GregorianCalendar();
        failDate.setTime(two);
        return (sysDate.getTimeInMillis() - failDate.getTimeInMillis()) / ONE_DAY_MILL_SECONDS;
    }

    public static long getDiffDays(String one, String two) {
        Date d1 = stringToDate(one, "yyyy-MM-dd");
        Date d2 = stringToDate(two, "yyyy-MM-dd");
        return getDiffDays(d1, d2);
    }

    /**
     * 取得两个时间的相隔的时间
     *
     * @param begin_date 开始时间
     * @param end_date   结束时间
     * @return Map
     */
    public static Map<String, Integer> getDiffDate(Date begin_date, Date end_date) {
        int hour = 0;
        int minute = 0;
        int day = 0;
        long total_minute = 0;
        StringBuffer sb = new StringBuffer();
        try {
            total_minute = (end_date.getTime() - begin_date.getTime()) / (1000 * 60);

            hour = (int) total_minute / 60;
            day = (int) (hour / 24);
            hour = hour - day * 24;
            minute = (int) total_minute % 60;
        } catch (Exception e) {
            System.out.println("传入的时间格式不符合规定");
        }
        Map<String, Integer> map = new HashMap<String, Integer>();
        map.put("day", day);
        map.put("hour", hour);
        map.put("minute", minute);
        return map;
    }

    /**
     * 获取某个日期的当月第一天
     *
     * @param date 日期
     * @return Date
     */
    public static Date getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取某个日期的当月最后一天
     *
     * @param date 日期
     * @return
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        return calendar.getTime();
    }

    /**
     * 获取某年某月的天数
     *
     * @param year  年
     * @param month 月
     * @return int
     */
    public static int getDaysOfMonth(String year, String month) {
        int days = 0;
        if (month.equals("1") || month.equals("3") || month.equals("5") || month.equals("7") || month.equals("8")
                || month.equals("10") || month.equals("12")) {
            days = 31;
        } else if (month.equals("4") || month.equals("6") || month.equals("9") || month.equals("11")) {
            days = 30;
        } else {
            if ((Integer.parseInt(year) % 4 == 0 && Integer.parseInt(year) % 100 != 0)) {
                days = 29;
            } else {
                days = 28;
            }
        }
        return days;
    }

    /**
     * 将日期和时间转换成整体
     *
     * @param date 日期
     * @param time 时间
     * @return Date
     */
    public static Date composeDate(Date date, Date time) {
        if (date == null || time == null) {
            return null;
        }
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(time);
        Calendar c3 = Calendar.getInstance();
        c3.set(c1.get(Calendar.YEAR), c1.get(Calendar.MONTH), c1.get(Calendar.DATE), c2.get(Calendar.HOUR_OF_DAY),
                c2.get(Calendar.MINUTE), c2.get(Calendar.SECOND));
        return c3.getTime();
    }

    /**
     * 时间分段 比如：2014-12-12 10:00:00 ～ 2014-12-12 14:00:00 分成两段就是 2014-12-12
     * 10：00：00 ～ 2014-12-12 12：00：00 和2014-12-12 12：00：00 ～ 2014-12-12 14：00：00
     *
     * @param start  起始日期
     * @param end    结束日期
     * @param pieces 分成几段
     * @return Date[]
     */
    public static Date[] getDatePieces(Date start, Date end, int pieces) {
        Long sl = start.getTime();
        Long el = end.getTime();

        Long diff = el - sl;

        Long segment = diff / pieces;

        Date[] dateArray = new Date[pieces + 1];

        for (int i = 1; i <= pieces + 1; i++) {
            dateArray[i - 1] = new Date(sl + (i - 1) * segment);
        }

        // 校正最后结束日期的误差，可能会出现偏差，比如14:00:00 ,会变成13:59:59之类的
        dateArray[pieces] = end;

        return dateArray;
    }

    /**
     * 把符合日期格式的字符串转换为日期类型
     *
     * @param dateStr 日期字符串
     * @param format  格式
     * @return date
     */
    public static Date stringToDate(String dateStr, String format) {
        Date date = null;
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            sdf.setLenient(false);
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            date = null;
        }
        return date;
    }

    /**
     * 把符合日期格式的字符串转换为日期类型
     *
     * @param dateStr 日期字符串
     * @param format  格式
     * @return date
     */
    public static Date stringToDateEn(String dateStr, String format) {
        Date date = null;
        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.ENGLISH);
        try {
            sdf.setLenient(false);
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            date = null;
        }
        return date;
    }

    public static String longToDateStr(long msel, String format) {
        Date date = new Date(msel);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date);
    }

    public static String longTimeToDay(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;
        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        Long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;
        StringBuffer sb = new StringBuffer();
        /*if (day > 0) {
            sb.append(day + "天");
        }*/
        if (hour > 0) {
            sb.append(day * 24 + hour + "小时");
        }
        if (minute > 0) {
            sb.append(minute + "分");
        } else {
            sb.append("00分");
        }
        if (second > 0) {
            sb.append(second + "秒");
        } else {
            sb.append("00秒");
        }
        /*if (milliSecond > 0) {
            sb.append(milliSecond + "毫秒");
        }*/
        return sb.toString();
    }

    /**
     * 把符合日期格式的字符串转换为日期类型
     *
     * @param dateStr 日期字符串
     * @param format  格式
     * @param pos     用来标明解析开始位 ParsePosition pp = new ParsePosition( 0 );
     * @return date
     */
    public static Date stringToDate(String dateStr, String format, ParsePosition pos) {
        Date date = null;
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            formater.setLenient(false);
            date = formater.parse(dateStr, pos);
        } catch (Exception e) {
            date = null;
        }
        return date;
    }

    /**
     * 格式化日期
     *
     * @param format 格式
     * @param date   日期
     * @return String 对应格式的字符型日期
     */
    public static String dateToString(String format, Date date) {
        String result = "";
        if (date == null) {
            return result;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            result = sdf.format(date);
        } catch (Exception e) {
        }
        return result;
    }

    /**
     * 格式化日期-LocalDateTime
     *
     * @param format 格式
     * @param date   日期
     * @return String 对应格式的字符型日期
     */
    public static String dateToString(String format, LocalDateTime date) {
        String result = "";
        if (date == null) {
            return result;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            result = formatter.format(date);
        } catch (Exception e) {
        }
        return result;
    }

    /**
     * 格式化日期
     *
     * @param format 格式
     * @param date   日期
     * @return String 对应格式的字符型日期
     */
    public static String dateToStringEn(String format, Date date) {
        String result = "";
        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.ENGLISH);
        try {
            result = sdf.format(date);
        } catch (Exception e) {
            // e.printStackTrace();
        }
        return result;
    }

    /**
     * 格式化日期
     *
     * @param format 格式，当format为null时默认格式：yyyy-MM-dd HH:mm:ss
     * @param obj    对象
     * @return 对应格式的字符型日期
     */
    public static String dateToString(String format, Object obj) {
        String result = "";
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            result = sdf.format(obj);
        } catch (Exception e) {
        }
        return result;
    }

    /**
     * 判断哪个日期在前，日期一在日期二之前，返回true,否则返回false
     *
     * @param date1 日期一
     * @param date2 日期二
     * @return boolean
     */
    public static boolean isBefore(Date date1, Date date2) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(date1);
        Calendar c2 = Calendar.getInstance();
        c2.setTime(date2);
        if (c1.before(c2)) {
            return true;
        }
        return false;
    }

    /**
     * 判断输入的字符串是否为合法的小时
     *
     * @param hourStr
     * @return true/false
     */
    public static boolean isValidHour(String hourStr) {
        if (!StringUtils.isEmpty(hourStr)) {
            int hour = new Integer(hourStr).intValue();
            if ((hour >= 0) && (hour <= 23)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断输入的字符串是否为合法的分或秒
     *
     * @param minuteStr
     * @return true/false
     */
    public static boolean isValidMinuteOrSecond(String str) {
        if (!StringUtils.isEmpty(str)) {
            int hour = new Integer(str).intValue();
            if ((hour >= 0) && (hour <= 59)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是闰年
     *
     * @param year 年份
     * @return boolean
     */
    public static boolean isLeapYear(int year) {
        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.isLeapYear(year);
    }

    /**
     * 将Date类转换为XMLGregorianCalendar
     *
     * @param date
     * @return
     */
    public static XMLGregorianCalendar dateToXmlDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        DatatypeFactory dtf = null;
        try {
            dtf = DatatypeFactory.newInstance();
        } catch (DatatypeConfigurationException e) {
        }
        XMLGregorianCalendar dateType = dtf.newXMLGregorianCalendar();
        dateType.setYear(cal.get(Calendar.YEAR));
        //由于Calendar.MONTH取值范围为0~11,需要加1
        dateType.setMonth(cal.get(Calendar.MONTH) + 1);
        dateType.setDay(cal.get(Calendar.DAY_OF_MONTH));
        dateType.setHour(cal.get(Calendar.HOUR_OF_DAY));
        dateType.setMinute(cal.get(Calendar.MINUTE));
        dateType.setSecond(cal.get(Calendar.SECOND));
        return dateType;
    }

    /**
     * 将XMLGregorianCalendar转换为Date
     *
     * @param cal
     * @return
     */
    public static Date xmlDate2Date(XMLGregorianCalendar cal) {
        return cal.toGregorianCalendar().getTime();
    }


    public static Date parse(Date strDate, String pattern) {
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        try {
            return df.parse(df.format(strDate));
        } catch (ParseException e) {
        }
        return null;
    }

    /**
     * 使用预设格式将字符串转为Date
     */
    public static Date parse(String strDate) {
        return parse(strDate, getDatePattern());
    }

    /**
     * 使用参数Format将字符串转为Date
     */
    public static Date parse(String strDate, String pattern) {
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        try {
            return df.parse(strDate);
        } catch (ParseException e) {
        }
        return null;
    }


    /**
     * 使用预设Format格式化Date成字符串
     */
    public static String format(Date date) {
        return format(date, defaultDatePattern);
    }


    /**
     * 使用参数Format格式化Date成字符串
     */
    public static String format(Date date, String pattern) {
        String returnValue = "";

        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            returnValue = df.format(date);
        }

        return (returnValue);
    }

    /**
     * 获得默认的 date pattern
     */
    public static String getDatePattern() {
        return defaultDatePattern;
    }

    /**
     * 格式化传进去的日期 格式 ：yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static String dateFormatter(Date date) {
        if (date != null) {
            return dateFormatter.format(date);
        } else {
            return dateFormatter.format(new Date());
        }
    }

    /**
     * 和系统时间相比，是否昨天
     *
     * @param date
     * @return
     */
    public static boolean isYesterday(Date date) {

        if (date == null) {
            return false;
        }
        Calendar yest = Calendar.getInstance();
        yest.add(Calendar.DAY_OF_MONTH, -1);
        Calendar compar = Calendar.getInstance();
        compar.setTime(date);

        return yest.get(Calendar.YEAR) == compar.get(Calendar.YEAR)
                && yest.get(Calendar.MONTH) == compar.get(Calendar.MONTH)
                && yest.get(Calendar.DAY_OF_MONTH) == compar.get(Calendar.DAY_OF_MONTH);
    }

    /***
     * 开始时间---结束时间(list集合)
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<Date> getStartEndDateList(Date startDate, Date endDate) {
        if (startDate == null) {
            return Collections.emptyList();
        }
        if (endDate == null) {
            return Stream.of(startDate).collect(Collectors.toList());
        }
        List<Date> startEndDateList = new ArrayList<>();
        //差几天
        long diffDay = DateUtil.getDiffDays(endDate, startDate);
        if (diffDay == 0) {
            return Stream.of(startDate).collect(Collectors.toList());
        }
        for (long day = 0L; day <= diffDay; day++) {
            startEndDateList.add(addDays(startDate, day));
        }
        return startEndDateList;
    }

    /***
     * 开始时间---结束时间(list集合)
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> getStartEndDateList(Date startDate, Date endDate, String format) {
        if (StringUtils.isEmpty(format)) {
            //throw new BusinessException(BusinessExceptionEnum.PARAMETER_NOT_EMPTY.getCode(), "format " + BusinessExceptionEnum.PARAMETER_NOT_EMPTY.getDesc());
        }
        List<Date> atartEndDateList = getStartEndDateList(startDate, endDate);
        if (CollectionUtils.isEmpty(atartEndDateList)) {
            return Collections.emptyList();
        }
        List<String> atartEndDateStrList = new ArrayList<>();
        for (Date date : atartEndDateList) {
            atartEndDateStrList.add(dateToString(format, date));
        }
        return atartEndDateStrList;
    }

    /**
     * 获取指定时间戳时间下一秒年月日十分秒时间戳
     *
     * @return
     */
    public static long getCurrentNextSecondMillis(long timeMillis) {
        return (timeMillis / 1000 + 1) * 1000;
    }

    /**
     * 获取指定时间戳下一分年月日十分秒时间戳
     *
     * @return
     */
    public static long getCurrentNextMinuteMillis(long timeMillis) {
        return (timeMillis / 60000 + 1) * 60000;
    }

    /**
     * 获取指定时间戳与下一秒之间的毫秒差
     *
     * @return
     */
    public static long getDiffCurrentNextSecondMillis(long timeMillis) {
        return (timeMillis / 1000 + 1) * 1000 - timeMillis;
    }

    /**
     * 获取指定时间戳与下一分之间的毫秒差
     *
     * @return
     */
    public static long getDiffCurrentNextMinuteMillis(long timeMillis) {
        return (timeMillis / 60000 + 1) * 60000 - timeMillis;
    }

    public static List<Date> getDaysBetweenDates(String startDate, String endDate) {
        return getDaysBetweenDates(stringToDate(startDate, DATE_FORMAT), stringToDate(endDate, DATE_FORMAT));
    }

    public static List<Date> getDaysBetweenDates(Date sd, Date ed) {
        List<Date> dates = new ArrayList<>();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(sd);

        while (calendar.getTime().before(ed)) {
            Date result = calendar.getTime();
            dates.add(result);
            calendar.add(Calendar.DATE, 1);
        }
        return dates;
    }

    public static List<String> getDaysOfStringBetweenDates(Date startDate, Date endDate, String format) {
        List<String> dates = new ArrayList<>();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(startDate);

        while (calendar.getTime().before(endDate)) {
            String result = dateToString(format, calendar.getTime());
            dates.add(result);
            calendar.add(Calendar.DATE, 1);
        }
        return dates;
    }

    public static List<String> getDaysOfStringBetweenDates(String startDate, String endDate, String format) {
        Date sd = stringToDate(startDate, DATE_FORMAT);
        Date ed = stringToDate(endDate, DATE_FORMAT);
        return getDaysOfStringBetweenDates(sd, ed, format);
//        List<String> dates = new ArrayList<>();
//        Calendar calendar = new GregorianCalendar();
//        calendar.setTime(sd);
//
//        while (calendar.getTime().before(ed)) {
//            String result = dateToString(format, calendar.getTime());
//            dates.add(result);
//            calendar.add(Calendar.DATE, 1);
//        }
//        return dates;
    }

    public static Timestamp stringToTimestamp(String dateStr) {
        return Timestamp.valueOf(dateStr);
    }

    public static String timestampToString(Timestamp date, String format) {
        DateFormat f = new SimpleDateFormat(format);
        return f.format(date);
    }

    /**
     * 日期格式字符串转换成时间戳
     *
     * @param date   字符串日期
     * @param format 如：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Long date2TimeStamp(String date_str, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(date_str).getTime() / 1000;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0L;
    }

    public static Long date2TimeStamp(Date date_str) {
        try {
            return date_str.getTime() / 1000;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0L;
    }

    /**
     * 时间戳转换成日期时间格式字符串
     *
     * @param seconds 精确到秒的字符串
     * @param format
     * @return
     */
    public static String timeStampToDate(Long seconds, String format) {
        if (seconds == null || seconds.equals("null")) {
            return "";
        }
        if (format == null || format.isEmpty()) {
            format = "yyyy-MM-dd HH:mm:ss";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(Long.valueOf(seconds + "000")));
    }

    /**
     * 将短时间格式字符串转换为时间 yyyy-MM-dd
     *
     * @param strDate
     * @return
     */
    public static Date strToDate(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }


    /**
     * 判断当前时间是否在设置的dark mode时间段内
     *
     * @param date1: 开始时间
     * @param date2: 结束时间
     */
    public static boolean isBelongPeriodTime(String beginTime, String endTime) {
        SimpleDateFormat df = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        Date currentTime = new Date(System.currentTimeMillis());
        Date startTimeDate;
        Date endTimeDate;
        Calendar date = Calendar.getInstance();
        Calendar begin = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        //这里是时间段的起止都在同一天的情况，只需要判断当前时间是否在这个时间段内即可
        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    public static String strToDateFormat(String datetime) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(LONG_FORMAT);
        LocalDateTime ldt = LocalDateTime.parse(datetime, dtf);
        DateTimeFormatter fa = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        String datetime2 = ldt.format(fa);
        return datetime2;
    }

    public static String getBeforeTime(String format, Integer min) {
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(Calendar.MINUTE, min);// min 分钟之前的时间
        Date beforeD = beforeTime.getTime();
        String before = new SimpleDateFormat(format).format(beforeD);  // 前min分钟时间

        return before;
    }

    /**
     * 获取过去某一天
     *
     * @return
     */
    public static String getLastOneDay(Integer amount) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, amount);
        Date d = c.getTime();
        String day = format.format(d);
        return day;
    }

    /**
     * 获取过去某一月
     *
     * @return
     */
    public static String getLastOneMonth(Integer amount) {
        SimpleDateFormat format = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, amount);
        Date d = c.getTime();
        String day = format.format(d);
        return day;
    }

    /**
     * 获取过去某一年
     *
     * @return
     */
    public static String getLastOneyear(Integer amount) {
        SimpleDateFormat format = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.YEAR, amount);
        Date d = c.getTime();
        String day = format.format(d);
        return day;
    }

    /**
     * 获取当前日期是星期几<br>
     *
     * @param date
     * @return 当前日期是星期几
     */
    public static Integer getWeekOfDate() {
        //String[] weekDays = { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return w;
    }

    /**
     * 获取当前日期是星期几<br>
     *
     * @param date
     * @return 当前日期是星期几
     */
    public static Integer getWeekNum() {
        int[] weekDays = {7, 1, 2, 3, 4, 5, 6};

        Calendar cal = Calendar.getInstance();

        cal.setTime(new Date());

        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;

        if (w < 0)

            w = 0;

        return weekDays[w];
    }

    /**
     * 获取当前日期是星期几<br>
     *
     * @param date
     * @return 当前日期是星期几
     */
    public static Integer getDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        int d = cal.get(Calendar.DAY_OF_MONTH) - 1;
        if (d < 0)
            d = 0;
        return d;
    }

    /**
     * 根据传递的格式，获取指定日期之前或者之后多少天的日期
     *
     * @param date   指定的时间
     * @param offset 日期偏移量，正数表示延后，负数表示天前
     * @param format 格式,当format为null时默认格式：yyyy-MM-dd HH:mm:ss
     * @return String
     */
    public static String getDateBySecOffset(Date date, int offset, String format) {
        if (format == null || "".equals(format)) {
            format = DEFAULT_DATE_FORMAT;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, offset);
        return dateToString(format, calendar.getTime());
    }

    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime   当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public static boolean isEffectiveDate(Date startTime, Date endTime) {
        Date nowTime = new Date();
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }
}
