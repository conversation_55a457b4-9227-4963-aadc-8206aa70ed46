package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-18 14:36:55
 */

@Data
@TableName("m_payable_plan")
public class MPayablePlanEntity extends Model<MPayablePlanEntity>{

  @Schema(description = "应付计划Id")
  private Long id;
 

  @Schema(description = "公司Id")
  private Long fkCompanyId;
 

  @Schema(description = "应付类型关键字，枚举，如：m_student_offer_item")
  private String fkTypeKey;
 

  @Schema(description = "应付类型对应记录Id，如：m_student_offer_item.id")
  private Long fkTypeTargetId;
 

  @Schema(description = "应收计划Id")
  private Long fkReceivablePlanId;
 

  @Schema(description = "摘要")
  private String summary;
 

  @Schema(description = "币种编号")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "学费金额")
  private BigDecimal tuitionAmount;
 

  @Schema(description = "费率%(代理)")
  private BigDecimal commissionRate;
 

  @Schema(description = "代理分成比率%")
  private BigDecimal splitRate;
 

  @Schema(description = "佣金金额(代理)")
  private BigDecimal commissionAmount;
 

  @Schema(description = "定额金额")
  private BigDecimal fixedAmount;
 

  @Schema(description = "其他金额")
  private BigDecimal bonusAmount;
 

  @Schema(description = "应付金额")
  private BigDecimal payableAmount;
 

  @Schema(description = "计划付款时间")
  private LocalDateTime payablePlanDate;
 

  @Schema(description = "是否预付，0否/1是")
  private Boolean isPayInAdvance;
 

  @Schema(description = "预付百分比：50, 100")
  private Integer payInAdvancePercent;
 

  @Schema(description = "状态：0关闭/1打开/2完成")
  private Integer status;
 

  @Schema(description = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
  private Integer statusSettlement;
 

  @Schema(description = "旧数据财务id(gea)")
  private String idGeaFinance;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
