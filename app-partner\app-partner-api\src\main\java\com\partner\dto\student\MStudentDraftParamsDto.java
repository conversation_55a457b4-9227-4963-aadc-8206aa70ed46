package com.partner.dto.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "学生草稿查询参数")
public class MStudentDraftParamsDto   {

    @Schema(description = "partner用户Id")
    private Long partnerUserId;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "查询列表类型不能为空 0草稿列表/1审核列表/-1 审核失败/100审核日志")
    @NotNull(message = "查询列表类型不能为空")
    private Integer searchType;

    @Schema(description = "角色编码")
    private String roleCode;



}
