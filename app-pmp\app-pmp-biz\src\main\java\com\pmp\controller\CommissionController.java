
package com.pmp.controller;


import com.common.core.util.R;
import com.pmp.dto.CommissionDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.mapper.MajorLevelCustomMapper;
import com.pmp.service.AgentCommissionMajorLevelCustomService;
import com.pmp.service.AgentCommissionPlanService;
import com.pmp.util.UserInfoUtils;
import com.pmp.vo.commission.*;
import com.pmp.vo.institution.CountryVo;
import com.pmp.vo.partner.PartnerUserVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/commission/")
@Tag(description = "代理佣金管理", name = "代理佣金管理")
public class CommissionController {

    @Autowired
    private AgentCommissionPlanService commissionPlanService;
    @Autowired
    private MajorLevelCustomMapper majorLevelCustomMapper;
    @Autowired
    private AgentCommissionMajorLevelCustomService levelCustomService;
    @Autowired
    private UserInfoUtils userInfoUtils;


    @Operation(summary = "测试", description = "测试1")
    @PostMapping("/testCommission")
    public R<List<Long>> test(@RequestBody InstitutionDto dateDto) {
        return R.ok(commissionPlanService.getPlanIds(null,null,dateDto.getStartDate()));
    }


    @Operation(summary = "课程等级列表", description = "课程等级列表(可以查询全部以及通用和非通用)")
    @GetMapping("/selectMajorLevel")
    public R<List<MajorLevelVo>> selectMajorLevel(@Schema(description = "1:通用课程等级;0:自定义课程等级;不传查询全部课程等级") Integer isGeneral) {
        return R.ok(majorLevelCustomMapper.selectMajorLevel(isGeneral));
    }

    @Operation(summary = "课程等级树", description = "课程等级树")
    @GetMapping("/getMajorLevelTree")
    public R<List<MajorLevelTreeVo>> getMajorLevelTree() {
        return R.ok(levelCustomService.getMajorLevelTree());
    }

//    @Operation(summary = "根据学校获取佣金计划", description = "根据学校获取佣金计划")
//    @PostMapping("/commissionDetail")
//    public R<AgentCommissionDetailVo> commissionDetail(@RequestBody @Valid CommissionDto dto) {
//        return R.ok(commissionPlanService.commissionDetail(dto));
//    }
//
//    @Operation(summary = "根据学校获取佣金计划-多方案", description = "根据学校获取佣金计划-多方案")
//    @PostMapping("/getCommissionDetail")
//    public R<List<CommissionDetail>> getCommissionDetail(@RequestBody @Valid CommissionDto dto) {
//        return R.ok(commissionPlanService.getCommissionDetail(dto,null,null));
//    }

    @Operation(summary = "根据学校获取佣金计划-合并方案", description = "根据学校获取佣金计划-合并方案")
    @PostMapping("/getMergeCommission")
    public R<MergeCommissionVo> getMergeCommission(@RequestBody @Valid CommissionDto dto) {
        PartnerUserVo partnerUser = userInfoUtils.getPartnerUser();
        return R.ok(commissionPlanService.getMergeCommission(dto,partnerUser.getAgentId(),partnerUser.getCompanyId(),null));
    }

    @Operation(summary = "高佣学校数量", description = "高佣学校数量")
    @PostMapping("/highCommissionCount")
    public R<Integer> highCommissionCount(@RequestBody DateDto dateDto) {
        return R.ok(commissionPlanService.highCommissionCount(dateDto));
    }

}
