package com.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发版信息子项查询参数
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@Schema(description = "发版信息子项查询参数")
public class MReleaseInfoItemParamsDto {

    @Schema(description = "发版信息Id")
    private Long fkReleaseInfoId;

    @Schema(description = "标题(模糊查询)")
    private String title;

    @Schema(description = "权限类型(0全局/1角色权限)")
    private Integer permissionType;

    @Schema(description = "系统资源Key(支持模糊查询)")
    private String fkResourceKey;

    @Schema(description = "创建用户")
    private String gmtCreateUser;

    @Schema(description = "排序字段(默认按创建时间倒序)")
    private String orderBy = "gmt_create DESC";

    @Schema(description = "页码", example = "1")
    private Long current = 1L;

    @Schema(description = "每页大小", example = "10")
    private Long size = 10L;
}