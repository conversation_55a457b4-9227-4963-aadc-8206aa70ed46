package com.partner.dto.finance;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
@Schema(description = "批量下载对账单")
public class DownloadMSettlementBillParamsDto extends UserInfoParams {
    @Schema(description = "申请开始时间")
    private LocalDate gmtCreateStart;

    @Schema(description = "申请结束时间")
    private LocalDate gmtCreateEnd;
}
