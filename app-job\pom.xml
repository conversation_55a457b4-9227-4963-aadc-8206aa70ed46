<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fzh</groupId>
        <artifactId>fzh</artifactId>
        <version>3.8.1</version>
    </parent>

    <artifactId>app-job</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>upms-api</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>app-insurance-api</artifactId>
            <version>3.8.1</version>
        </dependency>
        <!--文件管理-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-oss</artifactId>
        </dependency>
        <!--feign 调用-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-security</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>
        <!-- orm 模块-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-datasource</artifactId>
            <version>3.8.2</version>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- 阿里云短信下发 -->
        <dependency>
            <groupId>io.springboot.sms</groupId>
            <artifactId>aliyun-sms-spring-boot-starter</artifactId>
        </dependency>
        <!--xss 过滤-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-xss</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.20</version>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>app-partner-api</artifactId>
            <version>3.8.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>apps-api</artifactId>
            <version>3.8.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.1</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.4.3</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>