package com.fzh.job.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.mapper.MStudentOfferItemAgentConfirmMapper;
import com.fzh.job.mqconfig.SendConfimCommissionMessageProducer;
import com.fzh.job.mqconfig.SendConfimProducer;
import com.fzh.job.service.SendConfimCommissionService;
import com.partner.mqmessage.MQCommissionMessage;
import com.partner.util.BeanCopyUtils;
import com.partner.vo.job.SystemUserJobVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SendConfimCommissionServiceImpl implements SendConfimCommissionService {
    @Resource
    MStudentOfferItemAgentConfirmMapper mStudentOfferItemAgentConfirmMapper;

    @Resource
    SendConfimCommissionMessageProducer sendConfimCommissionMessageProducer;

    @Resource
    SendConfimProducer sendConfimProducer;

    @Override
    public Boolean sendMQCommissionMessage(BaseParamDto paramDto) {
        //需要提醒的人员邮件
        List<SystemUserJobVo> allSendMessageInfo=
                mStudentOfferItemAgentConfirmMapper.getAllAgentIdSendMessage();

        if(ObjectUtils.isNotEmpty(allSendMessageInfo)){
            for(SystemUserJobVo systemUser:allSendMessageInfo){
                MQCommissionMessage messageNum=BeanCopyUtils.objClone(systemUser,MQCommissionMessage::new);
                sendConfimCommissionMessageProducer.sendCommissionMessage(messageNum);
            }
        }
        return true;
    }

    @Override
    public Boolean sendCommissionMessage(MQCommissionMessage messageParams) {
        sendConfimProducer.sendCommissionMessage(messageParams);
        return true;
    }


}
