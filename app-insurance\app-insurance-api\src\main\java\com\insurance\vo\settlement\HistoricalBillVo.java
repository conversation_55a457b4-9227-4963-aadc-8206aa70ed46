package com.insurance.vo.settlement;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/18
 * @Version 1.0
 * @apiNote:历史结算账单
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HistoricalBillVo {

    @Schema(description = "结算单ID")
    private Long id;

    @Schema(description = "结算金额")
    private BigDecimal totalSettlementAmount;

    @Schema(description = "结算币种")
    private String currencyTypeNum;

    @Schema(description = "结算订单数")
    private Integer orderCount;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间/申请时间")
    private Date gmtCreate;

    @Schema(description = "审核状态:2待审核;3-结算中;4-已完成")
    private Integer approvalStatus;

}
