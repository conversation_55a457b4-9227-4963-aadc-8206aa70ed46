package com.coupon.dto;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class CouponTakenDto {
    private Long couponTypeId;
    private String mobile;
    private String nickName;
    private String roleCode;
    private String title;
    private String subTitle;
    private String description;
    private String studentName;
    private String studentEmail;
    private String studentNeeaId;
    private String examDate;
    private String price;
    private Boolean isActive;
    private Integer recommendedType;
    private LocalDateTime validPeriodStart;
    private LocalDateTime validPeriodEnd;
    private boolean isLapse;
    private Long sort;
    private long size = 10;
    private long current = 1;
}
