package com.apps.service.logic;

import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/1/15  17:18
 * @Version 1.0
 */
@Service
public class NacosService {

    @Autowired
    private NamingService namingService;

    @SneakyThrows
    public Instance getServiceInstance(String serviceName) {
        // 获取服务实例列表
        List<Instance> instances = namingService.getAllInstances(serviceName);
        if (!instances.isEmpty()) {
            // 返回第一个实例的 IP 地址
            return instances.get(0);
        } else {
            return null;
        }
    }
}
