package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-21 17:18:33
 */

@Data
@TableName("r_live_partner_user_appointment")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_live_partner_user_appointment ")
public class RLivePartnerUserAppointmentEntity extends Model<RLivePartnerUserAppointmentEntity>{

  @Schema(description = "直播用户预约Id")
  private Long id;
 

  @Schema(description = "直播Id")
  private Long fkLiveId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "状态：0未发送预约信息/1发送成功/2发送失败")
  private Integer status;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
