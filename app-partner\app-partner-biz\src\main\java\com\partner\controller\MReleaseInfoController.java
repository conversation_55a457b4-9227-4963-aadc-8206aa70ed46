package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.service.MReleaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mReleaseInfo")
@Tag(description = "发版信息管理", name = "发版信息管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class MReleaseInfoController {

    private final MReleaseInfoService mReleaseInfoService;

    /**
     * 获取已发布的发版信息列表
     * 
     * @return 统一响应结果，包含已发布的发版信息列表
     */
    @Operation(summary = "查询发版信息", description = "查询发版信息列表")
    @SysLog("查询发版信息")
    @GetMapping("/findReleasedList")
    public R findReleasedList() {
        return R.ok(mReleaseInfoService.findReleasedList());
    }

} 