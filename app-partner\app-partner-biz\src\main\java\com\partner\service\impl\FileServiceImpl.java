package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.partner.entity.MFilePartnerEntity;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.MFilePartnerMapper;
import com.partner.service.FileService;
import com.partner.service.ITencentCloudService;
import com.partner.util.AppendixUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
public class FileServiceImpl implements FileService {
    @Resource
    private  ITencentCloudService tencentCloudService;
    @Resource
    private MFilePartnerMapper filePartnerMapper;


    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    //文件存储
    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;


    @Override
    public List<MFilePartnerEntity> uploadAppendix(MultipartFile[] files, Boolean isPub,Boolean isPartnerPath) {
        List<MFilePartnerEntity> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = AppendixUtils.getFilePath(file);
            String bucketName = fileBucketName;//私有桶
            if(isPub){
                fileurl = AppendixUtils.getFileHtiPath(file);//公开桶
                if(isPartnerPath){
                    fileurl = AppendixUtils.getPartHtiPath(file);
                }


                bucketName=imageBucketName;//公开桶
            }
            MFilePartnerEntity fileVo = saveFileInfo(bucketName, file, fileurl, getAppendixPerfix(),isPub);
            datas.add(fileVo);

        }
        return datas;
    }

    @Override
    public List<MFilePartnerEntity> uploadAppendix(MultipartFile[] files, Boolean isPub, Boolean isPartnerPath, String fileName) {
        List<MFilePartnerEntity> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = AppendixUtils.getFilePath(file);
            String bucketName = fileBucketName;//私有桶
            if(isPub){
                fileurl = AppendixUtils.getFileHtiPath(file);//公开桶
                if(isPartnerPath){
                    fileurl = AppendixUtils.getPartHtiPath(file);
                }


                bucketName=imageBucketName;//公开桶
            }
            MFilePartnerEntity fileVo = saveFileInfoAndFileName(bucketName, file, fileurl, getAppendixPerfix(),isPub,fileName);
            datas.add(fileVo);

        }
        return datas;
    }


    @Override
    public MFilePartnerEntity uploadAppendix(MultipartFile file, Boolean isPub){
        String fileurl = AppendixUtils.getFilePath(file);
        String bucketName = fileBucketName;//私有桶
        if(isPub){
            fileurl = AppendixUtils.getFileHtiPath(file);//公开桶
            bucketName=imageBucketName;//公开桶
        }
        MFilePartnerEntity filePartner = saveFileInfo(bucketName, file, fileurl, getAppendixPerfix(),isPub);

        return filePartner;
    }

    @Override
    public MFilePartnerEntity uploadAisfileAppendix(MultipartFile file, Boolean isPub) {

        String fileurl = AppendixUtils.getFilePath(file);
        String bucketName = fileBucketName;//私有桶
        if(isPub){
            fileurl = AppendixUtils.getFileHtiPath(file);//公开桶
            bucketName=imageBucketName;//公开桶
        }
        MFilePartnerEntity filePartner = saveAisfileFileInfo(bucketName, file, fileurl, getAppendixPerfix(),isPub);

        return filePartner;
    }


    @Override
    public MFilePartnerEntity uploadFileAppendix(File file, Boolean isPub){
        String fileurl = AppendixUtils.getFilePath(file);
        String bucketName = fileBucketName;//私有桶
        if(isPub){
            fileurl = AppendixUtils.getFileHtiPath(file);//公开桶
            bucketName=imageBucketName;//公开桶
        }
        MFilePartnerEntity filePartner = saveFileInfo(bucketName, file, fileurl, getAppendixPerfix(),isPub);

        return filePartner;
    }




    /**
     * 保存文件
     * @param bucketName
     * @param file
     * @param fileurl
     * @param
     * @param perfix
     * @param isPub true:公开桶 false:私密桶
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MFilePartnerEntity saveFileInfo(String bucketName, MultipartFile file, String fileurl,String perfix, boolean isPub) {
        MFilePartnerEntity filePartnerEntity = null;
        if (ObjectUtil.isNotEmpty(perfix)) {
            fileurl = perfix + fileurl;
        }
        String filename = file.getOriginalFilename();
        int i = filename.lastIndexOf(".");
        int j = fileurl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String targetFileName = fileurl.substring(j + 1, fileurl.length());
        String ossPath = subString(fileurl);//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg


        filePartnerEntity = new MFilePartnerEntity();
        filePartnerEntity.setFilePath(fileurl);
        filePartnerEntity.setFileNameOrc(filename);
        filePartnerEntity.setFileTypeOrc(substring);
        filePartnerEntity.setFileName(targetFileName);
        filePartnerEntity.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));

        tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
        filePartnerEntity.setFileKey(ossPath);
        filePartnerMapper.insertSelective(filePartnerEntity);
        return filePartnerEntity;
    }


    @Transactional(rollbackFor = Exception.class)
    public MFilePartnerEntity saveAisfileFileInfo(String bucketName, MultipartFile file, String fileurl,String perfix, boolean isPub) {
        MFilePartnerEntity filePartnerEntity = null;
        if (ObjectUtil.isNotEmpty(perfix)) {
            fileurl = perfix + fileurl;
        }
        String filename = file.getOriginalFilename();
        int i = filename.lastIndexOf(".");
        int j = fileurl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String targetFileName = fileurl.substring(j + 1, fileurl.length());
        String ossPath = subString(fileurl);//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg


        filePartnerEntity = new MFilePartnerEntity();
        filePartnerEntity.setFilePath(fileurl);
        filePartnerEntity.setFileNameOrc(filename);
        filePartnerEntity.setFileTypeOrc(substring);
        filePartnerEntity.setFileName(targetFileName);
        filePartnerEntity.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));

        tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
        filePartnerEntity.setFileKey(ossPath);
        filePartnerMapper.insertAisFileSelective(filePartnerEntity);
        return filePartnerEntity;
    }


    @Transactional(rollbackFor = Exception.class)
    public MFilePartnerEntity saveFileInfoAndFileName(String bucketName, MultipartFile file, String fileurl,String perfix, boolean isPub,String fileNameYuanshi) {
        MFilePartnerEntity filePartnerEntity = null;
        if (ObjectUtil.isNotEmpty(perfix)) {
            fileurl = perfix + fileurl;
        }
        String filename = file.getOriginalFilename();
        if(StringUtil.isNotEmpty(fileNameYuanshi) && fileNameYuanshi.indexOf(".")!=-1){
            filename=fileNameYuanshi;
        }

        int i = filename.lastIndexOf(".");
        int j = fileurl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String targetFileName = fileurl.substring(j + 1, fileurl.length());
        String ossPath = subString(fileurl);//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg


        filePartnerEntity = new MFilePartnerEntity();
        filePartnerEntity.setFilePath(fileurl);
        filePartnerEntity.setFileNameOrc(filename);
        filePartnerEntity.setFileTypeOrc(substring);
        filePartnerEntity.setFileName(targetFileName);
        filePartnerEntity.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));

        tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
        filePartnerEntity.setFileKey(ossPath);
        filePartnerMapper.insertSelective(filePartnerEntity);
        return filePartnerEntity;
    }


    @Transactional(rollbackFor = Exception.class)
    public MFilePartnerEntity saveFileInfo(String bucketName, File file, String fileurl,String perfix, boolean isPub) {
        MFilePartnerEntity filePartnerEntity = null;
        if (ObjectUtil.isNotEmpty(perfix)) {
            fileurl = perfix + fileurl;
        }
        String filename = file.getName();
        int i = filename.lastIndexOf(".");
        int j = fileurl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String targetFileName = fileurl.substring(j + 1, fileurl.length());
        String ossPath = subString(fileurl);//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg


        filePartnerEntity = new MFilePartnerEntity();
        filePartnerEntity.setFilePath(fileurl);
        filePartnerEntity.setFileNameOrc(filename);
        filePartnerEntity.setFileTypeOrc(substring);
        filePartnerEntity.setFileName(targetFileName);
        filePartnerEntity.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));

        tencentCloudService.uploadFileObject(isPub,bucketName, file, ossPath);
        filePartnerEntity.setFileKey(ossPath);
        filePartnerMapper.insertSelective(filePartnerEntity);
        return filePartnerEntity;
    }



    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/app-partner/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }

    /**
     * @deprecated 文件描述
     * @param files
     */
    private void validatefile(MultipartFile[] files) {
        for (MultipartFile file : files) {
            String filename = file.getOriginalFilename();
            if (filename == null || "".equals(filename.trim())) {
                throw new PartnerExceptionInfo(PartnerErrorEnum.FILEEMPTY_EXCEPTION.errorCode,PartnerErrorEnum.FILEEMPTY_EXCEPTION.errorMessage);
            }
            int i = filename.lastIndexOf(".");
            //获取后缀名
            String substring = filename.substring(i, filename.length()).toLowerCase();
            //判断是否为规定可上传的文件
            String regexfile = "^(.doc|.pdf|.txt|.docx|.xlsx|.xls|.ppt|.apk|.jpg|.ico|.gif|.png|.jpeg|.bmp|.mp3|.mp4|.avi|.flv|.rmvb|.eml|.html|.msg|.htm|.odt|.wps|.mhtml|.zip|.rar|.asf|.wmv)$";
            String regeximage = "^(.bmp|.jpg|.pptx|.png|.tif|.gif|.pcx|.tga|.exif|.fpx|.svg|.psd|.cdr|.pcd|.dxf|.ufo|.eps|.ai|.raw|.WMF|.webp)$";
            if (!substring.matches(regexfile) && !substring.matches(regeximage)) {
                throw new PartnerExceptionInfo(PartnerErrorEnum.FILETYPE_EXCEPTION.errorCode,PartnerErrorEnum.FILETYPE_EXCEPTION.errorMessage);
            }

        }
    }

    private String getAppendixPerfix() {

        return "/data/project/get/app-partner/target";
    }
}
