package com.apps.controller.system;

import com.apps.api.dto.coupon.SaveCouponUserDto;
import com.apps.api.dto.partner.SavePartnerUserDto;
import com.apps.api.dto.partner.UpdatePartnerLockDto;
import com.apps.api.dto.partner.UpdateUserRoleDto;
import com.apps.api.dto.system.ResetPasswordDto;
import com.apps.api.dto.system.UpdateUserInfoDto;
import com.apps.api.dto.system.UpdateUserPasswordDto;
import com.apps.api.vo.system.SystemUserDetailVo;
import com.apps.api.vo.system.SystemUserVo;
import com.apps.service.SystemRoleService;
import com.apps.service.SystemUserRoleService;
import com.apps.service.SystemUserService;
import com.apps.service.logic.SmsService;
import com.apps.service.logic.WechatService;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/1/13  18:46
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/user")
@Tag(description = "系统用户管理", name = "系统用户管理")
public class SystemUserController {

    private final SystemUserService systemUserService;
    private final SystemUserRoleService userRoleService;
    private final SystemRoleService roleService;
    private final WechatService wechatService;
    private SmsService smsService;

    @Operation(summary = "注册新用户-partner", description = "注册新用户-partner")
    @Inner(value = false)
    @PostMapping("/registryUser")
    public R<Long> savePartnerUser(@RequestBody @Valid SavePartnerUserDto partnerUserDto) {
        return R.ok(systemUserService.savePartnerUser(partnerUserDto));
    }

    @Operation(summary = "注册新用户-coupon", description = "注册新用户-coupon")
    @Inner(value = false)
    @PostMapping("/saveCouponUser")
    public R<Long> saveCouponUser(@RequestBody @Valid SaveCouponUserDto couponUserDto) {
        return R.ok(systemUserService.saveCouponUser(couponUserDto));
    }

    @Operation(summary = "批量获取用户角色名称", description = "批量获取用户角色名称")
    @GetMapping("/getUserRoleNameList")
    @Inner(value = false)
    public R<Map<Long, Map<String, String>>> getUserRoleNameList(@RequestParam("userIdList") List<Long> userIdList) {
        return R.ok(userRoleService.getUserRoleNameList(userIdList));
    }

    @Operation(summary = "根据用户获取角色Id", description = "根据用户获取角色Id")
    @PostMapping("/getRoleIdByUserId")
    @Inner(value = false)
    public R<Long> getRoleIdByUserId(@RequestParam("userId") Long userId) {
        return R.ok(userRoleService.getRoleIdByUserId(userId));
    }

    @Operation(summary = "停用/启用账户", description = "停用/启用账户")
    @Inner(value = false)
    @PostMapping("/lockUser")
    public R<Long> lockUser(@RequestBody @Valid UpdatePartnerLockDto lockDto) {
        systemUserService.lockUser(lockDto);
        return R.ok();
    }

    @Operation(summary = "根据角色获取用户", description = "根据角色获取用户")
    @Inner(value = false)
    @GetMapping("/getPlatformUserByRole")
    public R<List<Long>> getPlatformUserByRole(@RequestParam("platformId") Long platformId,
                                               @RequestParam("platformCode") String platformCode,
                                               @RequestParam("roleCodes") List<String> roleCodes) {
        return R.ok(systemUserService.getPlatformUserByRole(platformId, platformCode, roleCodes));
    }

    @Operation(summary = "角色列表", description = "角色列表")
    @GetMapping("/getRoleList")
    @Inner(value = false)
    public R<List<Map<String, Object>>> getRoleList(@RequestParam("platformId") Long platformId,
                                                    @RequestParam("platformCode") String platformCode) {
        return R.ok(roleService.getRoleList(platformId, platformCode));
    }

    @Operation(summary = "用户详情", description = "用户详情")
    @GetMapping("/getUser")
    public R<SystemUserDetailVo> getUser() {
        FzhUser user = SecurityUtils.getUser();
        return R.ok(systemUserService.getUser(user.getId()));
    }

    @Operation(summary = "修改用户信息", description = "修改用户信息")
    @PostMapping("/updateUser")
    public R<SystemUserVo> updateUser(@Valid @RequestBody UpdateUserInfoDto updateUserInfoDto) {
        return R.ok(systemUserService.updateUser(updateUserInfoDto));
    }

    @Operation(summary = "修改用户密码", description = "修改用户密码")
    @PostMapping("/updateUserPassword")
    public R<String> updateUserPassword(@Valid @RequestBody UpdateUserPasswordDto userPasswordDto) {
        systemUserService.updateUserPassword(userPasswordDto);
        return R.ok("修改成功");
    }

    @Operation(summary = "重置用户密码-partner", description = "重置用户密码-partner")
    @Inner(value = false)
    @PostMapping("/resetPartnerUserPassword")
    public R<Boolean> resetPartnerUserPassword(@RequestBody @Valid ResetPasswordDto resetPasswordDto) {
        systemUserService.resetPartnerUserPassword(resetPasswordDto);
        return R.ok(true);
    }

    @Operation(summary = "修改用户角色", description = "修改用户角色")
    @PostMapping("/updateUserRole")
    @Inner(value = false)
    public R updateUserRole(@RequestBody UpdateUserRoleDto updateUserRoleDto) {
        this.roleService.updateUserRole(updateUserRoleDto);
        return R.ok();
    }

    @Operation(summary = "删除用户缓存并下线", description = "删除用户缓存并下线")
    @DeleteMapping("/delUserCache")
    @Inner(value = false)
    public R delUserCache(@RequestParam("userId") Long userId) {
        this.roleService.delUserCache(userId);
        return R.ok();
    }

    @Operation(summary = "用户批量下线", description = "用户批量下线")
    @GetMapping("/userOfferLine")
    @Inner(value = false)
    public R userOfferLine(@RequestParam("loginIds") List<String> loginIds) {
        this.roleService.userOfferLine(loginIds);
        return R.ok();
    }

    @Operation(summary = "获取用户openId", description = "获取用户openId")
    @GetMapping("/getOpenId")
    @Inner(value = false)
    public R<String> getOpenId(@RequestParam("code") String code, @RequestParam("appId") String appId, @RequestParam("secret") String secret) {
        return R.ok(wechatService.getOpenId(code, appId, secret));
    }

    @Operation(summary = "测试发送邮件", description = "测试发送邮件")
    @GetMapping("/testSend")
    @Inner(value = false)
    public R<String> testSend() {
        smsService.sendMail("11", "11", "<EMAIL>",3);
        return R.ok();
    }

}
