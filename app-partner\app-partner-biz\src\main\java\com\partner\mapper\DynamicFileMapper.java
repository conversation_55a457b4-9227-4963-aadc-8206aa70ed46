package com.partner.mapper;

import com.partner.config.DynamicSqlProvider;
import com.partner.vo.file.UploadFileParam;
import com.partner.vo.file.UploadFileVo;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DynamicFileMapper {

    /**
     * 文件上传-文件库
     *
     * @param param
     * @param vo
     */
    @InsertProvider(type = DynamicSqlProvider.class, method = "insertFileRecord")
    void insertFileRecord(@Param("param") UploadFileParam param, @Param("vo") UploadFileVo vo);

    /**
     * 文件上传-媒体库
     *
     * @param param
     */
    @InsertProvider(type = DynamicSqlProvider.class, method = "insertMediaRecord")
    void insertMediaRecord(@Param("param") UploadFileParam param);
}
