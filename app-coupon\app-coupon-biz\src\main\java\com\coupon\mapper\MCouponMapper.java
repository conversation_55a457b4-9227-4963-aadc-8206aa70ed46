package com.coupon.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coupon.dto.FindTakenRecDto;
import com.coupon.entity.MCouponEntity;
import com.coupon.vo.CouponTakenRecVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("coupon")
public interface MCouponMapper extends BaseMapper<MCouponEntity> {

    IPage<CouponTakenRecVo> getCouponTakenRec(Page page, @Param("findTakenRecDto") FindTakenRecDto findTakenRecDto);

}
