package com.insurance.service;

import com.insurance.dto.client.SaveClientDto;
import com.insurance.entity.PartnerUserClient;
import com.insurance.vo.insurance.client.ClientVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
public interface PartnerUserClientService {

    /**
     * 新增编辑客户
     *
     * @param saveClientDto
     */
    void saveClient(SaveClientDto saveClientDto);

    /**
     * 客户列表
     *
     * @param progress
     * @return
     */
    List<ClientVo> getClientList(Integer progress,String keyword);

    /**
     * 删除联系人
     *
     * @param id
     */
    void deleteClient(Long id);

    /**
     * 客户详情
     * @param id
     * @return
     */
    PartnerUserClient getClient(Long id);
}
