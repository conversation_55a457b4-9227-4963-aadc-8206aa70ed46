package com.partner.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "课程信息基础数据")
public class CourseCombox {
    @Schema(description="课程ID")
    Long courseId;
    @Schema(description="课程名称")
    String courseName;

    @Schema(description="课程中文名称")
    String courseNameChn;

    public String getCourseName() {
        if(courseName==null || courseName.length()==0){
            courseName=courseNameChn;
        }
        return courseName;
    }

    public String getCourseNameChn() {
        if(courseNameChn==null || courseNameChn.length()==0){
            courseNameChn=courseName;
        }

        return courseNameChn;
    }
}
