package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-23 19:14:46
 */

@Data
@TableName("m_institution_ranking")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_institution_ranking ")
public class MInstitutionRankingEntity extends Model<MInstitutionRankingEntity>{

  @Schema(description = "院校排名id")
  private Long id;
 

  @Schema(description = "年度")
  private Integer year;
 

  @Schema(description = "排名类型：QS=0/TIMES=1/THE=2/USNews=3/Macleans=4/ARWU=5/英国卫报=6/QS专业排名=10")
  private Integer rankingType;
 

  @Schema(description = "排名范围（下限）")
  private Integer rankingMin;
 

  @Schema(description = "排名范围（上限）")
  private Integer rankingMax;
 

  @Schema(description = "排名描述")
  private String rankingNote;
 

  @Schema(description = "学校Id")
  private Long fkInstitutionId;
 

  @Schema(description = "院校名称")
  private String institutionName;
 

  @Schema(description = "院校名称（中文）")
  private String institutionNameChn;
 

  @Schema(description = "学校性质")
  private String institutionNature;
 

  @Schema(description = "国家id")
  private Long fkAreaCountryId;
 

  @Schema(description = "国家名称")
  private String areaCountryName;
 

  @Schema(description = "专业大类id")
  private Long fkCourseTypeGroupId;
 

  @Schema(description = "专业大类名称")
  private String courseTypeGroupName;
 

  @Schema(description = "专业大类名称（中文）")
  private String courseTypeGroupNameChn;
 

  @Schema(description = "专业小类id")
  private Long fkCourseTypeId;
 

  @Schema(description = "专业小类名称")
  private String courseTypeName;
 

  @Schema(description = "专业小类名称（中文）")
  private String courseTypeNameChn;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
