package com.insurance.service;

import com.insurance.dto.email.RecipientInfo;
import com.insurance.enums.EmailTemplateType;

import java.util.List;
import java.util.Map;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/30
 * @Version 1.0
 * @apiNote:通知-邮件、短信
 */
public interface NotifyService {

    /**
     * 发送短信通知
     *
     * @param phones
     */
    void sendSmsNotify(List<RecipientInfo> phones);

    /**
     * 发送邮件通知
     *
     * @param emails
     */
    void sendEmailNotify(EmailTemplateType templateType, List<RecipientInfo> emails);
}
