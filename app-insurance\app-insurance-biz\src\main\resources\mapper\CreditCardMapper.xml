<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insurance.mapper.CreditCardMapper">

    <select id="selectRemindCreditCardIds" resultType="java.lang.Long">
        SELECT
        c.id
        FROM
        m_credit_card_reminder r
        JOIN
        m_credit_card c ON r.fk_credit_card_id = c.id
        WHERE
        c.is_active = 1
        <choose>
            <when test="type == 1">
                AND r.is_repayment_remind = 1
                AND (
                DAY(CURDATE()) = c.statement_date
                OR (
                DAY(CURDATE()) = DAY(LAST_DAY(CURDATE()))
                AND c.statement_date > DAY(LAST_DAY(CURDATE()))
                )
                )
            </when>
            <when test="type == 2">
                AND r.is_repayment_remind = 1
                AND (
                DAY(CURDATE()) = c.payment_date
                OR (
                DAY(CURDATE()) = DAY(LAST_DAY(CURDATE()))
                AND c.payment_date > DAY(LAST_DAY(CURDATE()))
                )
                )
            </when>
        </choose>
    </select>

    <select id="selectQuotaRemindCreditCardIds" resultType="java.lang.Long">
        SELECT c.id
        FROM m_credit_card c
                 JOIN
             m_credit_card_reminder r ON c.id = r.fk_credit_card_id
        WHERE c.is_active = 1
          AND r.is_quota_remind = 1
          AND c.current_amount &lt;= r.quota_limit;

    </select>
</mapper>
