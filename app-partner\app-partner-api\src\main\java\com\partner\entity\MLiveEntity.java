package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-18 17:15:01
 */

@Data
@TableName("m_live")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_live ")
public class MLiveEntity extends Model<MLiveEntity>{

  @Schema(description = "直播Id")
  private Long id;
 

  @Schema(description = "公司Id")
  private Long fkCompanyId;
 

  @Schema(description = "平台应用Id")
  private Long fkPlatformId;
 

  @Schema(description = "平台应用CODE")
  private String fkPlatformCode;
 

  @Schema(description = "直播类型Id")
  private Long fkLiveTypeId;
 

  @Schema(description = "培训标题")
  private String title;
 

  @Schema(description = "课程编码")
  private String num;
 

  @Schema(description = "直播详情")
  private String description;
 

  @Schema(description = "适用地区")
  private String targetAreas;
 

  @Schema(description = "讲师英文(拼音)名")
  private String teacherName;
 

  @Schema(description = "讲师中文名")
  private String teacherNameChn;
 

  @Schema(description = "讲师性别：0女/1男")
  private Integer teacherGender;
 

  @Schema(description = "讲师职位")
  private String teacherJob;
 

  @Schema(description = "讲师简介")
  private String teacherBrief;
 

  @Schema(description = "直播开始时间")
  private LocalDateTime liveTimeStart;
 

  @Schema(description = "直播结束时间")
  private LocalDateTime liveTimeEnd;
 

  @Schema(description = "直播链接")
  private String liveUrl;
 

  @Schema(description = "回播链接")
  private String loopUrl;
 

  @Schema(description = "是否有回放：0否/1是")
  private Boolean isLoop;
 

  @Schema(description = "【暂无用】状态：0未开始/1直播中/2已结束")
  private Integer liveStatus;
 

  @Schema(description = "上架状态：0已下架/1已上架")
  private Integer status;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
