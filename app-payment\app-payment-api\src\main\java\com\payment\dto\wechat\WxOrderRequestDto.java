package com.payment.dto.wechat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/7/15
 * @Version 1.0
 * @apiNote:微信订单参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxOrderRequestDto {

    @Schema(description = "商户订单号")
    private String outTradeNo;

    @Schema(description = "金额-分")
    private Integer amount;

    @Schema(description = "用户openid")
    private String openid;

    @Schema(description = "订单描述")
    private String description;

    @Schema(description = "下单成功需要通知的topic-用于业务处理自己的逻辑")
    private String notifyTopic;
}
