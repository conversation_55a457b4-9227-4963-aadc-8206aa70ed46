package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Schema(description = "返回新闻")
public class SNewsListVo implements Serializable {

    @Schema(description="新闻Id")
    private Long id;
    @Schema(description = "标题")
    private String title;
    @Schema(description = "简介")
    private String profile;
    @Schema(description = "描述内容")
    private String description;

    @Schema(description = "发布时间")
    private LocalDate publishTime;

    @Schema(description = "图片桶地址")
    private String fileKey;
    @Schema(description = "图片名称")
    private String fileNameOrc;
    @Schema(description = "网页标题")
    private String webTitle;
    @Schema(description = "网页标题描述")
    private String webMetaDescription;


}
