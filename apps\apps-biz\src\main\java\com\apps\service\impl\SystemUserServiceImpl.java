package com.apps.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.apps.api.dto.VerifyUserDto;
import com.apps.api.dto.base.BaseUserInfoDto;
import com.apps.api.dto.base.BusinessMethodDto;
import com.apps.api.dto.coupon.SaveCouponUserDto;
import com.apps.api.dto.partner.SavePartnerUserDto;
import com.apps.api.dto.partner.UpdatePartnerLockDto;
import com.apps.api.dto.system.ResetPasswordDto;
import com.apps.api.dto.system.SaveUserDto;
import com.apps.api.dto.system.UpdateUserInfoDto;
import com.apps.api.dto.system.UpdateUserPasswordDto;
import com.apps.api.dto.wechat.WxGetPhoneDto;
import com.apps.api.entity.*;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.api.vo.system.SystemUserDetailVo;
import com.apps.api.vo.system.SystemUserVo;
import com.apps.api.vo.system.UserPermissionVo;
import com.apps.config.EncryptConfig;
import com.apps.constant.redis.RedisConstant;
import com.apps.exception.AppsGlobalException;
import com.apps.mapper.*;
import com.apps.rocketmq.msg.UserOfflineDto;
import com.apps.rocketmq.producer.UserOfflineProducer;
import com.apps.service.SystemRoleMenuService;
import com.apps.service.SystemUserService;
import com.apps.service.logic.NacosService;
import com.apps.service.logic.SmsService;
import com.apps.service.logic.WechatService;
import com.apps.util.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.constant.CommonConstants;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class SystemUserServiceImpl extends ServiceImpl<SystemUserMapper, SystemUserEntity> implements SystemUserService {

    private final SystemUserMapper systemUserMapper;
    private final WechatService wechatService;
    private final SystemUserRoleMapper userRoleMapper;
    private final SystemRoleMapper roleMapper;
    private SystemRoleMenuService roleMenuService;
    private SystemMenuMapper menuMapper;
    private SystemPlatformMapper platformMapper;
    @Autowired
    private NacosService nacosService;
    private RedisUtil redisUtil;
    private SystemUserPlatformLoginMapper platformLoginMapper;
    private final SmsService smsService;
    private final PasswordEncoder encoder = new BCryptPasswordEncoder();
    @Autowired
    private UserOfflineProducer userOfflineProducer;
    @Autowired
    private EncryptConfig encryptConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveUser(SaveUserDto saveUserDto) {
        if (Objects.nonNull(saveUserDto.getId()) && saveUserDto.getId() > 0) {
            SystemUserEntity systemUser = systemUserMapper.selectById(saveUserDto.getId());
            if (Objects.nonNull(systemUser)) {
                BeanUtils.copyProperties(saveUserDto, systemUser);
                systemUser.setGmtModified(LocalDateTime.now());
                systemUserMapper.updateById(systemUser);
                return systemUser.getId();
            }
        }
        SystemUserEntity systemUser = new SystemUserEntity();
        BeanUtils.copyProperties(saveUserDto, systemUser);
        systemUser.setGmtCreate(LocalDateTime.now());
        systemUser.setGmtModified(LocalDateTime.now());
        systemUser.setIsDelFlag(0);
        systemUserMapper.insert(systemUser);
        return systemUser.getId();
    }

    @Override
    public SystemUserDetailVo getUser(Long userId) {
        SystemUserDetailVo userVo = new SystemUserDetailVo();
        SystemUserEntity systemUser = systemUserMapper.selectById(userId);
        if (Objects.nonNull(systemUser)) {
            BeanUtils.copyProperties(systemUser, userVo);
        }
        SystemUserPlatformLoginEntity loginEntity = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>().eq(SystemUserPlatformLoginEntity::getFkUserId, userId));
        if (Objects.nonNull(loginEntity)) {
            userVo.setLoginId(loginEntity.getLoginId());
            userVo.setLastLoginData(DateUtil.dateToString(DateUtil.DATE_FORMAT, loginEntity.getGmtModified()));
        }
        //角色信息
        List<SystemUserRoleEntity> userRoleEntities = userRoleMapper.selectList(new LambdaQueryWrapper<SystemUserRoleEntity>()
                .eq(SystemUserRoleEntity::getFkUserId, userId)
                .orderByDesc(SystemUserRoleEntity::getId));
        if (CollectionUtils.isNotEmpty(userRoleEntities)) {
            SystemRoleEntity role = roleMapper.selectById(userRoleEntities.get(0).getFkRoleId());
            if (Objects.nonNull(role)) {
                userVo.setRoleCode(role.getRoleCode());
                userVo.setRoleId(role.getId());
            }
        }
        userVo.setCreateData(DateUtil.dateToString(DateUtil.DATE_FORMAT, systemUser.getGmtCreate()));
        return userVo;
    }

    @Override
    public Boolean saveUserWxPhone(WxGetPhoneDto wxGetPhoneDto) {
        JSONObject userPhone = wechatService.getUserPhone(wxGetPhoneDto.getAppId(), wxGetPhoneDto.getCode());
        SystemUserEntity systemUser = systemUserMapper.selectById(wxGetPhoneDto.getUserId());
        if (Objects.nonNull(systemUser)) {
            systemUser.setMobile(userPhone.getString("phoneNumber"));
            systemUser.setMobileAreaCode(userPhone.getString("countryCode"));
            return systemUserMapper.updateById(systemUser) > 0;
        }
        return false;
    }

    @Override
    public Boolean checkLoginUser(Long userId) {
        SystemUserEntity systemUser = systemUserMapper.selectById(userId);
        if (Objects.isNull(systemUser)) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        if (systemUser.getIsLockFlag().equals(1)) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_LOCK);
        }
        return true;
    }

    @Override
    @SneakyThrows
    public UserPermissionVo getUserCacheInfo(Boolean getBusinessData, SystemUserPlatformLoginEntity loginEntity, Long userId) {
        SystemUserEntity systemUser = systemUserMapper.selectById(userId);
        if (Objects.isNull(systemUser)) {
            log.error("用户不存在,用户ID:{}", userId);
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        UserPermissionVo userPermission = getUserPermission(userId);
        userPermission.getUserVo().setName(loginEntity.getLoginId());
        userPermission.getUserVo().setLoginId(loginEntity.getLoginId());
        userPermission.getUserVo().setLoginPs(loginEntity.getLoginPs());
        //角色信息
        List<SystemUserRoleEntity> userRoleEntities = userRoleMapper.selectList(new LambdaQueryWrapper<SystemUserRoleEntity>()
                .eq(SystemUserRoleEntity::getFkUserId, userId)
                .orderByDesc(SystemUserRoleEntity::getId));
        if (CollectionUtils.isNotEmpty(userRoleEntities)) {
            SystemRoleEntity role = roleMapper.selectById(userRoleEntities.get(0).getFkRoleId());
            if (Objects.nonNull(role)) {
                userPermission.getUserVo().setRoleCode(role.getRoleCode());
                userPermission.getUserVo().setRoleId(role.getId());
            }
        }
        loginEntity.setGmtModified(LocalDateTime.now());
        platformLoginMapper.updateById(loginEntity);
        return getBusinessData(userPermission, Boolean.FALSE);
    }

    @Override
    public UserPermissionVo getUserPermission(Long userId) {
        SystemUserEntity systemUser = systemUserMapper.selectById(userId);
        SystemUserVo userVo = new SystemUserVo();
        BeanUtils.copyProperties(systemUser, userVo);
        UserPermissionVo userPermissionVo = new UserPermissionVo();
        userPermissionVo.setUserVo(userVo);
        List<Long> userMenuIds = roleMenuService.getUserMenuIds(userId);
        if (CollectionUtils.isNotEmpty(userMenuIds)) {
            List<String> permissionKey = menuMapper.selectList(new LambdaQueryWrapper<SystemMenuEntity>()
                            .in(SystemMenuEntity::getId, userMenuIds)
                            .ne(SystemMenuEntity::getIsVisible, CommonConstants.STATUS_NORMAL))
                    .stream().map(SystemMenuEntity::getPermissionKey).distinct().collect(Collectors.toList());
            userPermissionVo.setPermissions(permissionKey.toArray(new String[0]));
        } else {
            userPermissionVo.setPermissions(new String[0]);
        }
        List<Long> roleIds = userRoleMapper.selectList(new LambdaQueryWrapper<SystemUserRoleEntity>()
                        .eq(SystemUserRoleEntity::getFkUserId, userId))
                .stream().map(SystemUserRoleEntity::getFkRoleId).distinct().collect(Collectors.toList());
        userPermissionVo.setRoles(roleIds.toArray(new Long[0]));
        return userPermissionVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePartnerUser(SavePartnerUserDto partnerUserDto) {
        Long userId = verifyRegisterUser(partnerUserDto);
        if (Objects.isNull(userId)) {
            SystemUserEntity user = SystemUserEntity.builder()
                    .email(partnerUserDto.getEmail())
                    .fkFromPlatformId(partnerUserDto.getPlatformId())
                    .fkFromPlatformCode(partnerUserDto.getPlatformCode())
                    .name(partnerUserDto.getName())
                    .gmtCreateUser(partnerUserDto.getCreateUser())
                    .gmtCreate(LocalDateTime.now())
                    .gmtModifiedUser(partnerUserDto.getCreateUser())
                    .gmtModified(LocalDateTime.now())
                    .isDelFlag(Integer.parseInt(CommonConstants.STATUS_NORMAL))
                    .isLockFlag(Integer.parseInt(CommonConstants.STATUS_NORMAL))
                    .build();
            this.save(user);
            log.info("新增用户成功,用户信息:{}", JSONObject.toJSONString(user));
            user.setNum(CodeGeneratorUtil.generateUserCode(user.getFkFromPlatformId(), user.getId()));
            this.updateById(user);
            userId = user.getId();
            SystemUserRoleEntity userRole = SystemUserRoleEntity.builder()
                    .fkUserId(userId)
                    .fkRoleId(partnerUserDto.getRoleId())
                    .gmtCreateUser(partnerUserDto.getCreateUser())
                    .gmtCreate(LocalDateTime.now())
                    .gmtModifiedUser(partnerUserDto.getCreateUser())
                    .gmtModified(LocalDateTime.now())
                    .build();
            userRoleMapper.insert(userRole);
            String password = PasswordGenerator.generateNumericPassword();
            SystemUserPlatformLoginEntity loginEntity = SystemUserPlatformLoginEntity.builder()
                    .fkUserId(userId)
                    .fkPlatformId(partnerUserDto.getPlatformId())
                    .fkPlatformCode(partnerUserDto.getPlatformCode())
                    .gmtCreateUser(partnerUserDto.getCreateUser())
                    .gmtCreate(LocalDateTime.now())
                    .loginId(partnerUserDto.getEmail())
                    .loginPs(encoder.encode(password))
                    .gmtModifiedUser(partnerUserDto.getCreateUser())
                    .gmtModified(LocalDateTime.now())
                    .build();
            platformLoginMapper.insert(loginEntity);
            smsService.sendMail("诚邀您使用由华通国际推出的华通伙伴小程序！", password, loginEntity.getLoginId(), 1);
            return userId;
        }
        //todo 已存在的用注册华通伙伴用户邮件
        smsService.sendMail("诚邀您使用由华通国际推出的华通伙伴小程序！", partnerUserDto.getName(), partnerUserDto.getEmail(), 3);
        return userId;
    }

    @Override
    public void lockUser(UpdatePartnerLockDto lockDto) {
        SystemUserEntity systemUser = systemUserMapper.selectById(lockDto.getUserId());
        if (Objects.isNull(systemUser)) {
            log.error("未找到用户信息,用户ID:{}", lockDto.getUserId());
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        systemUser.setIsLockFlag(lockDto.getLockFlag());
        systemUserMapper.updateById(systemUser);
        if (CommonConstants.STATUS_DEL.equals(lockDto.getLockFlag().toString())) {
            redisUtil.del(systemUser.getFkTenantId() + ":" + systemUser.getFkFromPlatformCode() + ":" + lockDto.getUserId());
        }
        //发送下线消息
        if (lockDto.getLockFlag().toString().equals(CommonConstants.STATUS_DEL)) {
            SystemUserPlatformLoginEntity platformLogin = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                    .eq(SystemUserPlatformLoginEntity::getFkUserId, lockDto.getUserId()));
            if (Objects.nonNull(platformLogin)) {
                UserOfflineDto userOfflineDto = new UserOfflineDto(Arrays.asList(platformLogin.getLoginId()), systemUser.getFkFromPlatformId());
                userOfflineProducer.sendUserOfflineMessage(userOfflineDto);
            }
        }
    }

    @Override
    public List<Long> getPlatformUserByRole(Long platformId, String platformCode, List<String> roleCodes) {
        return userRoleMapper.findUserIdByRoleCode(roleCodes, platformId, platformCode);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveCouponUser(SaveCouponUserDto couponUserDto) {
        verifyRegisterCouponUser(couponUserDto);
        SystemUserEntity user = SystemUserEntity.builder()
                .mobile(couponUserDto.getMobile())
                .fkFromPlatformId(couponUserDto.getPlatformId())
                .fkFromPlatformCode(couponUserDto.getPlatformCode())
                .name(StringUtils.isBlank(couponUserDto.getName()) ? couponUserDto.getNickName() : couponUserDto.getName())
                .nickname(couponUserDto.getNickName())
                .gmtCreateUser(couponUserDto.getMobile())
                .gmtCreate(LocalDateTime.now())
                .gmtModifiedUser(couponUserDto.getMobile())
                .nickname(couponUserDto.getName())
                .gmtModified(LocalDateTime.now())
                .isDelFlag(Integer.parseInt(CommonConstants.STATUS_NORMAL))
                .isLockFlag(Integer.parseInt(CommonConstants.STATUS_NORMAL))
                .fkTenantId(2L)
                .build();
        this.save(user);
        log.info("新增用户成功,用户信息:{}", JSONObject.toJSONString(user));
        user.setNum(CodeGeneratorUtil.generateUserCode(user.getFkFromPlatformId(), user.getId()));
        this.updateById(user);
        //新增用户角色
        if (StringUtils.isNotBlank(couponUserDto.getRoleCode())) {
            SystemRoleEntity role = roleMapper.selectOne(new LambdaQueryWrapper<SystemRoleEntity>()
                    .eq(SystemRoleEntity::getRoleCode, couponUserDto.getRoleCode())
                    .eq(SystemRoleEntity::getFkPlatformId, couponUserDto.getPlatformId())
                    .eq(SystemRoleEntity::getFkPlatformCode, couponUserDto.getPlatformCode()));
            if (Objects.nonNull(role)) {
                SystemUserRoleEntity userRole = SystemUserRoleEntity.builder()
                        .fkUserId(user.getId())
                        .fkRoleId(role.getId())
                        .gmtCreateUser(couponUserDto.getMobile())
                        .gmtCreate(LocalDateTime.now())
                        .gmtModifiedUser(couponUserDto.getMobile())
                        .gmtModified(LocalDateTime.now())
                        .fkTenantId(2L)
                        .build();
                userRoleMapper.insert(userRole);
            }
        }
        String password = PasswordGenerator.generateNumericPassword();
        SystemUserPlatformLoginEntity loginEntity = SystemUserPlatformLoginEntity.builder()
                .fkUserId(user.getId())
                .fkPlatformId(couponUserDto.getPlatformId())
                .fkPlatformCode(couponUserDto.getPlatformCode())
                .gmtCreateUser(couponUserDto.getMobile())
                .gmtCreate(LocalDateTime.now())
                .loginId(couponUserDto.getMobile())
                .loginPs(encoder.encode(password))
                .gmtModifiedUser(couponUserDto.getMobile())
                .gmtModified(LocalDateTime.now())
                .fkTenantId(2L)
                .build();
        platformLoginMapper.insert(loginEntity);
        return user.getId();
    }

    @Override
    public Integer verifyUserSmsCode(String mobile, String platformCode, Long platformId, String captcha) {
        Long count = systemUserMapper.selectCount(new LambdaQueryWrapper<SystemUserEntity>()
                .eq(SystemUserEntity::getMobile, mobile)
                .eq(SystemUserEntity::getFkFromPlatformCode, platformCode)
                .eq(SystemUserEntity::getFkFromPlatformId, platformId));

        Object codeObj = redisUtil.get(RedisConstant.SMS_CODE_KEY_PREFIX + mobile);
        if ("9999".equals(captcha) && count > 0) {
            return 2;
        }
        if (Objects.isNull(codeObj) || !codeObj.toString().equals(captcha)) {
            return 0;
        }
        if (count < 1) {
            return 1;
        }
        redisUtil.del(RedisConstant.SMS_CODE_KEY_PREFIX + mobile);
        return 2;
    }


    @Override
    public void updateUserPassword(UpdateUserPasswordDto userPasswordDto) {
        FzhUser user = SecurityUtils.getUser();
        if (!userPasswordDto.getNewPassword().equals(userPasswordDto.getConfirmNewPassword())) {
            throw new AppsGlobalException(GlobExceptionEnum.UPDATE_PASSWORD_CONFIRM_ERROR);
        }
        SystemUserPlatformLoginEntity platformLogin = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getFkUserId, user.getId()));
        if (Objects.isNull(platformLogin)) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        String oldPassword = EncryptPasswordUtil.decrypt(userPasswordDto.getOldPassword(), encryptConfig.getKey());
        //明文在前-加密加后
        boolean matches = encoder.matches(oldPassword, platformLogin.getLoginPs());
        if (!matches) {
            throw new AppsGlobalException(GlobExceptionEnum.UPDATE_PASSWORD_OLD_PASSWORD_ERROR);
        }
        //解密密码
        String decryptPassword = EncryptPasswordUtil.decrypt(userPasswordDto.getNewPassword(), encryptConfig.getKey());
        platformLogin.setLoginPs(encoder.encode(decryptPassword));
        platformLogin.setGmtModifiedUser(user.getLoginId());
        platformLogin.setGmtModified(LocalDateTime.now());
        platformLoginMapper.updateById(platformLogin);
        //下线
        UserOfflineDto userOfflineDto = new UserOfflineDto(Arrays.asList(platformLogin.getLoginId()), platformLogin.getFkPlatformId());
        userOfflineProducer.sendUserOfflineMessage(userOfflineDto);
    }

    @Override
    public Boolean verifyUserBeforeLogin(VerifyUserDto verifyUserDto) {
        //先校验登录账号信息
        SystemUserPlatformLoginEntity platformLogin = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getLoginId, verifyUserDto.getAccount())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformCode, verifyUserDto.getFormPlatformCode())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformId, verifyUserDto.getFormPlatformId()));
        if (Objects.isNull(platformLogin)) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_ACCOUNT_NOT_EXISTS);
        }
        //检查用户
        SystemUserEntity user = systemUserMapper.selectById(platformLogin.getFkUserId());
        if (Objects.isNull(user)) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_ACCOUNT_NOT_EXISTS);
        }
        if (user.getIsDelFlag().equals(Integer.parseInt(CommonConstants.STATUS_DEL))) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_DELETE);
        }
        if (user.getIsLockFlag().equals(Integer.parseInt(CommonConstants.STATUS_DEL))) {
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_LOCK);
        }
        return Boolean.TRUE;
    }

    @Override
    public SystemUserVo updateUser(UpdateUserInfoDto updateUserInfoDto) {
        Long userId = SecurityUtils.getUser().getId();
        if (Objects.isNull(userId)) {
            log.error("更新用户失败,用户未登录");
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        SystemUserEntity user = systemUserMapper.selectById(userId);
        if (Objects.isNull(user)) {
            log.error("更新用户失败,未找到用户信息,userId:{}", userId);
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        BeanUtil.copyProperties(updateUserInfoDto, user, CopyOptions.create().ignoreNullValue());
        user.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
        user.setGmtModified(LocalDateTime.now());
        systemUserMapper.updateById(user);
        return getUser(userId);
    }

    @Override
    public void resetPartnerUserPassword(ResetPasswordDto resetPasswordDto) {
        SystemUserPlatformLoginEntity platformLogin = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getLoginId, resetPasswordDto.getLoginId()));
        if (Objects.isNull(platformLogin)) {
            log.error("未找到用户登录信息,loginId:{}", resetPasswordDto.getLoginId());
            throw new AppsGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        String password = PasswordGenerator.generateNumericPassword();
        platformLogin.setLoginPs(encoder.encode(password));
        platformLogin.setGmtModifiedUser(resetPasswordDto.getLoginId());
        platformLogin.setGmtModified(LocalDateTime.now());
        platformLoginMapper.updateById(platformLogin);
        smsService.sendMail("Partner小程序账号密码重置成功", password, resetPasswordDto.getLoginId(), 2);
        UserOfflineDto userOfflineDto = new UserOfflineDto(Arrays.asList(platformLogin.getLoginId()), platformLogin.getFkPlatformId());
        userOfflineProducer.sendUserOfflineMessage(userOfflineDto);
    }

    private UserPermissionVo getBusinessData(UserPermissionVo userPermissionVo, Boolean getBusinessData) {
        if (!getBusinessData) {
            return userPermissionVo;
        }
        SystemUserVo userVo = userPermissionVo.getUserVo();
        SystemPlatformEntity platform = platformMapper.selectOne(new LambdaQueryWrapper<SystemPlatformEntity>()
                .eq(SystemPlatformEntity::getCode, userVo.getFkFromPlatformCode()));
        if (Objects.isNull(platform)) {
            log.error("未找到平台信息,平台CODE:{}", userVo.getFkFromPlatformCode());
            throw new AppsGlobalException(GlobExceptionEnum.PLATFORM_NOT_EXIST);
        }

        if (StringUtils.isBlank(platform.getLoginFunction())) {
            log.info("未配置第三方用户信息接口,平台CODE:{}", userVo.getFkFromPlatformCode());
            return userPermissionVo;
        }

        BusinessMethodDto methodDto = JSONObject.toJavaObject(JSONObject.parseObject(platform.getLoginFunction()), BusinessMethodDto.class);
        if (Objects.nonNull(methodDto) && methodDto.getEnable()) {
            BaseUserInfoDto userInfoDto = BaseUserInfoDto.builder()
                    .platformId(userVo.getFkFromPlatformId())
                    .userId(userVo.getId())
                    .tenantId(userVo.getFkTenantId())
                    .roleCode(userVo.getRoleCode())
                    .build();

            Instance serviceInstance = nacosService.getServiceInstance(methodDto.getName());
            if (Objects.isNull(serviceInstance)) {
                log.error("未找到第三方用户信息接口服务:{}", methodDto.getName());
                throw new AppsGlobalException(GlobExceptionEnum.REMOTE_SERVICE_NOT_FIND);
            }
            log.info("调用第三方用户信息参数:{}", JSONObject.toJSONString(userInfoDto));
            String url = "http://" + serviceInstance.getIp() + ":" + serviceInstance.getPort() + methodDto.getPath();
            log.info("调用第三方用户信息接口请求地址:{}", url);

            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("tenantid", userInfoDto.getTenantId().toString())
                    .header("formplatformid", userInfoDto.getPlatformId().toString())
                    .body(JSON.toJSONString(userInfoDto))
                    .timeout(5000)
                    .execute();
            log.info("调用第三方用户信息接口返回结果:{}", response.body());
            if (StringUtils.isBlank(response.body())) {
                log.error("调用第三方用户信息接口返回结果为空,请求路径:{}", url);
                throw new AppsGlobalException(GlobExceptionEnum.REMOTE_SERVICE_NO_RESULT);
            }

            R r = JSONObject.parseObject(response.body(), R.class);

            if (!r.isSuccess() || Objects.isNull(r.getData())) {
                log.error("调用第三方用户信息接口返回结果失败,请求路径:{},返回结果:{}", url, response.body());
                throw new AppsGlobalException(GlobExceptionEnum.REMOTE_SERVICE_NO_RESULT);
            }
            String redisKey = userVo.getFkTenantId() + ":" + platform.getCode() + ":" + userVo.getId();
            redisUtil.set(redisKey, JSONObject.toJSONString(r.getData()));
        }
        return userPermissionVo;
    }

    private Long verifyRegisterUser(SavePartnerUserDto partnerUserDto) {
        //校验平台
        checkPlatform(partnerUserDto.getPlatformCode(), partnerUserDto.getPlatformId());
        //校验角色
        SystemRoleEntity role = roleMapper.selectById(partnerUserDto.getRoleId());
        if (Objects.isNull(role)) {
            log.error("注册失败,该角色不存在,参数:{}", JSONObject.toJSONString(partnerUserDto));
            throw new AppsGlobalException(GlobExceptionEnum.ROLE_NOT_EXIST);
        }
        //校验邮件-Partner用户可以绑定多个邮箱
        SystemUserPlatformLoginEntity loginEntity = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getLoginId, partnerUserDto.getEmail())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformId, partnerUserDto.getPlatformId())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformCode, partnerUserDto.getPlatformCode()));
        if (Objects.nonNull(loginEntity)) {
            log.info("邮箱已注册,无需新增,邮箱:{}", JSONObject.toJSONString(partnerUserDto));
            return loginEntity.getFkUserId();
        }
//        if (Objects.nonNull(loginEntity)) {
//            log.error("注册失败,登录信息表中该邮箱已注册,邮箱:{}", JSONObject.toJSONString(partnerUserDto));
//            throw new AppsGlobalException(GlobExceptionEnum.EMAIL_ALREADY_REGISTERED);
//        }
//        SystemUserEntity systemUser = systemUserMapper.selectOne(new LambdaQueryWrapper<SystemUserEntity>()
//                .eq(SystemUserEntity::getEmail, partnerUserDto.getEmail())
//                .eq(SystemUserEntity::getFkFromPlatformId, partnerUserDto.getPlatformId())
//                .eq(SystemUserEntity::getFkFromPlatformCode, partnerUserDto.getPlatformCode()));
//        if (Objects.nonNull(systemUser)) {
//            log.error("注册失败，用户表中该邮箱已注册,邮箱:{}", JSONObject.toJSONString(partnerUserDto));
//            throw new AppsGlobalException(GlobExceptionEnum.EMAIL_ALREADY_REGISTERED);
//        }
        return null;
    }

    private void checkPlatform(String platformCode, Long platformId) {
        SystemPlatformEntity platform = platformMapper.selectById(platformId);
        if (Objects.isNull(platform)) {
            log.error("注册失败,该平台不存在,参数:{},{}", platformCode, platformId);
            throw new AppsGlobalException(GlobExceptionEnum.PLATFORM_NOT_EXIST);
        }
        if (!platform.getCode().equals(platformCode)) {
            log.error("注册失败,平台Code错误,参数:{}", platformCode);
            throw new AppsGlobalException(GlobExceptionEnum.PLATFORM_CODE_ERROR);
        }
    }

    private void verifyRegisterCouponUser(SaveCouponUserDto saveCouponUserDto) {
        //校验平台
        checkPlatform(saveCouponUserDto.getPlatformCode(), saveCouponUserDto.getPlatformId());
        //校验手机号
        SystemUserPlatformLoginEntity loginEntity = platformLoginMapper.selectOne(new LambdaQueryWrapper<SystemUserPlatformLoginEntity>()
                .eq(SystemUserPlatformLoginEntity::getLoginId, saveCouponUserDto.getMobile())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformId, saveCouponUserDto.getPlatformId())
                .eq(SystemUserPlatformLoginEntity::getFkPlatformCode, saveCouponUserDto.getPlatformCode()));
        if (Objects.nonNull(loginEntity)) {
            log.error("注册失败,登录信息表中该手机已注册,手机:{}", saveCouponUserDto.getMobile());
            throw new AppsGlobalException(GlobExceptionEnum.MOBILE_ALREADY_REGISTERED);
        }
        SystemUserEntity systemUser = systemUserMapper.selectOne(new LambdaQueryWrapper<SystemUserEntity>()
                .eq(SystemUserEntity::getMobile, saveCouponUserDto.getMobile())
                .eq(SystemUserEntity::getFkFromPlatformId, saveCouponUserDto.getPlatformId())
                .eq(SystemUserEntity::getFkFromPlatformCode, saveCouponUserDto.getPlatformCode()));
        if (Objects.nonNull(systemUser)) {
            log.error("注册失败，用户表中该手机已注册,手机:{}", saveCouponUserDto.getMobile());
            throw new AppsGlobalException(GlobExceptionEnum.MOBILE_ALREADY_REGISTERED);
        }
    }

}
