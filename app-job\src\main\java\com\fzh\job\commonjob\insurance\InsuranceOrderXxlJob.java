
package com.fzh.job.commonjob.insurance;

import cn.hutool.core.date.DateUtil;
import com.insurance.feign.RemoteInsuranceService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

@Slf4j
@Component
public class InsuranceOrderXxlJob {
    @Resource
    private RemoteInsuranceService remoteInsuranceService;

    /**
     * 订单下单请求
     */
    @XxlJob("InsuranceOrderXxlJob")
    public void insuranceOrderXxlJob() {
        try {
            log.info("InsuranceOrderXxlJob start...{}", DateUtil.formatDateTime(new Date()));
            remoteInsuranceService.sendRequest();
        } catch (Exception e) {
            log.error("InsuranceOrderXxlJob error:{}", Arrays.toString(e.getStackTrace()));
            log.error("InsuranceOrderXxlJob error:{}", e.getMessage());
            throw e;
        }
    }

    /**
     * 发送信用卡还款提醒
     */
    @XxlJob("SendRepaymentNotifyXxlJob")
    public void sendRepaymentNotifyXxlJob() {
        try {
            log.info("SendRepaymentNotifyXxlJob start...{}", DateUtil.formatDateTime(new Date()));
            remoteInsuranceService.sendRepaymentNotify();
        } catch (Exception e) {
            log.error("SendRepaymentNotifyXxlJob error:{}", Arrays.toString(e.getStackTrace()));
            log.error("SendRepaymentNotifyXxlJob error:{}", e.getMessage());
            throw e;
        }
    }

    /**
     * 发送信用卡出账提醒
     */
    @XxlJob("SendStatementNotifyXxlJob")
    public void sendStatementNotifyXxlJob() {
        try {
            log.info("SendStatementNotifyXxlJob start...{}", DateUtil.formatDateTime(new Date()));
            remoteInsuranceService.sendStatementNotify();
        } catch (Exception e) {
            log.error("SendStatementNotifyXxlJob error:{}", Arrays.toString(e.getStackTrace()));
            log.error("SendStatementNotifyXxlJob error:{}", e.getMessage());
            throw e;
        }
    }

    /**
     * 订单额度提醒
     */
    @XxlJob("SendQuotaRemindNotifyXxlJob")
    public void sendQuotaRemindNotifyXxlJob() {
        try {
            log.info("SendQuotaRemindNotifyXxlJob start...{}", DateUtil.formatDateTime(new Date()));
            remoteInsuranceService.sendQuotaRemindNotify();
        } catch (Exception e) {
            log.error("SendQuotaRemindNotifyXxlJob error:{}", Arrays.toString(e.getStackTrace()));
            log.error("SendQuotaRemindNotifyXxlJob error:{}", e.getMessage());
            throw e;
        }
    }

}

