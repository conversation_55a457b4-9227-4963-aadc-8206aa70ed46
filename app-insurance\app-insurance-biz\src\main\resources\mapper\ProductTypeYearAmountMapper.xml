<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.ProductTypeYearAmountMapper">


    <select id="getMaxYear" resultType="java.lang.Integer">
        SELECT
            max(policy_year)
        FROM
            u_product_type_year_amount p
        WHERE
            p.insurance_type = #{insurancePlanDTO.insuranceType}
        AND p.product_type = #{insurancePlanDTO.productType}
    </select>

</mapper>