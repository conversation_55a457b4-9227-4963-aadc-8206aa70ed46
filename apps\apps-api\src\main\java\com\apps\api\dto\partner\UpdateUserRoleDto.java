package com.apps.api.dto.partner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/6/17 上午10:14
 * @Version 1.0
 */
@Data
public class UpdateUserRoleDto {

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "创建人LoginId")
    @NotBlank(message = "创建人LoginId不能为空")
    private String createUser;



}
