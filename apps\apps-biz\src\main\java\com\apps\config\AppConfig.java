package com.apps.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {

    private List<AppCredential> credentials;

    public List<AppCredential> getCredentials() {
        return credentials;
    }

    public void setCredentials(List<AppCredential> credentials) {
        this.credentials = credentials;
    }

    public AppCredential getCredential(String code) {
        return credentials.stream().filter(credential -> credential.getCode().equals(code)).findFirst().orElse(null);
    }
}
