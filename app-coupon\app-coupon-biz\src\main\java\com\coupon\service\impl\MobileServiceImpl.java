package com.coupon.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.common.core.constant.SecurityConstants;
import com.common.core.util.R;
import com.coupon.service.IMobileService;
import com.coupon.vo.SmsResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MobileServiceImpl implements IMobileService {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public R sendSmsCode(String mobile) throws Exception {
        try {
                Credential cred = new Credential("AKIDQioiSyHTSEsKZdXEhxgIiw2wd7NQHvL6", "z6uZ8ZopgNr8LNCoCPRGrHXZUPAj9ZyF");

            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            // 从3.0.96版本开始, 单独设置 HTTP 代理（无需要直接忽略）
            // httpProfile.setProxyHost("真实代理ip");
            // httpProfile.setProxyPort(真实代理端口);
            httpProfile.setReqMethod("GET"); // get请求(默认为post请求)
            httpProfile.setConnTimeout(10); // 请求连接超时时间，单位为秒(默认60秒)
            httpProfile.setWriteTimeout(10);  // 设置写入超时时间，单位为秒(默认0秒)
            httpProfile.setReadTimeout(10);  // 设置读取超时时间，单位为秒(默认0秒)

            /* 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com ，也支持指定地域域名访问，例如广州地域的域名为 sms.ap-guangzhou.tencentcloudapi.com */
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            /* 非必要步骤:
             * 实例化一个客户端配置对象，可以指定超时时间等配置 */
            ClientProfile clientProfile = new ClientProfile();
            /* SDK默认用TC3-HMAC-SHA256进行签名
             * 非必要请不要修改这个字段 */
            clientProfile.setSignMethod("HmacSHA256");
            clientProfile.setHttpProfile(httpProfile);

            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);
            SendSmsRequest req = new SendSmsRequest();
            String sdkAppId = "1400955994";
            req.setSmsSdkAppId(sdkAppId);
            String signName = "上海华宸鑫通";
            req.setSignName(signName);
            String templateId = "2346233";
            req.setTemplateId(templateId);
            String code = RandomUtil.randomNumbers(Integer.parseInt(SecurityConstants.CODE_SIZE));
            String[] templateParamSet = {code, "3"};
            req.setTemplateParamSet(templateParamSet);
            String[] phoneNumberSet = {"+86" + mobile};
            req.setPhoneNumberSet(phoneNumberSet);
            // 发送短信
            SendSmsResponse res = client.SendSms(req);
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            SmsResponse smsResponse = objectMapper.readValue(SendSmsResponse.toJsonString(res), SmsResponse.class);
            if (!smsResponse.getSendStatusSet().get(0).getMessage().contains("send success")) {
//                return R.restResult(null, 3, smsResponse.getSendStatusSet().get(0).getMessage());
                return R.restResult(null, 3, "验证码发送失败");
            }
            log.info("短信发送成功：{}，内容是：{}", SendSmsResponse.toJsonString(res), code);
            redisTemplate.opsForValue().set(mobile, code, 180L, TimeUnit.SECONDS);
            return R.restResult(null, 0, "验证码发送成功");
        } catch (Exception e) {
            return R.restResult(null, 3, "验证码发送失败");
        }
    }
}
