package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("m_coupon_type")
@Schema(description = "优惠卷类型")
public class MCouponTypeEntity {
    @TableId(type = IdType.AUTO) // 使用数据库自增主键
    @Schema(description = "优惠券类型Id")
    private Long id;

    @Schema(description = "优惠券类型uuid")
    private String uuid;

    @Schema(description = "优惠券二维码的uuid")
    private String codeImage;

    @Schema(description = "优惠券标题")
    private String title;

    @Schema(description = "优惠券说明")
    private String subTitle;

    @Schema(description = "优惠券使用规则")
    private String description;

    @Schema(description = "优惠金额")
    private String price;

    @Schema(description = "是否上架：0否/1是")
    private Boolean isActive;

    @Schema(description = "推荐类型：0、不做推荐，1、新优惠券，2、热门优惠券")
    private Integer recommendedType;

    @Schema(description = "优惠类型类型：1、满减，2、折扣卷")
    private Integer discountMethod;

    @Schema(description = "有效期开始时间")
    private LocalDateTime validPeriodStart;

    @Schema(description = "有效期截止时间")
    private LocalDateTime validPeriodEnd;

    @Schema(description = "兑换开始时间")
    private LocalDateTime redeemPeriodStart;

    @Schema(description = "兑换截至时间")
    private LocalDateTime redeemPeriodEnd;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;

    @Schema(description = "网页标题")
    private String ruleTitle;
}
