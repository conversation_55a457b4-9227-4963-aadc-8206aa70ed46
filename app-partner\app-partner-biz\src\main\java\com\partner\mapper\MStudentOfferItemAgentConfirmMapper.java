package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.finance.CommissionAffirmDto;
import com.partner.entity.MStudentOfferItemAgentConfirmEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student_offer_item_agent_confirm】的数据库操作Mapper
* @createDate 2025-01-03 10:24:43
* @Entity com.partner.entity.MStudentOfferItemAgentConfirm
*/
@Mapper
public interface MStudentOfferItemAgentConfirmMapper extends BaseMapper<MStudentOfferItemAgentConfirmEntity> {

    public void insertAffirmOfferItem(@Param("CommissionAffirmList") List<MStudentOfferItemAgentConfirmEntity> commissionAffirmList);


    public int valiyAffirmOfferItem(@Param("valiyAffirmList")List<CommissionAffirmDto> valiyAffirmVoList);






}




