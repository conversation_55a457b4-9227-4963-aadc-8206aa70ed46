package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-13 19:02:43
 */

@Data
@TableName("m_settlement_bill")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_settlement_bill ")
public class MSettlementBillEntity extends Model<MSettlementBillEntity>{

  @Schema(description = "结算账单Id")
  private Long id;
 

  @Schema(description = "结算代理Id")
  private Long fkAgentId;
 

  @Schema(description = "代理合同结算账户Id")
  private Long fkAgentContractAccountId;
 

  @Schema(description = "结算账户币种")
  private String fkCurrencyTypeNum;

  @Schema(description = "对账单总金额（根据账户币种）")
  private BigDecimal amount;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
