package com.pmp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pmp.entity.AgentCommissionTypeAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentCommissionTypeAgentMapper extends BaseMapper<AgentCommissionTypeAgent> {

    /**
     * 根据代理id查询代理佣金类型
     * @param agentId
     * @return
     */
    AgentCommissionTypeAgent getAgentCommissionTypeAgentById(@Param("agentId") Long agentId);
}
