package com.coupon.controller;

import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.coupon.dto.CouponTakenDto;
import com.coupon.dto.FindTakenRecDto;
import com.coupon.entity.MCouponTypeEntity;
import com.coupon.service.ICouponManageService;
import com.coupon.service.impl.CouponTakenServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/couponTaken")
public class CouponTakenController {
    @Resource
    private CouponTakenServiceImpl couponTakenService;
    @Autowired
    private ICouponManageService couponManageService;

    @PostMapping("/getAllCouponType")
    @Inner(false)
    public R getAllCouponType(@RequestBody CouponTakenDto couponTakenDto) throws Exception {
        return R.ok(couponTakenService.getAllCouponType(couponTakenDto));
    }

    @PostMapping("/fetchCoupon")
    public R fetchCoupon(@RequestBody CouponTakenDto couponTakenDto) throws Exception {
        return R.ok(couponTakenService.fetchCoupon(couponTakenDto));
    }

    @PostMapping("/findTakenRec")
    public R findTakenRec(@RequestBody FindTakenRecDto findTakenRecDto) throws Exception {
        return couponTakenService.findTakenRec(findTakenRecDto);
    }

    @GetMapping("/getIntroduction")
    @Inner(false)
    public R getIntroduction() throws Exception {
        return couponTakenService.getIntroduction();
    }

    @GetMapping("/getCouponTypeById")
    @Inner(false)
    @Operation(summary = "获取优惠券详情", description = "获取优惠券详情-id或者uuid")
    public R<MCouponTypeEntity> getCouponTypeById(String id) {
        return R.ok(couponManageService.getCouponTypeById(id));
    }
}
