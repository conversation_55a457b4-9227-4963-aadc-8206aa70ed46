package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 信用卡支付状态枚举类
 */

@Getter
@AllArgsConstructor
public enum CreditCardPayStatusEnum {

    SUCCESS(1, "成功" ),
    FAIL(0, "失败" ),
    ;


    private Integer code;

    private String msg;


    public static CreditCardPayStatusEnum getEnumByCode(Integer code) {
        for (CreditCardPayStatusEnum value : CreditCardPayStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
