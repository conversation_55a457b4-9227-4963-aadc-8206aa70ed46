package com.partner.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.entity.PartnerRoleMenu;
import com.partner.entity.PartnerUserPartnerRole;
import com.partner.event.publisher.PartnerUserOfflineEventPublisher;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.AppSystemCenterMapper;
import com.partner.mapper.PartnerRoleMenuMapper;
import com.partner.mapper.PartnerUserPartnerRoleMapper;
import com.partner.service.PartnerRoleMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class PartnerRoleMenuServiceImpl extends ServiceImpl<PartnerRoleMenuMapper, PartnerRoleMenu> implements PartnerRoleMenuService {

    @Autowired
    private PartnerRoleMenuMapper partnerRoleMenuMapper;
    @Autowired
    private AppSystemCenterMapper appSystemCenterMapper;
    @Autowired
    private PartnerUserOfflineEventPublisher offlineEventPublisher;
    @Autowired
    private PartnerUserPartnerRoleMapper userPartnerRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRoleMenu(Long roleId, List<Long> menuIds) {
        if (Objects.isNull(roleId)) {
            return;
        }
        if (CollectionUtils.isEmpty(menuIds)) {
            throw new PartnerExceptionInfo(500, "角色权限不能为空");
//            this.remove(new LambdaQueryWrapper<PartnerRoleMenu>().eq(PartnerRoleMenu::getFkMenuId, roleId));
//            return;
        }

        Boolean isChange = Boolean.FALSE;

        FzhUser user = SecurityUtils.getUser();
        //当前角色目前已有的菜单权限
        List<Long> currentMenuIds = partnerRoleMenuMapper.selectList(new LambdaQueryWrapper<PartnerRoleMenu>()
                        .eq(PartnerRoleMenu::getFkPartnerRoleId, roleId))
                .stream().map(PartnerRoleMenu::getFkMenuId).collect(Collectors.toList());

        //获取新增的菜单权限
        List<Long> addMenuIds = menuIds.stream()
                .filter(menuId -> !currentMenuIds.contains(menuId)).collect(Collectors.toList());

        //获取需要删除的菜单权限
        List<Long> delMenuIds = currentMenuIds.stream()
                .filter(menuId -> !menuIds.contains(menuId)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addMenuIds)) {
            List<PartnerRoleMenu> saveList = addMenuIds.stream()
                    .map(menu -> {
                        PartnerRoleMenu roleMenu = new PartnerRoleMenu();
                        roleMenu.setFkPartnerRoleId(roleId);
                        roleMenu.setFkTenantId(Long.valueOf(user.getFkTenantId()));
                        roleMenu.setFkMenuId(menu);
                        roleMenu.setPermission(1);
                        roleMenu.setGmtCreate(new Date());
                        roleMenu.setGmtModified(new Date());
                        roleMenu.setGmtCreateUser(user.getLoginId());
                        roleMenu.setGmtModifiedUser(user.getLoginId());
                        return roleMenu;
                    }).collect(Collectors.toList());
            this.saveBatch(saveList);
            isChange = Boolean.TRUE;
        }

        if (CollectionUtils.isNotEmpty(delMenuIds)) {
            this.remove(new LambdaQueryWrapper<PartnerRoleMenu>()
                    .eq(PartnerRoleMenu::getFkPartnerRoleId, roleId)
                    .in(PartnerRoleMenu::getFkMenuId, delMenuIds));
            isChange = Boolean.TRUE;
        }

        //权限修改,用户下线
        if (isChange) {
            List<Long> partnerUserIds = userPartnerRoleMapper.selectList(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                            .eq(PartnerUserPartnerRole::getFkPartnerRoleId, roleId))
                    .stream()
                    .map(PartnerUserPartnerRole::getFkPartnerUserId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(partnerUserIds)) {
                offlineEventPublisher.publishPartnerUserOfflineEvent(partnerUserIds, Boolean.TRUE);
            }
        }
    }

    @Override
    public List<Long> getRoleMenuIds(Long roleId) {
        if (Objects.isNull(roleId)) {
            return Collections.emptyList();
        }
        return partnerRoleMenuMapper.selectList(new LambdaQueryWrapper<PartnerRoleMenu>()
                        .eq(PartnerRoleMenu::getFkPartnerRoleId, roleId))
                .stream()
                .map(PartnerRoleMenu::getFkMenuId)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getMenuPermissionByRoleIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        List<Long> menuIds = partnerRoleMenuMapper.selectList(new LambdaQueryWrapper<PartnerRoleMenu>()
                        .in(PartnerRoleMenu::getFkPartnerRoleId, roleIds))
                .stream()
                .map(PartnerRoleMenu::getFkMenuId)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());

        return appSystemCenterMapper.selectSystemMenuByIds(CollectionUtils.isEmpty(menuIds) ? Arrays.asList(0L) : menuIds)
                .stream()
                .map(systemMenu -> systemMenu.getPermissionKey())
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
    }
}
