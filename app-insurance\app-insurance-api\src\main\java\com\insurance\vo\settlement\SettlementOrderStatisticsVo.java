package com.insurance.vo.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/20
 * @Version 1.0
 * @apiNote:结算订单统计VO
 */
@Data
public class SettlementOrderStatisticsVo {

    @Schema(description = "已结算金额-人名币")
    private BigDecimal alreadySettledAmount;

    @Schema(description = "未结算金额-人名币")
    private BigDecimal unSettledAmount;

    @Schema(description = "已提交金额-人名币")
    private BigDecimal submittedAmount;

    public SettlementOrderStatisticsVo() {
        this.alreadySettledAmount = BigDecimal.ZERO;
        this.unSettledAmount = BigDecimal.ZERO;
        this.submittedAmount = BigDecimal.ZERO;
    }
}

