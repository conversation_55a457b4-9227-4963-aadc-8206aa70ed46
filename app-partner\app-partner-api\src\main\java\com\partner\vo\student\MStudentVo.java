package com.partner.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "返回学生管理首页")
public class MStudentVo {
    @Schema(description = "学生主键UUID")
    private String studentUUID;

    @Schema(description = "学生姓名")
    private String name;

    @Schema(description = "跟进人")
    private String followName;

    @Schema(description = "所属公司")
    private String fkCompanyId;

    @Schema(description = "首申国家")
    private String countryName;

    @Schema(description = "学生所有步骤")
    private List<MStudentStepList> studentStep;
    @Schema(description = "按步骤排序前2条")
    private List<MStudentStepList> studentStepTwoPairs;









}
