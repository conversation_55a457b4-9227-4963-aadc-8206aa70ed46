package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-23 10:05:36
 */

@Data
@TableName("m_feedback_order_reply")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_feedback_order_reply ")
public class MFeedbackOrderReplyEntity extends Model<MFeedbackOrderReplyEntity>{

  @Schema(description = "反馈工单回复Id")
  private Long id;
 

  @Schema(description = "反馈工单Id")
  private Long fkFeedbackOrderId;
 

  @Schema(description = "回复员工Id（若发起者的回复，该字段为NULL）")
  private Long fkStaffId;
 

  @Schema(description = "回复内容")
  private String reply;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
