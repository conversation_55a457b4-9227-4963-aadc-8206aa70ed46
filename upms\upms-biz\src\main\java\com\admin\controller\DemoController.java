/*
 *
 *      Copyright (c) 2018-2025, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the fzh developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 *
 */

package com.admin.controller;

import com.admin.service.SysFileService;
import com.common.core.dto.TransferRequest;
import com.common.core.util.HttpUtils;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
/**
 * <p>
 * 远程接口调用Demo
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/test")
public class DemoController {

	@Resource
	private HttpUtils httpUtils;

	private final SysFileService sysFileService;

	private static final String transferUrl="/middle-center/api/demo";

	@RequestMapping("/demo")
	@Inner(false)
	public String demo() {
		TransferRequest transferRequest = TransferRequest.builder().fromAccountId("张三").toAccountId("李四").transferPrice(new BigDecimal("100.00")).build();
		return httpUtils.sendPostRequest(transferRequest,transferUrl);
	}

	/**
	 * 通过id删除文件管理
	 * @param ids id 列表
	 * @param isPrivate 是否私有桶
	 * @return R
	 */
	@Operation(summary = "通过id删除文件管理", description = "通过id删除文件管理")
	@DeleteMapping
	@Inner(false)
	public R removeById(@RequestBody Long[] ids) {
		for (Long id : ids) {
			sysFileService.deleteFile(id,false);
		}
		return R.ok();
	}

	/**
	 * 上传文件
	 * @param file 资源
	 * @param isPrivate 是否私有桶
	 * @return R(/ admin / bucketName / filename)
	 */
	@PostMapping(value = "/upload")
	@Inner(false)
	public R upload(@RequestPart("file") MultipartFile file) {
		return sysFileService.uploadFile(file,false);
	}

	/**
	 * 获取文件
	 * @param bucket 桶名称
	 * @param fileName 文件空间/名称
	 * @param isPrivate 是否私有桶
	 * @param response
	 * @return
	 */
	@Inner(false)
	@GetMapping("/{bucket}/{fileName}")
	public void file(@PathVariable String bucket, @PathVariable String fileName, HttpServletResponse response) {
		sysFileService.getFile(bucket, fileName, response,false);
	}

}
