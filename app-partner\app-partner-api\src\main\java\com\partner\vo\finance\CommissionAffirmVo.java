package com.partner.vo.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "佣金确认名单-申请信息")
public class CommissionAffirmVo {

    private Long offerItemId;

    @Schema(description = "申请项目UUID")
    private String offerItemUUID;
    private String areaCountryName;

    private String areaCountryNameChn;

    @Schema(description = "学生名字")
    private String studentsName;

    @Schema(description = "学生生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;


    @Schema(description = "学校中文名称")
    private String institutionName;

    @Schema(description = "学校名称")
    private String institutionNameChn;

    private String fkCurrencyTypeNum;

    @Schema(description = "课程名称")
    private String courseName;


    private String stepName;

    @Schema(description = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    @Schema(description = "获得签证函日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date COETime;



    @Schema(description = "学费")
    private BigDecimal tuitionAmount;



}
