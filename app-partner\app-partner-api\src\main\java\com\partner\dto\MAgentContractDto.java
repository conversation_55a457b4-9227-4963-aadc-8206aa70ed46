package com.partner.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "代理合同")
public class MAgentContractDto  {

    @Schema(description = "学生代理合同Id")
    private Long id;

    @Schema(description = "学生代理Id-前端不传")
    private Long fkAgentId;

    @Schema(description = "2未签署/3待审核/4审核通过-合同未开始/6-审核通过-生效中/7-审核通过-已过期/-4审核驳回/")
    private Integer contractStatus;

    @Schema(description = "当前页")
    private Integer page;

    @Schema(description = "每页大小")
    private Integer pageSize;

}
