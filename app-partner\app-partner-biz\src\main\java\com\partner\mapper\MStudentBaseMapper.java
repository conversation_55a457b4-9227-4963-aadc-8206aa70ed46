package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.TeamDataSumDto;
import com.partner.entity.MStudentEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student】的数据库操作Mapper
* @createDate 2025-01-16 20:31:37
* @Entity com.partner.entity.MStudent
*/
@Mapper
@DS("saledb")
public interface MStudentBaseMapper extends BaseMapper<MStudentEntity> {


    int getStudentsCount(TeamDataSumDto params);

    List<Long> getStudentsLevelCount(TeamDataSumDto params);
}




