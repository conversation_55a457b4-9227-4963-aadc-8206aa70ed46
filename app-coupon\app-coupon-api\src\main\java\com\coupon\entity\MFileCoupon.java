package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("m_file_coupon")
@Schema(description = "文件列表")
public class MFileCoupon {
    @TableId(type = IdType.AUTO) // 使用数据库自增主键
    @Schema(description = "附件Id")
    private Long id;

    @Schema(description = "文件guid")
    private String fileGuid;

    @Schema(description = "源文件类型")
    private String fileTypeOrc;

    @Schema(description = "源文件名")
    private String fileNameOrc;

    @Schema(description = "目标文件名")
    private String fileName;

    @Schema(description = "目标文件路径")
    private String filePath;

    @Schema(description = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;


}
