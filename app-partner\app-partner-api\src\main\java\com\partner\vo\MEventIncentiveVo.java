package com.partner.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "返回公开奖励信息")
public class MEventIncentiveVo {

    @Schema(description="奖励信息ID")
    private Long eventIncentiveId;
    @Schema(description="奖励标题")
    private String eventIncentiveTitle;
    @Schema(description="学校提供商名称")
    private String institutionProviderName;

    @Schema(description="国家名称")
    private String areaCountryNames;
    @Schema(description="发布时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private LocalDateTime releaseTime;
    @Schema(description="状态 0计划/1结束/2取消/3延期")
    private String status;
    @Schema(description="多文件用逗号隔开")
    private String files;
    @Schema(description="查看人数")
    private int total;

}
