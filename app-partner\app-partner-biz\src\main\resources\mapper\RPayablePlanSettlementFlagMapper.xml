<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RPayablePlanSettlementFlagMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.RPayablePlanSettlementFlagEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="numSettlementBatch" column="num_settlement_batch" jdbcType="VARCHAR"/>
            <result property="fkTypeKey" column="fk_type_key" jdbcType="VARCHAR"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="fkCurrencyTypeNumAccount" column="fk_currency_type_num_account" jdbcType="VARCHAR"/>
            <result property="statusSettlement" column="status_settlement" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,num_settlement_batch,fk_type_key,
        fk_agent_id,fk_currency_type_num,fk_currency_type_num_account,
        status_settlement,gmt_create,gmt_create_user,
        gmt_modified,gmt_modified_user
    </sql>
</mapper>
