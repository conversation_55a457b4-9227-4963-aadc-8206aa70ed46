<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MHelpMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MHelpEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkParentHelpId" column="fk_parent_help_id" jdbcType="BIGINT"/>
            <result property="fkHelpTypeId" column="fk_help_type_id" jdbcType="BIGINT"/>
            <result property="keyCode" column="key_code" jdbcType="VARCHAR"/>
            <result property="showType" column="show_type" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="keyInfoName" column="key_info_name" jdbcType="VARCHAR"/>
            <result property="publicLevel" column="public_level" jdbcType="VARCHAR"/>
            <result property="viewOrder" column="view_order" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="getDetail" resultType="com.partner.vo.HelpVo">
        SELECT mHelp.* FROM ais_help_center.m_help mHelp  WHERE key_code='PARTNER' AND  FIND_IN_SET(13,public_level) LIMIT 100;

    </select>
</mapper>
