package com.coupon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GetAllCouponTypeVo {
    private Long couponTypeId;
    private String uuid;
    private String title;
    private String subTitle;
    private String description;
    private String price;
    private boolean isActive;
    // 是否失效
    private boolean isLapse = false;
    private int recommendedType;
    private int discountMethod;
    private LocalDateTime validPeriodStart;
    private LocalDateTime validPeriodEnd;

    private LocalDateTime redeemPeriodStart;
    private LocalDateTime redeemPeriodEnd;
    private long sort;
    // 配额
    private Integer quota;
    // 剩余配额
    private Integer remainderQuota;
    // 剩余优惠卷数量
    private Integer remainderNum;
    // 是否已经领取
    private boolean isTaken;

    // 是否预热
    private boolean isPreHot;

    private Integer allCouponNum;
    private Integer isTakenNum;
    private Integer isUsedNum;

    private String imageGuid;
    private String imagePath;
    private String codeImageGuid;
    private String codeImagePath;

    @Schema(description = "网页标题")
    private String ruleTitle;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
