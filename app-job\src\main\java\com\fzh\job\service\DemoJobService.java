package com.fzh.job.service;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

/**
 * <AUTHOR>
 * @date 2025-02-13
 */
@Slf4j
@Service
public class DemoJobService {

	@XxlJob("demoJob")
	public ReturnT<String> demoJobr() {
		XxlJobHelper.log("This is a demo job." + XxlJobHelper.getShardIndex());
		return SUCCESS;
	}

}