package com.insurance.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 用于“只发不等”的异步POST请求（Fire-and-Forget）。
 */
@Slf4j
public class FastFireAndForgetHttpClient {

    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    // 专用 OkHttpClient：连接快速，读取超短
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(2, TimeUnit.SECONDS)
            .writeTimeout(2, TimeUnit.SECONDS)
            .readTimeout(1, TimeUnit.SECONDS) // 超短读取
            .callTimeout(3, TimeUnit.SECONDS)
            .build();

    /**
     * 发起 POST 请求，但只关心是否成功送达，不等待响应体
     */
    public static void postJson(String url, String jsonBody) {
        RequestBody body = RequestBody.create(jsonBody, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        // 真正 fire-and-forget 行为
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (e.getMessage() != null && e.getMessage().toLowerCase().contains("timeout")) {
                    // 忽略 timeout 错误（对方可能还在处理）
                    log.info("[FireAndForget POST] 已发送，目标处理中（忽略超时）→ {}", url);
                } else {
                    log.error("[FireAndForget POST] 请求失败 → {}, 错误: {}", url, e.getMessage());
                }
            }

            @Override
            public void onResponse(Call call, Response response) {
                response.close(); // 不管结果
                log.debug("[FireAndForget POST] 请求成功送达 → {}", url);
            }
        });
    }
}
