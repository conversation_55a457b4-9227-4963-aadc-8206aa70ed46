package com.apps.mapper;

import com.apps.api.entity.SystemUserRoleEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface SystemUserRoleMapper extends BaseMapper<SystemUserRoleEntity> {

    /**
     * 根据角色编码获取用户id
     * @param roleCodes
     * @param platformId
     * @param platformCode
     * @return
     */
    List<Long> findUserIdByRoleCode(@Param("roleCodes") List<String> roleCodes,
                                    @Param("platformId") Long platformId,
                                    @Param("platformCode") String platformCode);
}




