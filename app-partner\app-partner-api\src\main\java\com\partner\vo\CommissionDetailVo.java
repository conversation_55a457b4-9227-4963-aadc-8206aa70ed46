package com.partner.vo;

import com.partner.util.MyDateUtils;
import com.partner.vo.base.LABELComboxVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.Date;
import java.util.List;


@Data
@Schema(description = "PMP佣金信息")
public class CommissionDetailVo {
    @Schema(description="学校ID")
    private Long institutionId;

    @Schema(description="学校名称")
    private String institutionName;
    @Schema(description="学校中文名称")
    private String institutionNameChn;

    @Schema(description = "学校封面图")
    private String fileKey;
    @Schema(description = "学校环境")
    private String institutionPic;
    @Schema(description = "学校封面")
    private String fileKeyCover;




    @Schema(description = "学校封面图")
    private String fileCommission;

    @Schema(description="学校所属国家")
    private Long countryId;
    @Schema(description="学校所属国家名称")
    private String countryName;
    @Schema(description="学校所属国家中文名称")
    private String countryNameChn;

    @Schema(description="佣金-标题(课程名称)")
    private String title;

    @Schema(description="代理佣金")
    private String agentCommission;

    @Schema(description="标签类型labelIds")
    private String labelIds;

    private int year;
    private Date gmtCreate;



    @Schema(description="标签类型")
    private List<LABELComboxVo> labelType;

    @Schema(description="列表详情")
    List<CommissionDetailInfo> commissionDetailInfo;

    public int getYear() {
        if(gmtCreate!=null){
            year=MyDateUtils.getYear(gmtCreate);
        }else {
            year=MyDateUtils.getYear(new Date());
        }
        return year;
    }

    public String getFileKey() {

        if(Strings.isEmpty(fileKey)) {
            fileKey=getInstitutionPic();
        }
        if(Strings.isEmpty(fileKey)) {
            fileKey=getFileKeyCover();
        }
        return fileKey;
    }
}
