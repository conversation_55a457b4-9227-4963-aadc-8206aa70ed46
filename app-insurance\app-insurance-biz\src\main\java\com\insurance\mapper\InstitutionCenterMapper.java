package com.insurance.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.insurance.vo.institution.AreaStateVo;
import com.insurance.vo.institution.CountryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@DS("institution")
@Mapper
public interface InstitutionCenterMapper {

    /**
     * 查询国家列表
     *
     * @return
     */
    List<CountryVo> selectCountryList();

    /**
     * 查询国家下的省/州
     *
     * @param countryId
     * @return
     */
    List<AreaStateVo> selectAreaState(@Param("countryId") Long countryId);
}
