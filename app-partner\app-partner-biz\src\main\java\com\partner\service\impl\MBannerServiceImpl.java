package com.partner.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.partner.dto.MBannerParamsDto;
import com.partner.entity.MBannerEntity;
import com.partner.enums.ConfigTypeEnum;
import com.partner.mapper.MBannerMapper;
import com.partner.service.MBannerService;
import com.partner.util.TencentCloudUtils;
import com.partner.vo.MBannerVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class MBannerServiceImpl extends ServiceImpl<MBannerMapper, MBannerEntity>
        implements MBannerService {
    private final MBannerMapper mbannerMapper;

    private final TencentCloudUtils tencentCloudUtils;

    @Override
    public List<MBannerVo> searchBanner(MBannerParamsDto params) {

        /*FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());*/
        params.setFkPlatformId(ConfigTypeEnum.PARTNER_PLATFORM_ID.key);

        String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        params.setMMageAddress(baseurl);

        List<MBannerVo>  result= mbannerMapper.searchBanner(params);

        return result;
    }
}
