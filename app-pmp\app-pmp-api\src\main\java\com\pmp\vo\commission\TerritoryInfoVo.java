package com.pmp.vo.commission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/26
 * @Version 1.0
 * @apiNote:
 */
@Data
public class TerritoryInfoVo {

    @Schema(description = "适用国家/地区")
    private List<PlanTerritoryVo> territories;

    @Schema(description = "territory特殊说明")
    private String territory;

    @Schema(description = "territory特殊说明-中文")
    private String territoryChn;

    @Schema(description = "佣金方案专业说明")
    private String course;

    @Schema(description = "佣金方案专业说明（中文）")
    private String courseChn;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "唯一key")
    private String key;

    @Schema(description = "代理单项佣金明细列表")
    private List<AgentCommissionListVo.AgentCommissionInfo> agentCommissionInfoList;
}
