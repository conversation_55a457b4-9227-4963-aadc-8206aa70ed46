package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保险金额配置表实体类
 *
 * <AUTHOR>
 * @since YYYY-MM-DD
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("u_product_type_year_amount")
@Schema(description = "保险金额配置表")
public class ProductTypeYearAmount extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "保单年份 (从1开始)")
    private Integer policyYear;

    @Schema(description = "计划类型 (Single, Couple, Family)")
    private String insuranceType;

    @Schema(description = "保险产品类型 (NIB_LOGIN, ALLIAN)")
    private String productType;

    @Schema(description = "保费金额")
    private BigDecimal insuranceAmount;

    @Schema(description = "产品类型ID")
    private Integer fkProductTypeId;

}