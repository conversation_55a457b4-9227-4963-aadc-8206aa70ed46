package com.partner.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * partner
 *
 * <AUTHOR>
 * @date 2024-11-22 10:42:54
 */
@Data
@TableName("s_news")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "partner")
public class SNewsEntity extends Model<SNewsEntity> {


	/**
	* 新闻Id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="新闻Id")
    private Long id;

	/**
	* 表名
	*/
    @Schema(description="表名")
    private String fkTableName;

	/**
	* 表Id
	*/
    @Schema(description="表Id")
    private Long fkTableId;

	/**
	* 新闻类型Id
	*/
    @Schema(description="新闻类型Id")
    private Long fkNewsTypeId;

	/**
	* 标题
	*/
    @Schema(description="标题")
    private String title;

	/**
	* 简介
	*/
    @Schema(description="简介")
    private String profile;

	/**
	* 描述内容
	*/
    @Schema(description="描述内容")
    private String description;

	/**
	* 描述内容（预览）
	*/
    @Schema(description="描述内容（预览）")
    private String descriptionPreview;

	/**
	* 描述内容手机
	*/
    @Schema(description="描述内容手机")
    private String descriptionM;

	/**
	* 描述内容手机（预览）
	*/
    @Schema(description="描述内容手机（预览）")
    private String descriptionMPreview;

	/**
	* 网页标题
	*/
    @Schema(description="网页标题")
    private String webTitle;

	/**
	* 网页Meta描述
	*/
    @Schema(description="网页Meta描述")
    private String webMetaDescription;

	/**
	* 网页Meta关键字
	*/
    @Schema(description="网页Meta关键字")
    private String webMetaKeywords;

	/**
	* 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
	*/
    @Schema(description="公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

	/**
	* 发布时间
	*/
    @Schema(description="发布时间")
    private LocalDate publishTime;

	/**
	* 生效时间
	*/
    @Schema(description="生效时间")
    private LocalDateTime effectiveStartTime;

	/**
	* 失效时间
	*/
    @Schema(description="失效时间")
    private LocalDateTime effectiveEndTime;

	/**
	* 推荐新闻类型，支持多选，逗号隔开，如：1,2,3
	*/
    @Schema(description="推荐新闻类型，支持多选，逗号隔开，如：1,2,3")
    private String fkNewsTypeIdRecommend;

	/**
	* 跳转URL
	*/
    @Schema(description="跳转URL")
    private String gotoUrl;

	/**
	* 发送电邮时间（发送给员工）
	*/
    @Schema(description="发送电邮时间（发送给员工）")
    private LocalDateTime sendEmailTime;

	/**
	* 发送邮件涉及相关条件JS
	*/
    @Schema(description="发送邮件涉及相关条件JS")
    private String sendEmailOfferItemSteps;

	/**
	* 发送电邮时间（发送给代理）
	*/
    @Schema(description="发送电邮时间（发送给代理）")
    private LocalDateTime sendEmailAgentTime;

	/**
	* 发送电邮时间（发送给所有代理）
	*/
    @Schema(description="发送电邮时间（发送给所有代理）")
    private LocalDateTime sendEmailAllAgentTime;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

	/**
	* 创建用户(登录账号)
	*/
    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

	/**
	* 修改用户(登录账号)
	*/
    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;
}