package com.partner.util;

import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class HeaderFooter extends PdfPageEventHelper {


    public void onEndPage(PdfWriter writer, Document document) {
        try {
            PdfContentByte cb = writer.getDirectContent();

            // 添加页眉图片
            String headfilename="template/header.png";
            byte[] headImage=getResourceAsBytes(headfilename);
            Image headerImg = Image.getInstance(headImage);
            float pageWidth = PageSize.A4.getWidth(); // 获取页面总宽度

            headerImg.scaleAbsolute(pageWidth, 100); // 调整大小
            headerImg.setAbsolutePosition(
                    (document.right() - document.left() - headerImg.getScaledWidth()) / 2 + document.leftMargin(),
                    document.top() + 10);
            cb.addImage(headerImg);

            // 添加页脚图片
            String footerfilename="template/footer.png";
            byte[] footerImage=getResourceAsBytes(footerfilename);
            Image footerImg = Image.getInstance(footerImage);
            footerImg.scaleAbsolute(pageWidth, 50); // 调整大小
            footerImg.setAbsolutePosition(
                    (document.right() - document.left() - footerImg.getScaledWidth()) / 2 + document.leftMargin(),
                    document.bottom() - 30);
            cb.addImage(footerImg);



        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public  byte[] getResourceAsBytes(String path) throws IOException {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(path);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            if (inputStream == null) {
                throw new IOException("Resource not found: " + path);
            }

            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toByteArray();
        }
    }

}
