package com.partner.dto.student;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "添加分配学生参数")
public class StudentApportionAddDto{


   /* @Schema(description = "租户Id")
    @NotNull(message = "租户Id不能为空")
    private Long tenantId;*/

    @Schema(description = "伙伴用户Id")
    @NotNull(message = "伙伴用户Id不能为空")
    private Long[] partnerUserId;

    @Schema(description = "学生UUID-分配")
    @NotNull(message = "学生UUID不能为空")
    private String[] studentUUIDArr;
    @Schema(description = "分配类型 0追加分配 1 重新分配")
    @NotNull(message = "分配类型不能为空")
    private Integer type;

    @Schema(description = "角色编码")
    private String roleCode;
}
