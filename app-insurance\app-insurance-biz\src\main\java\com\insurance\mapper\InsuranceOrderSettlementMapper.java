package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.insurance.entity.InsuranceOrderSettlement;
import com.insurance.vo.settlement.PayablePlanVo;
import com.insurance.vo.settlement.SettlementOrderStatisticsVo;
import com.insurance.vo.settlement.SettlementOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface InsuranceOrderSettlementMapper extends BaseMapper<InsuranceOrderSettlement> {

    /**
     * 获取应付计划列表
     *
     * @param payablePlanIds
     * @return
     */
    List<PayablePlanVo> getPayablePlanList(@Param("payablePlanIds") List<Long> payablePlanIds);

    /**
     * 获取结算中订单数
     *
     * @param agentId
     * @return
     */
    Integer selectSettlementProgressOrder(@Param("agentId") Long agentId);


    /**
     * 获取结算订单统计
     *
     * @param date
     * @return
     */
    SettlementOrderStatisticsVo getSettlementOrderStatistics(@Param("agentId") Long agentId,
                                                             @Param("date") String date);

    /**
     * 获取结算订单列表-根据日期和结算状态
     * @param agentId
     * @param statusList
     * @param date
     * @return
     */
    List<SettlementOrderVo> selectSettlementOrderByStatusAndDate(@Param("agentId") Long agentId,
                                                                 @Param("statusList") List<Integer> statusList,
                                                                 @Param("date") String date);
}
