package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.partner.dto.student.MStudentAddPublicDto;
import com.partner.entity.MAppStudentEntity;
import com.partner.entity.MAppStudentOfferItemEntity;
import com.partner.entity.SMediaAndAttachedEntity;
import com.partner.enums.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.MAppStudentMapper;
import com.partner.mapper.MAppStudentOfferItemMapper;
import com.partner.mapper.SMediaAndAttachedMapper;
import com.partner.service.MStudentPublicService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.GetStringUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@AllArgsConstructor
public class MStudentPublicServiceImpl implements MStudentPublicService {

    private final MAppStudentMapper mAppStudentMapper;

    private final MAppStudentOfferItemMapper mAppStudentOfferItemMapper;

    private final SMediaAndAttachedMapper sMediaAndAttachedMapper;
    @Override
    public Long addStudents(MStudentAddPublicDto dto) {

        if(dto.getList()!=null && dto.getList().size()>0){
            List<MAppStudentOfferItemEntity> offerItemList=dto.getList();
            Boolean flag=false;
            String msg="";
            for(MAppStudentOfferItemEntity itemtmp:offerItemList){
                if(itemtmp.getFkAreaCountryId()==null){
                    msg="申请计划国家不能为空！";
                    break;
                }
                if(itemtmp.getFkInstitutionId()==null){
                    msg="申请计划学校不能为空！";
                    break;
                }
            }
            if(flag){
                throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,msg);

            }
        }


        //创建学生
        MAppStudentEntity appStudent= BeanCopyUtils.objClone(dto,MAppStudentEntity::new);
        Long companyid=dto.getFkCompanyId()==null?3l:dto.getFkCompanyId();
        appStudent.setFkCompanyId(companyid);
        appStudent.setFkAgentId(dto.getFkAgentId());

        appStudent.setStatus(0);//草稿

        appStudent.setFkPlatformId(2l);
        appStudent.setFkPlatformCode("PARTNER");
        appStudent.setFkPlatformCreateUserId(dto.getPartnerUserId());


        appStudent.setGmtCreate(LocalDateTime.now());
        appStudent.setGmtCreateUser(dto.getPartnerUserId().toString());



        mAppStudentMapper.insert(appStudent);
        appStudent.setNum(GetStringUtils.getStudentNum(appStudent.getId()));
        mAppStudentMapper.updateById(appStudent);


        List<MAppStudentOfferItemEntity> list= dto.getList();
        if(ObjectUtil.isNotEmpty(list)){
            for(MAppStudentOfferItemEntity offerItemEntity: list){
                offerItemEntity.setFkAppStudentId(appStudent.getId());
                offerItemEntity.setIsAdditional(false);
                offerItemEntity.setStatusAdditional(0);//未提交
                offerItemEntity.setGmtCreate(LocalDateTime.now());
                offerItemEntity.setGmtCreateUser(dto.getPartnerUserId().toString());
                mAppStudentOfferItemMapper.insert(offerItemEntity);
            }
        }

        if(ObjectUtils.isNotEmpty(dto.getFileGuidArray())){
            String[] fileArry= dto.getFileGuidArray();
            for(String fileGuid:fileArry){
                SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                sMediaAndAttachedEntity.setFkFileGuid(fileGuid);
                sMediaAndAttachedEntity.setFkTableName("m_app_student");
                sMediaAndAttachedEntity.setFkTableId(appStudent.getId());
                sMediaAndAttachedEntity.setTypeKey("m_app_student_file");
                sMediaAndAttachedEntity.setRemark("PARTNER申请学生基础附件");
                sMediaAndAttachedEntity.setGmtCreate(LocalDateTime.now());
                sMediaAndAttachedEntity.setGmtCreateUser(dto.getPartnerUserId().toString());
                sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
            }
        }

        return dto.getId();
    }
}
