package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.student.MStudentSubAndAddOrEditDto;
import com.partner.entity.MAppStudentEntity;
import com.partner.entity.MStudentEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_app_student】的数据库操作Mapper
* @createDate 2025-03-28 11:50:26
* @Entity com.partner.entity.MAppStudent
*/
@Mapper
@DS("saledb")
public interface MAppStudentMapper extends BaseMapper<MAppStudentEntity> {

    MAppStudentEntity selectAgentAppOne(MStudentSubAndAddOrEditDto dto);


}




