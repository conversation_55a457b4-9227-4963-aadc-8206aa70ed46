package com.fzh.job;

import com.common.job.annotation.EnableXxlJob;
import com.common.security.annotation.EnableResourceServer;
import com.common.swagger.annotation.EnableDoc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableXxlJob
@EnableDoc("xxljob")
@EnableFeignClients(basePackages = {"com.admin.api.feign","com.apps.api.feign", "com.payment.feign"})
@EnableResourceServer
@EnableDiscoveryClient
@SpringBootApplication
public class AppJobCenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(AppJobCenterApplication.class, args);
    }

}
