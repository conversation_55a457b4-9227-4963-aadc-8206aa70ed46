package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.finance.DownloadMSettlementBillParamsDto;
import com.partner.dto.finance.MSettlementBillParamsDto;
import com.partner.dto.finance.paramsmapper.MSettlementBillSearchParams;
import com.partner.entity.MSettlementBillEntity;
import com.partner.vo.finance.DownLoadMSettlementZipVo;
import com.partner.vo.finance.MSettlementBillItemVo;
import com.partner.vo.finance.MSettlementBillVo;
import com.partner.vo.finance.MSettlementReportDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_settlement_bill】的数据库操作Mapper
* @createDate 2025-01-13 19:04:43
* @Entity com.partner.entity.MSettlementBill
*/
@Mapper
public interface MSettlementBillMapper extends BaseMapper<MSettlementBillEntity> {
    IPage<MSettlementBillVo> getMSettlementBillPage(Page page, @Param("query") MSettlementBillSearchParams searchParams);

    List<MSettlementBillItemVo> getMSettlementBillDetail(Long msettlementId);

    List<MSettlementReportDetail> getMSettlementReportDetail(MSettlementBillParamsDto dto);

    String getBase64Image(MSettlementBillParamsDto dto);


    List<DownLoadMSettlementZipVo>  searchMSettlementBillDownload(DownloadMSettlementBillParamsDto params);
    List<DownLoadMSettlementZipVo>  searchMSettlementBillNoFile(DownloadMSettlementBillParamsDto params);

}




