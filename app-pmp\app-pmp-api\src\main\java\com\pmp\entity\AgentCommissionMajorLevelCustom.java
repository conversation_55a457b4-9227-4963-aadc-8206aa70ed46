package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_agent_commission_major_level_custom")
public class AgentCommissionMajorLevelCustom extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "代理佣金Id")
    private Long fkAgentCommissionId;

    @Schema(description = "用户自定义课程等级Id")
    private Long fkMajorLevelCustomId;
}
