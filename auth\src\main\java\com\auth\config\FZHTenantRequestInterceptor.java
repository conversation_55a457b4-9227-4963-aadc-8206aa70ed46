//package com.auth.config;
//
//import feign.RequestInterceptor;
//import feign.RequestTemplate;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * @Author:Oliver
// * @Date: 2025/1/20  19:21
// * @Version 1.0
// */
////@Component
//@Slf4j
//public class FZHTenantRequestInterceptor implements RequestInterceptor {
//
//    @Override
//    public void apply(RequestTemplate template) {
//
//        // 将 tenantId 添加到请求头
////        String tenantId = TenantContext.getTenantId();
////        log.info("feign request tenantId:{}", tenantId);
//
//        // 动态添加租户 ID 到请求头
////        if (StringUtils.isNotBlank(tenantId)) {
////            template.header("tenantId", tenantId);
////        }
//        log.info("====feign自定义请求头tenantId");
//        template.header("tenantId", "2");
//    }
//}
