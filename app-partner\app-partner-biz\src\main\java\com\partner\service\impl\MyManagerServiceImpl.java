package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.MyParamsDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.SMediaAndAttachedEntity;
import com.partner.mapper.SMediaAndAttachedMapper;
import com.partner.service.MyManagerService;
import com.partner.util.UserInfoParamsUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
@Slf4j
@Service
@AllArgsConstructor
public class MyManagerServiceImpl implements MyManagerService {
    private final SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Override
    public boolean addOrUpdateMyProfilePhoto(MyParamsDto paramsDto) {
        boolean flag = true;
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        try{
            SMediaAndAttachedEntity sMediaAndAttachedEntity = sMediaAndAttachedMapper.selectOne(
                    new LambdaQueryWrapper<SMediaAndAttachedEntity>().eq(SMediaAndAttachedEntity::getFkTableName,"m_partner_user")
                            .eq(SMediaAndAttachedEntity::getFkTableId,userinfo.getPartnerUserId())
                            .eq(SMediaAndAttachedEntity::getTypeKey,"m_partner_user_photo")
                    ,false
            );

            if(ObjectUtil.isNotEmpty(sMediaAndAttachedEntity)){

                sMediaAndAttachedEntity.setFkFileGuid(paramsDto.getFileGuid());
                sMediaAndAttachedEntity.setGmtModified(LocalDateTime.now());
                sMediaAndAttachedEntity.setGmtModifiedUser(userinfo.getPartnerUserId().toString());
                sMediaAndAttachedMapper.updateById(sMediaAndAttachedEntity);

            }else {
                SMediaAndAttachedEntity sMediaAndAttachedInsert = new SMediaAndAttachedEntity();
                sMediaAndAttachedInsert.setFkFileGuid(paramsDto.getFileGuid());
                sMediaAndAttachedInsert.setFkTableName("m_partner_user");
                sMediaAndAttachedInsert.setFkTableId(userinfo.getPartnerUserId());
                sMediaAndAttachedInsert.setTypeKey("m_partner_user_photo");
                sMediaAndAttachedInsert.setGmtCreate(LocalDateTime.now());
                sMediaAndAttachedInsert.setGmtCreateUser(userinfo.getPartnerUserId().toString());
                sMediaAndAttachedMapper.insert(sMediaAndAttachedInsert);
            }

        }catch (Exception e){
            log.error("用户修改头像失败 {}",userinfo.getPartnerUserId());
            flag = false;
        }
        return flag;
    }
}
