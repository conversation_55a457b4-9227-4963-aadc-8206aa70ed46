package com.auth.rocketmq.producer;

import com.auth.rocketmq.msg.UserOfflineDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author:Oliver
 * @Date: 2025/3/20
 * @Version 1.0
 * @apiNote:用户下线消息发送者
 */
@Slf4j
@Component
public class UserOfflineProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    //定义发送的topic
    private static final String TOPIC = "user_offline_topic";


    /**
     * 发送异步消息
     *
     * @param offlineDto-消息体
     */
    public void sendUserOfflineMessage(UserOfflineDto offlineDto) {
        log.info("发送用户下线消息，offlineDto={},topic={}", offlineDto, TOPIC);
        rocketMQTemplate.asyncSend(TOPIC, offlineDto, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("用户下线消息发送成功，offlineDto={}，消息ID={}", offlineDto, sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("用户下线消息发送失败，offlineDto={}，异常信息={}", offlineDto, throwable.getMessage());
                // 这里可以做失败告警、日志记录等处理
            }
        });
    }
}
