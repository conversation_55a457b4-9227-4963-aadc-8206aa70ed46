<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MSettlementBillMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MSettlementBillEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkAgentContractAccountId" column="fk_agent_contract_account_id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="getMSettlementBillPage" resultType="com.partner.vo.finance.MSettlementBillVo">
        SELECT  mSettlementBill.id AS msettlementId,
                mSettlementBill.gmt_create ,
                mSettlementBill.amount AS amount,
                count(*) AS studentNum,
            (SELECT GROUP_CONCAT(rPayablePlanSettlement.status_settlement) statusSettlements
             FROM r_settlement_bill_settlement_installment rSettlementBill
             INNER JOIN ais_finance_center.r_payable_plan_settlement_installment  rPayablePlanSettlement ON rSettlementBill.fk_payable_plan_settlement_installment_id=rPayablePlanSettlement.id
             WHERE rSettlementBill.fk_settlement_bill_id=mSettlementBill.id   ) AS statusSettlements
        from m_settlement_bill mSettlementBill
        INNER JOIN m_settlement_bill_item  mSettlementBillItem
            ON mSettlementBill.id=mSettlementBillItem.fk_settlement_bill_id
        WHERE mSettlementBill.fk_agent_id=#{query.agentId}

        <if test=" query.gmtCreateStart != null and query.gmtCreateEnd != null  ">
            AND mSettlementBill.gmt_create >= #{query.gmtCreateStart} and mSettlementBill.gmt_create &lt;= #{query.gmtCreateEnd}
        </if>

        GROUP BY mSettlementBill.id,mSettlementBill.gmt_create ORDER BY mSettlementBill.gmt_create DESC
    </select>
    <select id="getMSettlementBillDetail" resultType="com.partner.vo.finance.MSettlementBillItemVo">
        SELECT * FROM m_settlement_bill_item mSettlementBillItem WHERE mSettlementBillItem.fk_settlement_bill_id=#{msettlementId}

    </select>
    <select id="getMSettlementReportDetail" resultType="com.partner.vo.finance.MSettlementReportDetail">
        SELECT
            uAreaCountry.name_chn 						AS  areaCountryNameChn,
            institution.name_chn              AS  institutionNameChn,
            student.name 											AS 	studentsName,
            institutionCourse.name 						AS  courseName,
            offerItem.opening_time 						AS  openingTime,
            mPayablePlan.tuition_amount 					AS  tuitionAmount,
            mPayablePlan.split_rate     AS  scale,
            settlementBillItem.fk_currency_type_num  	AS  fkCurrencyTypeNum,
            settlementBillItem.amount_actual  AS  amountActual,
            settlementBillItem.amount_exchange  AS amountExchange,
            mSettlementBill.fk_currency_type_num AS accountCurrencyTypeNum
        FROM 				 app_partner_center.m_settlement_bill_item settlementBillItem
                                 INNER JOIN   app_partner_center.m_settlement_bill mSettlementBill ON settlementBillItem.fk_settlement_bill_id=mSettlementBill.id
                                 INNER JOIN   ais_sale_center.m_payable_plan  mPayablePlan  ON settlementBillItem.fk_payable_plan_id=mPayablePlan.id  AND mPayablePlan.fk_type_key = 'm_student_offer_item'
                                 INNER JOIN   ais_sale_center.m_student_offer_item offerItem   ON  offerItem.id=mPayablePlan.fk_type_target_id
                                 INNER JOIN   ais_sale_center.m_student student ON student.id = offerItem.fk_student_id
                                 INNER JOIN   ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
                                 LEFT  JOIN   ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
                                 LEFT  JOIN   ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id
        WHERE fk_settlement_bill_id=#{msettlementId}

    </select>
    <select id="getBase64Image" resultType="java.lang.String">
        select signature from r_settlement_bill_signature where fk_settlement_bill_id=#{msettlementId}
    </select>
    <select id="searchMSettlementBillDownload" resultType="com.partner.vo.finance.DownLoadMSettlementZipVo">
        SELECT  mSettlementBill.id,
                mFilePartner.file_guid,
                mFilePartner.file_key,
                mFilePartner.file_name_orc,
                mAgent.name AS agentName

        FROM app_partner_center.m_settlement_bill mSettlementBill
        INNER JOIN ais_sale_center.m_agent mAgent ON mSettlementBill.fk_agent_id=mAgent.id
        INNER JOIN app_partner_center.s_media_and_attached sMediaAndAttached ON mSettlementBill.id=sMediaAndAttached.fk_table_id
        AND sMediaAndAttached.fk_table_name='m_settlement_bill'
        INNER JOIN app_file_center.m_file_partner mFilePartner ON mFilePartner.file_guid=sMediaAndAttached.fk_file_guid
        WHERE mSettlementBill.fk_agent_id=#{agentId}
        <if test=" gmtCreateStart != null and gmtCreateEnd != null  ">
            AND mSettlementBill.gmt_create >= #{gmtCreateStart} and mSettlementBill.gmt_create &lt;= #{gmtCreateEnd}
        </if>


    </select>
    <select id="searchMSettlementBillNoFile" resultType="com.partner.vo.finance.DownLoadMSettlementZipVo">
        SELECT  mSettlementBill.id,
        mFilePartner.file_guid,
        mFilePartner.file_key,
        mFilePartner.file_name_orc,
        mAgent.name AS agentName

        FROM app_partner_center.m_settlement_bill mSettlementBill
        INNER JOIN ais_sale_center.m_agent mAgent ON mSettlementBill.fk_agent_id=mAgent.id
        LEFT JOIN app_partner_center.s_media_and_attached sMediaAndAttached ON mSettlementBill.id=sMediaAndAttached.fk_table_id
        AND sMediaAndAttached.fk_table_name='m_settlement_bill'
        LEFT JOIN app_file_center.m_file_partner mFilePartner ON mFilePartner.file_guid=sMediaAndAttached.fk_file_guid
        WHERE mSettlementBill.fk_agent_id=#{agentId}
        <if test=" gmtCreateStart != null and gmtCreateEnd != null  ">
            AND mSettlementBill.gmt_create >= #{gmtCreateStart} and mSettlementBill.gmt_create &lt;= #{gmtCreateEnd}
        </if>
            AND mFilePartner.file_key IS NULL




    </select>


</mapper>
