package com.partner.vo;

import com.partner.entity.MLiveEntity;
import com.partner.util.MyDateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "返回直播")
public class MLivePageVo  {
    @Schema(description = "直播ID")
    private Long id;


    @Schema(description = "培训标题")
    private String title;


    @Schema(description = "课程编码")
    private String num;


    @Schema(description = "讲师中文名")
    private String teacherNameChn;

    @Schema(description = "直播详情")
    private String description;

    @Schema(description = "直播类型Id")
    private Long fkLiveTypeId;


    @Schema(description = "直播开始时间")
    private LocalDateTime liveTimeStart;

    @Schema(description = "直播倒计时天")
    private int dayNo=0;


    @Schema(description = "直播结束时间")
    private LocalDateTime liveTimeEnd;

    @Schema(description = "直播链接")
    private String liveUrl;


    @Schema(description = "回播链接")
    private String loopUrl;

    @Schema(description = "主图-桶地址")
    private String fileKey;
    @Schema(description = "附件-桶地址(用逗号隔开)")
    private String fileKeyFile;

    @Schema(description = "头像-用户信息")
    private List<AppointmentInfo> userinfo;
    @Schema(description = "预约数量")
    private long totaluser;

    @Schema(description = "当前用户预约状态：0未预约 1已预约")
    private int appointmentType;


    @Schema(description = "直播时间按天分组")
    private LocalDate liveDate;

    @Schema(description = "当前直播状态：0未开播 1直播中")
    private int inLive;

    @Schema(description = "当前直播准备播放状态：0未开播 1准备开播")
    private int prepareLive;



    @Schema(description = "是否最新直播：0直播列表 1是历史播放列表")
    private int isNewLive;
    @Schema(description = "是否有回放：0否/1是")
    private Boolean isLoop;


    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;



    public int getDayNo() {
        if(liveTimeStart!=null){
            Date datestart = Date.from(liveTimeStart.atZone(ZoneId.systemDefault()).toInstant());
            dayNo=MyDateUtils.differentDays(new Date(),datestart);
        }
        return dayNo;
    }


    public LocalDate getLiveDate() {
        if(liveTimeStart!=null){
            liveDate=liveTimeStart.toLocalDate();
        }
        return liveDate;
    }

    public int getInLive() {
        if(liveTimeStart!=null && liveTimeEnd!=null){
            if(liveTimeStart.isBefore(LocalDateTime.now()) && liveTimeEnd.isAfter(LocalDateTime.now()) ){
                inLive=1;
            }

        }

        return inLive;
    }

    public int getPrepareLive() {
        if(liveTimeStart!=null){
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime oneHourLater = now.plusHours(1);
            if(liveTimeStart.isAfter(LocalDateTime.now()) && liveTimeStart.isBefore(oneHourLater) ){
                prepareLive=1;
            }
        }


        return prepareLive;
    }
}
