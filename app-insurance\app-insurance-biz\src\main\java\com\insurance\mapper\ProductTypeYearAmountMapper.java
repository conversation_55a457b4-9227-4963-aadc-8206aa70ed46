package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.insurance.dto.order.InsurancePlanDTO;
import com.insurance.entity.ProductTypeYearAmount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote: 保险金额配置表 Mapper 接口
 */
@Mapper
public interface ProductTypeYearAmountMapper extends BaseMapper<ProductTypeYearAmount> {

    Integer getMaxYear(@Param("insurancePlanDTO") InsurancePlanDTO insurancePlanDTO);

}
