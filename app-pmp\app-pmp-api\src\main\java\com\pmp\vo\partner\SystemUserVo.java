package com.pmp.vo.partner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/3/4  10:53
 * @Version 1.0
 * partner用户
 */
@Data
public class SystemUserVo {
    @Schema(description = "系统用户ID")
    private Long userId;

    @Schema(description = "删除标记，0未删除，1已删除")
    private Integer isDelFlag;

    @Schema(description = "锁定标记，0未锁定，1已锁定，锁定后不允许登录")
    private Integer isLockFlag;
}
