package com.insurance.event;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/6/27
 * @Version 1.0
 */
public class SendNotifyMessageEvent extends ApplicationEvent {

    @Schema(description = "信用卡id")
    private Long creditCardId;

    @Schema(description = "消息类型:额度不足/支付失败/出账还款提醒/交易通知")
    private String messageType;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "付款时间")
    private Date payTime;

    public SendNotifyMessageEvent(Object source, Long creditCardId, String messageType, String orderNo, Date payTime) {
        super(source);
        this.creditCardId = creditCardId;
        this.messageType = messageType;
        this.orderNo = orderNo;
        this.payTime = payTime;
    }

    public Long getCreditCardId() {
        return creditCardId;
    }

    public void setPartnerUserIds(Long creditCardId) {
        this.creditCardId = creditCardId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

}
