package com.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付类型枚举
 */
@Getter
@AllArgsConstructor
public enum PayTypeEnum {

    WX_JSAPI("WX_JSAPI", "微信小程序支付"),
    ALI("ALI", "支付宝"),
    ;


    private String code;

    private String msg;


    public static PayTypeEnum getEnumByCode(String code) {
        for (PayTypeEnum value : PayTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
