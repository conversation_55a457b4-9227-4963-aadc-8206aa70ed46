package com.insurance.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.util.SecurityUtils;
import com.insurance.dto.client.SaveClientDto;
import com.insurance.entity.InsuranceOrder;
import com.insurance.entity.PartnerUserClient;
import com.insurance.enums.OrderStatusEnum;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.mapper.InsuranceOrderSettlementMapper;
import com.insurance.mapper.PartnerUserClientMapper;
import com.insurance.service.PartnerUserClientService;
import com.insurance.util.PartnerUserUtils;
import com.insurance.vo.insurance.client.ClientOrderVo;
import com.insurance.vo.insurance.client.ClientVo;
import com.insurance.vo.settlement.PayablePlanVo;
import com.insurance.vo.settlement.SettlementBillItemVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class PartnerUserClientServiceImpl extends ServiceImpl<PartnerUserClientMapper, PartnerUserClient> implements PartnerUserClientService {

    @Autowired
    private PartnerUserClientMapper partnerUserClientMapper;
    @Autowired
    private InsuranceOrderMapper orderMapper;
    @Autowired
    private InsuranceOrderSettlementMapper settlementMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveClient(SaveClientDto saveClientDto) {
        if (Objects.nonNull(saveClientDto.getId()) && saveClientDto.getId() > 0) {
            PartnerUserClient client = partnerUserClientMapper.selectById(saveClientDto.getId());
            if (Objects.nonNull(client)) {
                BeanUtils.copyProperties(saveClientDto, client);
                client.setGmtModified(new Date());
                client.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
                partnerUserClientMapper.updateById(client);
                return;
            }
        }
        PartnerUserClient client = new PartnerUserClient();
        BeanUtils.copyProperties(saveClientDto, client);
        client.setGmtCreate(new Date());
        client.setGmtModified(new Date());
        client.setGmtCreateUser(SecurityUtils.getUser().getLoginId());
        client.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
        client.setFkPartnerUserId(PartnerUserUtils.getCurrentPartnerId());
        client.setFkTenantId(Long.parseLong(SecurityUtils.getUser().getFkTenantId()));
        partnerUserClientMapper.insert(client);
    }

    @Override
    public List<ClientVo> getClientList(Integer progress, String keyword) {
        List<PartnerUserClient> clientList = partnerUserClientMapper.getClientList(PartnerUserUtils.getCurrentPartnerId(), progress, keyword);
        return clientList.stream()
                .map(client -> {
                    ClientVo clientVo = new ClientVo();
                    BeanUtils.copyProperties(client, clientVo);
                    //查询最新的购买记录
                    List<InsuranceOrder> insuranceOrders = orderMapper.selectList(new LambdaQueryWrapper<InsuranceOrder>()
                            .eq(InsuranceOrder::getInsurantEmail, client.getEmail())
                            .orderByDesc(InsuranceOrder::getGmtCreate)
                            .last("limit 1"));
                    clientVo.setProgress(0);
                    //最新进展:0-未购买(包含未购买和下单失败失败的);1-下单中;2-已购买;(1和2表示已购买)
                    if (CollectionUtils.isNotEmpty(insuranceOrders)) {
                        InsuranceOrder insuranceOrder = insuranceOrders.get(0);
                        //为请求和请求中-下单中
                        if (insuranceOrder.getOrderStatus().equals(OrderStatusEnum.PENDING.getCode())
                                || insuranceOrder.getOrderStatus().equals(OrderStatusEnum.PROGRESSING)) {
                            clientVo.setProgress(1);
                        }
                        if (insuranceOrder.getOrderStatus().equals(OrderStatusEnum.SUCCESS.getCode())) {
                            clientVo.setProgress(2);
                        }
                    }
                    return clientVo;
                }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteClient(Long id) {
        partnerUserClientMapper.deleteById(id);
    }

    @Override
    public PartnerUserClient getClient(Long id) {
        PartnerUserClient client = partnerUserClientMapper.selectById(id);
        if (Objects.nonNull(client)) {
            //查询最新的购买记录
            List<InsuranceOrder> insuranceOrders = orderMapper.selectList(new LambdaQueryWrapper<InsuranceOrder>()
                    .eq(InsuranceOrder::getInsurantEmail, client.getEmail())
                    .orderByDesc(InsuranceOrder::getGmtCreate)
                    .last("limit 1"));
            client.setProgress(0);
            //最新进展:0-未购买(包含未购买和下单失败失败的);1-下单中;2-已购买;(1和2表示已购买)
            if (CollectionUtils.isNotEmpty(insuranceOrders)) {
                InsuranceOrder insuranceOrder = insuranceOrders.get(0);
                //为请求和请求中-下单中
                if (insuranceOrder.getOrderStatus().equals(OrderStatusEnum.PENDING.getCode())
                        || insuranceOrder.getOrderStatus().equals(OrderStatusEnum.PROGRESSING)) {
                    client.setProgress(1);
                }
                if (insuranceOrder.getOrderStatus().equals(OrderStatusEnum.SUCCESS.getCode())) {
                    client.setProgress(2);
                }
            }
        }
        List<ClientOrderVo> orders = orderMapper.selectClientOrderList(client.getEmail());
        List<Long> payablePlanIds = orders.stream().map(ClientOrderVo::getPayablePlanId).collect(Collectors.toList());
        Map<Long, PayablePlanVo> planMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(payablePlanIds)) {
            planMap = settlementMapper.getPayablePlanList(payablePlanIds)
                    .stream()
                    .collect(Collectors.toMap(
                            PayablePlanVo::getPayablePlanId,
                            Function.identity()));
        }
        Map<Long, PayablePlanVo> finalPlanMap = planMap;
        orders.stream().forEach(item -> {
            BigDecimal commissionRate = finalPlanMap.getOrDefault(item.getPayablePlanId(), new PayablePlanVo()).getCommissionRate();
            if (Objects.nonNull(commissionRate)) {
                item.setRate(commissionRate);
                BigDecimal settlementAmount = item.getInsuranceAmount().multiply(commissionRate.divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                item.setSettlementAmount(settlementAmount);
                item.setSettlementCurrencyTypeNum(item.getFkCurrencyTypeNum());
            }
        });
        client.setOrders(orders);
        return client;
    }
}
