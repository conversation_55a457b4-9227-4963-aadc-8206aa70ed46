package com.insurance.vo.system;

import com.alibaba.cloud.commons.lang.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/8/1
 * @Version 1.0
 * @apiNote:
 */
@Data
public class SystemConfig {

    @Schema(description = "配置主键")
    private String configKey;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "数值一")
    private String value1;

    @Schema(description = "数值二")
    private String value2;

    @Schema(description = "数值三")
    private String value3;

    public SystemConfig() {
        super();
        this.configKey = "AXB_WECHAT_PAY_RATE_FORMULA";
        this.description = "微信收取费用公式参数配置，value1=exchange_rate_factor_formula, value2=service_fee_formula, value3=service_fee_factor_formula";
        this.value1 = "1.030";
        this.value2 = "100";
        this.value3 = "1.006";
    }

    @Schema(description = "数值一 BigDecimal")
    public BigDecimal getValue1Decimal() {
        return parseBigDecimalOrDefault(value1, new BigDecimal("1.030"));
    }

    @Schema(description = "数值二 BigDecimal")
    public BigDecimal getValue2Decimal() {
        return parseBigDecimalOrDefault(value2, new BigDecimal("100"));
    }

    @Schema(description = "数值三 BigDecimal")
    public BigDecimal getValue3Decimal() {
        return parseBigDecimalOrDefault(value3, new BigDecimal("1.006"));
    }

    private BigDecimal parseBigDecimalOrDefault(String value, BigDecimal defaultValue) {
        try {
            return StringUtils.isBlank(value) ? defaultValue : new BigDecimal(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }


}
