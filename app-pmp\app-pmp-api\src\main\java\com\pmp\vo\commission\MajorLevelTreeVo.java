package com.pmp.vo.commission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/19
 * @Version 1.0
 * @apiNote:
 */
@Data
public class MajorLevelTreeVo {

    @Schema(description = "课程等级Id")
    private Long levelId;

    @Schema(description = "等级名称")
    private String levelName;

    @Schema(description = "等级名称-中文")
    private String levelNameChn;

    @Schema(description = "是否通用等级：0否/1是")
    private Integer isGeneral;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @Schema(description = "子级课程等级")
    private List<MajorLevelTreeVo> children;

}
