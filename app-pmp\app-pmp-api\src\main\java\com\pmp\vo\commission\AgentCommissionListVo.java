package com.pmp.vo.commission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  16:47
 * @Version 1.0
 * @Description:代理佣金方案明细列表
 */
@Data
public class AgentCommissionListVo {

    @Schema(description = "代理单项佣金明细列表")
    private List<AgentCommissionInfo> agentCommissionInfoList;

    @Schema(description = "组合课程列表")
    private List<AgentCombinationInfo> agentCombinationList;

    @Schema(description = "整体佣金Bonus条件列表")
    private List<AgentBonusInfo> agentBonusList;


    public AgentCommissionListVo() {
        this.agentCommissionInfoList = new ArrayList<>();
        this.agentCombinationList = new ArrayList<>();
        this.agentBonusList = new ArrayList<>();
    }

    @Data
    @Schema(description = "代理佣金明细基础信息")
    public static class AgentCommissionBaseInfo {

        @Schema(description = "佣金明细ID-编辑传")
        private Long id;

        @Schema(description = "学校提供商Id-必传")
        private Long fkInstitutionProviderId;

        @Schema(description = "代理佣金方案Id-必传")
        private Long fkAgentCommissionPlanId;

        @Schema(description = "佣金类型：1课程/2阶梯/3组合")
        private Integer commissionType;

        @Schema(description = "佣金")
        private BigDecimal commission;

        @Schema(description = "佣金单位：%/CNY/等货币编号")
        private String commissionUnit;

        @Schema(description = "后续佣金")
        private BigDecimal followCommission;

        @Schema(description = "后续佣金单位：%/CNY/等货币编号")
        private String followCommissionUnit;

        @Schema(description = "备注")
        private String remarkNote;

        @Schema(description = "备注（本地语言）")
        private String remarkNoteNative;

        @Schema(description = "是否激活：0否/1是")
        private Integer isActive;

        @Schema(description = "标题")
        private String title;

        @Schema(description = "标题（本地语言）")
        private String titleNative;

        @Schema(description = "课程")
        private String course;

        @Schema(description = "后续备注")
        private String followRemarkNote;

        @Schema(description = "后续备注（中文）")
        private String followRemarkNoteNative;

        @Schema(description = "学校提供商佣金Id（继承，自己添加没有，用处：佣金提醒，学校提供商佣金明细删除时会统一删除继承）")
        private Long fkInstitutionProviderCommissionId;
    }

    @Data
    @Schema(description = "代理单项佣金明细信息")
    public static class AgentCommissionInfo extends AgentCommissionBaseInfo {

        @Schema(description = "课程等级Id")
        private Long levelId;

        @Schema(description = "等级名称")
        private String customName;

        @Schema(description = "等级名称-中文")
        private String customNameChn;

        @Schema(description = "后续学校提供商Id（不一致才需要选择）")
        private Long fkInstitutionProviderIdFollow;
    }

    //一个组合里面包含多个课程等级明细（commissionId）,通过packageKey区分组
    @Data
    @Schema(description = "代理组合课程列表信息")
    public static class AgentCombinationInfo {

        @Schema(description = "组合名称（同组相同）")
        private String packageName;

        @Schema(description = "组合名称（本地语言）（同组相同）")
        private String packageNameNative;

        @Schema(description = "组合key-保存过才会有,新增时没有,系统自动生成")
        private String packageKey;

        @Schema(description = "整个组合包含的所有课程等级列表-新增编辑不用传")
        private List<Long> levelIds;

        @Schema(description = "整个组合包含的所有课程等级名称列表-新增编辑不用传")
        private List<String> levelNames;

        @Schema(description = "包含的佣金明细列表")
        private List<AgentCommissionInfo> agentCommissionInfos;

    }

    @Data
    @Schema(description = "代理整体佣金Bonus列表信息")
    public static class AgentBonusInfo extends AgentCommissionBaseInfo {

        @Schema(description = "阶梯起始学生数")
        private Integer studentCountMin;

        @Schema(description = "阶梯结束学生数")
        private Integer studentCountMax;

        @Schema(description = "阶梯统计类型：0不追加/1从第1个学生开始计算")
        private Integer studentCountType;

        @Schema(description = "包含的课程等级列表")
        private List<Long> levelIds;

        @Schema(description = "整个组合包含的所有课程等级名称列表-新增编辑不用传")
        private List<String> levelNames;

    }

}
