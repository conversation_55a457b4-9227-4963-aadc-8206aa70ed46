package com.apps.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/1/14  14:56
 * @Version 1.0
 */
@Data
public class PrincipalInfo implements Serializable {

    @Schema(description = "是否通过认证")
    @JsonProperty("authenticated")
    private boolean authenticated;

    @Schema(description = "用户权限列表")
    @JsonProperty("authorities")
    private List<TokenVo.Authority> authorities;

    @Schema(description = "用户名称")
    @JsonProperty("name")
    private String name;

    @Schema(description = "用户详细信息")
    @JsonProperty("principal")
    private UserPrincipal principal;


    @Data
    public static class UserPrincipal {

        @Schema(description = "账号是否未过期")
        @JsonProperty("accountNonExpired")
        private boolean accountNonExpired;

        @Schema(description = "账号是否未被锁定")
        @JsonProperty("accountNonLocked")
        private boolean accountNonLocked;

        @Schema(description = "附加的用户属性信息")
        @JsonProperty("attributes")
        private Map<String, Object> attributes;

        @Schema(description = "用户权限列表")
        @JsonProperty("authorities")
        private List<TokenVo.Authority> authorities;

        @Schema(description = "凭据是否未过期")
        @JsonProperty("credentialsNonExpired")
        private boolean credentialsNonExpired;

        @Schema(description = "部门ID")
        @JsonProperty("deptId")
        private int deptId;

        @Schema(description = "账号是否启用")
        @JsonProperty("enabled")
        private boolean enabled;

        @Schema(description = "用户ID")
        @JsonProperty("id")
        private int id;

        @Schema(description = "用户姓名")
        @JsonProperty("name")
        private String name;

        @Schema(description = "用户手机号")
        @JsonProperty("phone")
        private String phone;

        @Schema(description = "用户名")
        @JsonProperty("username")
        private String username;
    }

}
