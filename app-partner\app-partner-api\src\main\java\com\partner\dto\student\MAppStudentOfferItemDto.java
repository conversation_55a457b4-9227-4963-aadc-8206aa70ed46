package com.partner.dto.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class MAppStudentOfferItemDto {

    @Schema(description = "appOfferItemid")
    private int id;

    @Schema(description = "申请学生Id")
    private Long fkAppStudentId;


    @Schema(description = "国家Id")
    @NotNull(message = "申请国家Id空!")
    private Long fkAreaCountryId;


    @Schema(description = "学校Id")
    @NotNull(message = "学校Id空!")
    private Long fkInstitutionId;

    @Schema(description = "课程Id")
    private Long fkInstitutionCourseId;


    @Schema(description = "开学时间")
    private LocalDate openingTime;

}
