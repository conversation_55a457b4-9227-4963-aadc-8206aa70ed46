package com.pmp.vo.partner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  10:53
 * @Version 1.0
 * partner用户
 */
@Data
public class PartnerUserVo {

    @Schema(description = "伙伴用户ID")
    private Long partnerUserId;

    @Schema(description = "系统用户ID")
    private Long userId;

    @Schema(description = "代理ID")
    private Long agentId;

    @Schema(description = "分公司ID")
    private Long companyId;

    @Schema(description = "关联的国家Id")
    private List<Long> countryIds;

    @Schema(description = "是否过滤国家")
    private Boolean filterCountry = Boolean.TRUE;
}
