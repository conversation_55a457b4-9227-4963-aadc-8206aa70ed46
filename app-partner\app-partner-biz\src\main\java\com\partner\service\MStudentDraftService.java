package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.student.MStudentDraftParamsDto;
import com.partner.vo.MStudentBaseVo;


public interface MStudentDraftService {
    /**
     * parner学生列表
     * @param page
     * @param dto
     * @return
     */
    IPage getPartnerStudents(Page page, MStudentDraftParamsDto dto);

    /**
     * 审核学生分页
     * @param page
     * @param dto
     * @return
     */
    IPage getCheckStudentsPage(Page page, MStudentDraftParamsDto dto);

    /**
     * 学生 阅读
     */
    void getReadingCheck();

    /**
     * 学生未阅读
     * @return
     */
    int getNoReadingNum();
    long deleteOne(Long id);


    MStudentBaseVo getStudentOne(Long id);
}
