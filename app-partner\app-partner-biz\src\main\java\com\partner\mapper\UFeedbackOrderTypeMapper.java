package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.UFeedbackOrderTypeEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【u_feedback_order_type】的数据库操作Mapper
* @createDate 2025-05-23 10:16:36
* @Entity com.partner.entity.UFeedbackOrderType
*/
@Mapper
@DS("aisplatformdb")
public interface UFeedbackOrderTypeMapper extends BaseMapper<UFeedbackOrderTypeEntity> {





}




