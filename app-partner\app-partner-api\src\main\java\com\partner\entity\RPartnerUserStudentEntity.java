package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-23 17:06:25
 */

@Data
@TableName("r_partner_user_student")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_partner_user_student ")
public class RPartnerUserStudentEntity extends Model<RPartnerUserStudentEntity>{

  @Schema(description = "伙伴用户和学生绑定关系Id")
  private Long id;
 

  @Schema(description = "租户Id")
  private Long fkTenantId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "学生Id")
  private Long fkStudentId;
 

  @Schema(description = "是否激活：0否/1是")
  private Boolean isActive;
 

  @Schema(description = "绑定时间")
  private LocalDateTime activeDate;
 

  @Schema(description = "取消绑定时间（下次绑定时，需要重新建立记录）")
  private LocalDateTime unactiveDate;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
