<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MBannerMapper">

    <sql id="Base_Column_List">
        banner.id,
        banner.fk_company_id,
        banner.fk_platform_id,
        banner.fk_platform_code,
        banner.fk_banner_type_id,
        banner.name,
        banner.weight,
        banner.jump_mode,
        banner.jump_url,
        banner.web_title,
        banner.web_meta_description,
        banner.status,
        banner.start_time,
        banner.end_time,
        banner.remark,
        banner.gmt_create,
        banner.gmt_create_user,
        banner.gmt_modified,
        banner.gmt_modified_user
    </sql>

    <select id="searchBanner" resultType="com.partner.vo.MBannerVo">
        SELECT  <include refid="Base_Column_List" />,
            (
            SELECT CONCAT(#{mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
            INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
            WHERE sAttached.fk_table_name='m_banner'    AND sAttached.fk_table_id=banner.id limit 1
            ) AS file_key

        FROM ais_platform_center.m_banner banner
        WHERE banner.status=2 and banner.fk_platform_id=#{fkPlatformId}  AND banner.start_time &lt; now() AND banner.end_time>now()
            ORDER BY banner.weight DESC
        LIMIT 100
    </select>
</mapper>
