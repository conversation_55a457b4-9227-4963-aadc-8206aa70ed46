package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.dto.partner.SavePartnerUserDto;
import com.apps.api.entity.SystemMenuEntity;
import com.apps.api.feign.RemoteAppsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.TeamDataSumDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.role.SavePartnerRoleDto;
import com.partner.dto.role.SaveUserDto;
import com.partner.entity.*;
import com.partner.enums.PartnerErrorEnum;
import com.partner.enums.PartnerMenuPermissionEnum;
import com.partner.event.publisher.PartnerUserOfflineEventPublisher;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.*;
import com.partner.service.*;
import com.partner.util.DateUtil;
import com.partner.util.RemoteErrorUtil;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.MPayablePlanVo;
import com.partner.vo.TeamMemberTreeVo;
import com.partner.vo.TeamMemberVo;
import com.partner.vo.my.MPayablePlanMyDetailVo;
import com.partner.vo.role.MenuTreeVo;
import com.partner.vo.role.PartnerUserDetailVo;
import com.partner.vo.role.TeamUserVo;
import com.partner.vo.role.UserRolePermission;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class PartnerRoleServiceImpl extends ServiceImpl<PartnerRoleMapper, PartnerRole> implements PartnerRoleService {

    @Autowired
    private PartnerRoleMapper partnerRoleMapper;
    @Autowired
    private PartnerRoleMenuService partnerRoleMenuService;
    @Autowired
    private AppSystemCenterMapper appSystemCenterMapper;
    @Autowired
    private PartnerUserPartnerRoleMapper partnerUserRoleMapper;
    @Autowired
    private PartnerUserPartnerRoleService partnerUserRoleService;
    @Autowired
    private MPartnerUserMapper partnerUserMapper;
    @Autowired
    private RPartnerUserSuperiorMapper partnerUserSuperiorMapper;
    @Autowired
    private RPartnerUserAreaCountryMapper userAreaCountryMapper;
    @Autowired
    private RPartnerUserAreaCountryService userAreaCountryService;
    @Autowired
    private RPartnerUserSuperiorService userSuperiorService;
    @Autowired
    private RemoteAppsService appsService;
    @Autowired
    private PartnerUserOfflineEventPublisher offlineEventPublisher;
    @Autowired
    private MStudentOfferItemMapper mStudentOfferItemMapper;
    @Autowired
    private MPartnerUserMapper mPartnerUserMapper;
    @Autowired
    private MStudentBaseMapper mStudentBaseMapper;
    @Autowired
    MPartnerUserService mPartnerUserService;
    @Autowired
    private MAgentContractService agentContractService;

    @Override
    public List<PartnerRole> roleList() {
        //默认角色+代理角色
        Long agentId = UserInfoParamsUtils.getCurrentAgentId();
        //默认角色
        List<PartnerRole> defaultRoles = partnerRoleMapper.selectList(new LambdaQueryWrapper<PartnerRole>()
                .eq(PartnerRole::getFkAgentId, 0)
                .orderByAsc(PartnerRole::getId));
        //代理角色
        List<PartnerRole> agentRoleList = partnerRoleMapper.selectList(new LambdaQueryWrapper<PartnerRole>()
                .eq(PartnerRole::getFkAgentId, agentId)
                .orderByDesc(PartnerRole::getId));
        defaultRoles.addAll(agentRoleList);

        defaultRoles.stream().forEach(role -> {
            if (Objects.isNull(role.getFkAgentId()) || role.getFkAgentId().equals(0L)) {
                role.setIsDefault(1);
            }
        });
        return defaultRoles;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRole(SavePartnerRoleDto roleDto) {
        //判断角色名称
        Long count = partnerRoleMapper.selectCount(new LambdaQueryWrapper<PartnerRole>()
                .eq(PartnerRole::getRoleName, roleDto.getRoleName())
                .eq(PartnerRole::getFkAgentId, UserInfoParamsUtils.getCurrentAgentId())
                .ne(Objects.nonNull(roleDto.getId()), PartnerRole::getId, roleDto.getId())
                .or()
                .eq(PartnerRole::getRoleName, roleDto.getRoleName())
                .eq(PartnerRole::getFkAgentId, 0L));
        if (count > 0) {
            throw new PartnerExceptionInfo(500, "角色名称已存在");
        }

        FzhUser fzhUser = SecurityUtils.getUser();
        //修改角色
        if (Objects.nonNull(roleDto.getId()) && roleDto.getId() > 0) {
            PartnerRole role = this.getById(roleDto.getId());
            if (Objects.isNull(role)) {
                throw new PartnerExceptionInfo(500, "角色不存在");
            }
            role.setRoleName(roleDto.getRoleName());
            role.setRoleDesc(roleDto.getRoleName());
            role.setGmtModified(new Date());
            role.setGmtModifiedUser(fzhUser.getLoginId());
            this.updateById(role);
            //保存角色权限
            partnerRoleMenuService.saveRoleMenu(role.getId(), roleDto.getMenuIds());
            return;
        }
        PartnerRole role = new PartnerRole();
        role.setFkTenantId(Long.valueOf(fzhUser.getFkTenantId()));
        role.setFkCompanyId(UserInfoParamsUtils.getCurrentCompanyId());
        role.setFkAgentId(UserInfoParamsUtils.getCurrentAgentId());
        role.setRoleName(roleDto.getRoleName());
        role.setRoleDesc(roleDto.getRoleName());
        role.setIsActive(1);
        role.setGmtCreate(new Date());
        role.setGmtCreateUser(fzhUser.getLoginId());
        role.setGmtModified(new Date());
        role.setGmtModifiedUser(fzhUser.getLoginId());
        this.save(role);
        //保存角色权限
        partnerRoleMenuService.saveRoleMenu(role.getId(), roleDto.getMenuIds());
    }

    @Override
    public List<MenuTreeVo> menuTree() {
        List<SystemMenuEntity> menuList = appSystemCenterMapper.selectByPlatformCode();
        if (CollectionUtils.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        // 将 SystemMenu 转成 MenuTreeVo
        Map<Long, MenuTreeVo> idToNodeMap = menuList.stream().collect(Collectors.toMap(
                SystemMenuEntity::getId,
                menu -> {
                    MenuTreeVo node = new MenuTreeVo();
                    BeanUtils.copyProperties(menu, node);
                    return node;
                }
        ));
        // 根据 parentId 分组 children
        Map<Long, List<MenuTreeVo>> parentIdGroup = idToNodeMap.values().stream()
                .collect(Collectors.groupingBy(node -> Optional.ofNullable(node.getFkParentMenuId()).orElse(0L)));
        // 将子节点填充到父节点
        idToNodeMap.values().forEach(node -> {
            List<MenuTreeVo> children = parentIdGroup.get(node.getId());
            if (children != null) node.setChildren(children);
        });
        // 返回顶级菜单（parentId为null或0）
        return parentIdGroup.getOrDefault(0L, new ArrayList<>());
    }

    @Override
    public UserRolePermission getUserRolePermission() {
        UserRolePermission rolePermission = UserRolePermission.builder()
                .agentId(UserInfoParamsUtils.getCurrentAgentId())
                .companyId(UserInfoParamsUtils.getCurrentCompanyId())
                .partnerUserId(UserInfoParamsUtils.getCurrentPartnerUserId())
                .systemUserId(SecurityUtils.getUser().getId())
                .roles(new ArrayList<>())
                .permissions(new ArrayList<>())
                .roleIds(new ArrayList<>())
                .roleNames(new ArrayList<>())
                .areaCountryIds(new ArrayList<>())
                .build();
        //角色集合
        List<Long> userRoleIds = partnerUserRoleMapper.selectList(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                        .eq(PartnerUserPartnerRole::getFkPartnerUserId, UserInfoParamsUtils.getCurrentPartnerUserId()))
                .stream().map(PartnerUserPartnerRole::getFkPartnerRoleId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userRoleIds)) {
            List<PartnerRole> partnerRoles = partnerRoleMapper.selectList(new LambdaQueryWrapper<PartnerRole>()
                    .in(PartnerRole::getId, userRoleIds));
            List<String> roleNames = partnerRoles.stream().map(PartnerRole::getRoleName).distinct().collect(Collectors.toList());
            //角色ID
            //角色名称
            rolePermission.setRoleNames(roleNames);
            rolePermission.setRoles(partnerRoles);
        }
        rolePermission.setRoleIds(userRoleIds);
        //权限
        List<String> permissions = partnerRoleMenuService.getMenuPermissionByRoleIds(userRoleIds);
        rolePermission.setPermissions(permissions);

        MPartnerUserEntity partnerUser = partnerUserMapper.selectOne(new LambdaQueryWrapper<MPartnerUserEntity>()
                .eq(MPartnerUserEntity::getId, UserInfoParamsUtils.getCurrentPartnerUserId())
                .eq(MPartnerUserEntity::getFkAgentId, UserInfoParamsUtils.getCurrentAgentId()), Boolean.FALSE);
        if (Objects.nonNull(partnerUser)) {
            rolePermission.setIsAdmin(partnerUser.getIsAdmin());
        }
        //获取最新的合同状态
        Integer contractStatus = agentContractService.getLatestAgentContractStatus();
        rolePermission.setLatestAgentContractStatus(contractStatus);
        //国家ID
        List<Long> countryIds = userAreaCountryMapper.selectList(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                        .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUser.getId()))
                .stream().map(RPartnerUserAreaCountryEntity::getFkAreaCountryId).collect(Collectors.toList());
        rolePermission.setAreaCountryIds(countryIds);
        return rolePermission;
    }

    @Override
    public PartnerRole roleDetail(Long roleId) {
        PartnerRole role = this.getById(roleId);
        if (Objects.nonNull(role)) {
            if (Objects.isNull(role.getFkAgentId()) || role.getFkAgentId().equals(0L)) {
                role.setIsDefault(1);
            }
            role.setMenuIds(partnerRoleMenuService.getRoleMenuIds(roleId));
            return role;
        }
        return null;
    }

    @Override
    public PartnerUserDetailVo getUserDetail(Long partnerUserId) {
        PartnerUserDetailVo userDetail = new PartnerUserDetailVo();
        MPartnerUserEntity partnerUser = partnerUserMapper.selectById(partnerUserId);
        if (Objects.isNull(partnerUser)) {
            throw new PartnerExceptionInfo(500, "用户不存在");
        }
        BeanUtils.copyProperties(partnerUser, userDetail);
        userDetail.setLockFlag(partnerUser.getIsActive() ? 0 : 1);
        //角色
        List<Long> roleIds = partnerUserRoleMapper.selectList(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                        .eq(PartnerUserPartnerRole::getFkPartnerUserId, partnerUserId))
                .stream().map(PartnerUserPartnerRole::getFkPartnerRoleId).collect(Collectors.toList());
        userDetail.setRoleIds(roleIds);
        //上司
        List<Long> superiorIds = partnerUserSuperiorMapper.selectList(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                        .eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, partnerUserId))
                .stream().map(RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior).collect(Collectors.toList());
        userDetail.setSuperiorIds(superiorIds);
        //国家
        List<Long> countryIds = userAreaCountryMapper.selectList(new LambdaQueryWrapper<RPartnerUserAreaCountryEntity>()
                        .eq(RPartnerUserAreaCountryEntity::getFkPartnerUserId, partnerUserId))
                .stream().map(RPartnerUserAreaCountryEntity::getFkAreaCountryId).distinct().collect(Collectors.toList());
        userDetail.setCountryIds(countryIds);

        //判断是否有团队管理权限
        List<String> permissionKeys = partnerUserRoleMapper.selectMenuPermissionKeyByPartnerUserId(partnerUserId);
        userDetail.setHasTeamPermission(permissionKeys.contains(PartnerMenuPermissionEnum.TEAM.getCode()));
        return userDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePartnerUser(SaveUserDto saveUserDto) {
        //同一个系统用户可以对应多个不同的伙伴用户
        Long count = partnerUserMapper.selectCount(new LambdaQueryWrapper<MPartnerUserEntity>()
                .eq(MPartnerUserEntity::getEmail, saveUserDto.getEmail())
                .eq(MPartnerUserEntity::getFkAgentId, UserInfoParamsUtils.getCurrentAgentId())
                .eq(MPartnerUserEntity::getFkCompanyId, UserInfoParamsUtils.getCurrentCompanyId())
                .ne(Objects.nonNull(saveUserDto.getPartnerUserId()), MPartnerUserEntity::getId, saveUserDto.getPartnerUserId()));
        if (count > 0) {
            throw new PartnerExceptionInfo(500, "邮箱已存在");
        }
        if (CollectionUtils.isEmpty(saveUserDto.getRoleIds())) {
            throw new PartnerExceptionInfo(500, "请选择角色");
        }
        //添加成员
        FzhUser user = SecurityUtils.getUser();
        Long partnerUserId = saveUserDto.getPartnerUserId();
        if (Objects.isNull(saveUserDto.getPartnerUserId()) || saveUserDto.getPartnerUserId().equals(0L)) {
            //调用APPS注册用户
            //获取系统默认角色-非伙伴角色
            Long systemDefaultRoleId = appSystemCenterMapper.selectSystemPartnerDefaultRoleId();
            SavePartnerUserDto partnerUserDto = SavePartnerUserDto.builder()
                    .platformCode(user.getFkFromPlatformCode())
                    .platformId(Long.parseLong(user.getFkFromPlatformId()))
                    .tenantId(Long.valueOf(user.getFkTenantId()))
                    .name(saveUserDto.getName())
                    .email(saveUserDto.getEmail())
                    .roleId(Objects.isNull(systemDefaultRoleId) ? 0L : systemDefaultRoleId)
                    .createUser(user.getLoginId())
                    .build();
            log.info("调用apps注册用户:{}", JSONObject.toJSONString(partnerUserDto));
            R<Long> result = appsService.registryUser(partnerUserDto);
            if (!result.isSuccess() || Objects.isNull(result.getData())) {
                log.error("调用apps注册用户失败:{}", JSONObject.toJSONString(result));
                String errorMsg = RemoteErrorUtil.extractInnerErrorMsg(JSONObject.toJSONString(result));
                throw new PartnerExceptionInfo(PartnerErrorEnum.REGISTER_USER_ERROR.errorCode,
                        PartnerErrorEnum.REGISTER_USER_ERROR.errorMessage +
                                (StringUtils.isBlank(errorMsg) ? "" : ":" + errorMsg));
            }
            Long userId = result.getData();
            MPartnerUserEntity partnerUser = MPartnerUserEntity.builder()
                    .fkTenantId(Long.valueOf(user.getFkTenantId()))
                    .fkUserId(userId)
                    .fkUserLoginId(saveUserDto.getEmail())
                    .name(saveUserDto.getName())
                    .email(saveUserDto.getEmail())
                    .isIdentityChecked(true)
                    .isModifiedPs(false)
                    .isActive(true)
                    .fkAgentId(UserInfoParamsUtils.getCurrentAgentId())
                    .fkCompanyId(UserInfoParamsUtils.getCurrentCompanyId())
                    .gmtCreateUser(user.getLoginId())
                    .gmtCreate(LocalDateTime.now())
                    .gmtModifiedUser(user.getLoginId())
                    .gmtModified(LocalDateTime.now()).build();
            partnerUserMapper.insert(partnerUser);
            partnerUserId = partnerUser.getId();
        }
        //保存伙伴角色
        partnerUserRoleService.savePartnerUserPartnerRole(partnerUserId, saveUserDto.getRoleIds());
        //保存上司
        userSuperiorService.savePartnerUserSuperior(partnerUserId, saveUserDto.getSuperiorIds());
        //保存国家
        userAreaCountryService.savePartnerCountry(partnerUserId, saveUserDto.getCountryIds());
        //用户下线
        offlineEventPublisher.publishPartnerUserOfflineEvent(Arrays.asList(partnerUserId), Boolean.FALSE);
    }

    @Override
    public List<TeamMemberVo> teamMemberList(Integer year) {
        year = DateUtil.getYearOrCurrentYear(year);
        //展示同一个代理下的全部成员,当前用户排在最前
        List<MPartnerUserEntity> userList = partnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>()
                .eq(MPartnerUserEntity::getFkAgentId, UserInfoParamsUtils.getCurrentAgentId())
                .last("ORDER BY CASE WHEN id = " + UserInfoParamsUtils.getCurrentPartnerUserId() + " THEN 0 ELSE 1 END, gmt_create DESC"));
        if (userList.isEmpty()) {
            return Collections.emptyList();
        }
        //填充角色信息
        List<Long> partnerUserIds = userList.stream().map(MPartnerUserEntity::getId).collect(Collectors.toList());
        // 1. 批量查询所有绑定关系
        List<PartnerUserPartnerRole> relationList = partnerUserRoleMapper.selectList(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                .in(PartnerUserPartnerRole::getFkPartnerUserId, partnerUserIds)
        );
        // 2. 提取出所有角色ID
        List<Long> roleIds = relationList
                .stream()
                .map(PartnerUserPartnerRole::getFkPartnerRoleId)
                .distinct()
                .collect(Collectors.toList());

        // 3. 查询所有角色详情
        Map<Long, PartnerRole> roleMap = partnerRoleMapper.selectBatchIds(CollectionUtils.isEmpty(roleIds) ? Arrays.asList(0L) : roleIds)
                .stream()
                .collect(Collectors.toMap(PartnerRole::getId, Function.identity()));
        // 4. 构建最终映射 Map<Long, List<PartnerRole>>
        Map<Long, List<PartnerRole>> userRoleMap = relationList.stream()
                .collect(Collectors.groupingBy(
                        PartnerUserPartnerRole::getFkPartnerUserId,
                        Collectors.mapping(rel -> roleMap.get(rel.getFkPartnerRoleId()), Collectors.toList())));

        Integer finalYear = year;
        List<TeamMemberVo> teamMembers = userList.stream().map(user -> {
            TeamMemberVo teamMemberVo = new TeamMemberVo();
            teamMemberVo.setPartnerUserId(user.getId());
            teamMemberVo.setUserId(user.getFkUserId());
            teamMemberVo.setName(user.getName());
            teamMemberVo.setLoginId(user.getFkUserLoginId());
            teamMemberVo.setGmtCreate(user.getGmtCreate());
            teamMemberVo.setIsYearCreate(false);
            teamMemberVo.setLockFlag(user.getIsActive() ? 0 : 1);
            teamMemberVo.setIsAdmin(user.getIsAdmin());
            // 设置是否为当年创建
            if (Objects.nonNull(user.getGmtCreate())) {
                int createYear = user.getGmtCreate().getYear();
                teamMemberVo.setIsYearCreate(createYear == finalYear);
            }
            //角色
            List<PartnerRole> roles = userRoleMap.getOrDefault(user.getId(), Collections.emptyList());
            teamMemberVo.setRoleNames(roles.stream().map(PartnerRole::getRoleName).collect(Collectors.toList()));
            teamMemberVo.setRoleIds(roles.stream().map(PartnerRole::getId).collect(Collectors.toList()));
            return teamMemberVo;
        }).collect(Collectors.toList());
        //填充团队业绩信息
        setPartnerUserPerformance(teamMembers, year);
        return teamMembers;
    }


    public void setPartnerUserPerformance(List<TeamMemberVo> teamMembervos, Integer year) {
        TeamDataSumDto params = new TeamDataSumDto();
        params.setRoleFlag(true);
        params.setYear(year);
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        for (TeamMemberVo teamMemberVo : teamMembervos) {
            List<Long> levelPartnerUserIdsTmp = new ArrayList<>();
            levelPartnerUserIdsTmp.add(teamMemberVo.getPartnerUserId());
            params.setPartnerUserId(teamMemberVo.getPartnerUserId());
            //只查自己
            params.setLevelPartnerUserIds(levelPartnerUserIdsTmp);
            params.setAgentId(userinfo.getAgentId());

            //完成用户数量
            List<Long> levelCompleteTotal = mStudentOfferItemMapper.getLevelCompleteStudens(params);
            teamMemberVo.setDealNum(levelCompleteTotal.size());

            //跟进中学生数量
            List<Long> followTotal = mStudentBaseMapper.getStudentsLevelCount(params);
            if (ObjectUtil.isNotEmpty(followTotal)) {
                if (ObjectUtil.isNotEmpty(levelCompleteTotal)) {
                    //去除已经完成的用户数量
                    followTotal.removeAll(levelCompleteTotal);
                    teamMemberVo.setFollowStudentNum(followTotal.size());
                } else {
                    teamMemberVo.setFollowStudentNum(followTotal.size());
                }
            } else {
                teamMemberVo.setFollowStudentNum(0);
            }
            //成交金额
            BigDecimal transactionAmount = new BigDecimal(0);
            List<MPayablePlanMyDetailVo> planAmountArr = mPartnerUserMapper.getSelfAmount(params);
            if (ObjectUtil.isNotEmpty(planAmountArr)) {
                List<MPayablePlanVo> resultlist = mPartnerUserService.getConvAmount(planAmountArr, userinfo);
                for (MPayablePlanVo planVo : resultlist) {
                    transactionAmount = transactionAmount.add(planVo.getToPayableAmount());
                }
            }
            teamMemberVo.setDealAmount(transactionAmount);
        }
    }

    @Override
    public void delRole(Long id) {
        Long count = partnerUserRoleMapper.selectCount(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                .eq(PartnerUserPartnerRole::getFkPartnerRoleId, id));
        if (count > 0) {
            throw new PartnerExceptionInfo(500, "有用户关联此角色,不能删除");
        }
        partnerRoleMapper.deleteById(id);
    }

    @Override
    public List<TeamMemberVo> getSuperiorList() {
        return partnerUserMapper.selectList(new LambdaQueryWrapper<MPartnerUserEntity>()
                        .eq(MPartnerUserEntity::getFkAgentId, UserInfoParamsUtils.getCurrentAgentId())
                        .orderByDesc(MPartnerUserEntity::getGmtCreate))
                .stream().map(partnerUserEntity -> {
                    TeamMemberVo teamMemberVo = new TeamMemberVo();
                    teamMemberVo.setPartnerUserId(partnerUserEntity.getId());
                    teamMemberVo.setName(partnerUserEntity.getName());
                    return teamMemberVo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<TeamUserVo> teamMemberTree(Integer year) {
        List<PartnerRole> roleList = roleList();
        List<TeamMemberVo> teamMemberList = teamMemberList(year);
        // 先把成员列表展开成 <roleId, TeamMemberVo> 的映射
        Map<Long, List<TeamMemberVo>> roleIdToMembersMap = teamMemberList.stream()
                .filter(member -> CollectionUtils.isNotEmpty(member.getRoleIds()))
                .flatMap(member -> member.getRoleIds().stream()
                        .map(roleId -> new AbstractMap.SimpleEntry<>(roleId, member)))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
        // 再按 roleList 构造结果，确保每个角色都有 entry
        Map<PartnerRole, List<TeamMemberVo>> roleToMembersMap = roleList.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        role -> roleIdToMembersMap.getOrDefault(role.getId(), Collections.emptyList()),
                        (v1, v2) -> v1,
                        LinkedHashMap::new
                ));
        // 构建一个 partnerUserId -> name 的映射表，方便查上司名称
        Map<Long, String> userIdNameMap = teamMemberList.stream()
                .collect(Collectors.toMap(TeamMemberVo::getPartnerUserId, TeamMemberVo::getName, (a, b) -> a));
        List<TeamUserVo> list = roleToMembersMap.keySet().stream().map(role -> {
            TeamUserVo teamUser = new TeamUserVo();
            teamUser.setRole(role);
            List<TeamMemberVo> members = roleToMembersMap.get(role);
            if (CollectionUtils.isNotEmpty(members)) {
                members.stream().forEach(member -> {
                    //查询上司
                    List<Long> superiorIds = userSuperiorService.getBaseMapper().selectList(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                                    .eq(RPartnerUserSuperiorEntity::getFkPartnerUserId, member.getPartnerUserId()))
                            .stream().map(RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior)
                            .distinct().collect(Collectors.toList());
                    member.setSuperiorIds(superiorIds);
                    // 映射上司ID -> 上司名称
                    List<String> superiorNames = superiorIds.stream()
                            .map(userIdNameMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    member.setSuperiorNames(superiorNames);
                    member.setSuperiorNamesStr(String.join(",", superiorNames));
                });
            }
            teamUser.setTeamMemberList(members);
            return teamUser;
        }).collect(Collectors.toList());
        return list;
    }

    private List<TeamMemberTreeVo> getSubordinates(Long superiorId, List<MPartnerUserEntity> teamMemberList) {
        // 查询该用户的直接下级
        List<RPartnerUserSuperiorEntity> relations = partnerUserSuperiorMapper.selectList(new LambdaQueryWrapper<RPartnerUserSuperiorEntity>()
                .eq(RPartnerUserSuperiorEntity::getFkPartnerUserIdSuperior, superiorId));

        List<TeamMemberTreeVo> subordinates = new ArrayList<>();

        // 如果有下级，递归查询
        for (RPartnerUserSuperiorEntity relation : relations) {
            Long partnerUserId = relation.getFkPartnerUserId();
            // 获取下级用户的基本信息
            MPartnerUserEntity teamMember = getTeamMemberByPartnerUserId(partnerUserId, teamMemberList);
            if (Objects.nonNull(teamMember)) {
                // 组装下级信息
                TeamMemberTreeVo subordinateTree = new TeamMemberTreeVo(teamMember.getFkUserId(), teamMember.getId(), teamMember.getName());
                // 递归查询该下级的下级
                subordinateTree.setChildren(getSubordinates(partnerUserId, teamMemberList));
                subordinates.add(subordinateTree);
            }
        }
        return subordinates;
    }

    private MPartnerUserEntity getTeamMemberByPartnerUserId(Long partnerUserId, List<MPartnerUserEntity> teamMemberList) {
        return teamMemberList.stream()
                .filter(teamMember -> teamMember.getId().equals(partnerUserId))
                .findFirst()
                .orElse(null);
    }

    public Set<Long> getAllPartnerUserIdsIteratively(TeamMemberTreeVo userTree) {
        Set<Long> partnerUserIds = new HashSet<>();
        // 用于缓存已访问的节点
        Set<Long> visitedIds = new HashSet<>();
        // 使用栈模拟递归过程
        Deque<TeamMemberTreeVo> stack = new ArrayDeque<>();
        // 将根节点压入栈
        stack.push(userTree);

        while (!stack.isEmpty()) {
            TeamMemberTreeVo currentNode = stack.pop();
            // 如果当前节点已访问过，则跳过
            if (visitedIds.contains(currentNode.getPartnerUserId())) {
                continue;
            }
            // 添加当前节点的 partnerUserId
            partnerUserIds.add(currentNode.getPartnerUserId());
            // 标记当前节点为已访问
            visitedIds.add(currentNode.getPartnerUserId());
            // 将所有子节点压入栈中
            if (currentNode.getChildren() != null) {
                for (TeamMemberTreeVo child : currentNode.getChildren()) {
                    stack.push(child);
                }
            }
        }

        return partnerUserIds;
    }
}
