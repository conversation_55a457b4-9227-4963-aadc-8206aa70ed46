package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.TeamDataSumDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.RPartnerUserSuperiorEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【r_partner_user_superior】的数据库操作Mapper
* @createDate 2025-01-14 21:05:44
* @Entity com.partner.entity.RPartnerUserSuperior
*/
@Mapper
public interface RPartnerUserSuperiorMapper extends BaseMapper<RPartnerUserSuperiorEntity> {

    List<Long> selectChildList(Long partUserId);

    List<Long> selectLevelChildList(@Param("partUserIds") List<Long> partUserId);

    UserInfoParams selectLevelSupList(@Param("partUserId") Long partUserId);


    int selectSuper(TeamDataSumDto params);
}




