package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RPayablePlanSettlementInstallmentEntity;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.dynamic.datasource.annotation.DS;
/**
* <AUTHOR>
* @description 针对表【r_payable_plan_settlement_installment】的数据库操作Mapper
* @createDate 2025-01-10 11:25:44
* @Entity com.partner.entity.RPayablePlanSettlementInstallment
*/
@Mapper
@DS("financedb")
public interface RPayablePlanSettlementInstallmentMapper extends BaseMapper<RPayablePlanSettlementInstallmentEntity> {







}




