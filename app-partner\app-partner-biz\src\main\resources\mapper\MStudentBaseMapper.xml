<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MStudentBaseMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MStudentEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="fkClientId" column="fk_client_id" jdbcType="BIGINT"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="lastName" column="last_name" jdbcType="VARCHAR"/>
            <result property="firstName" column="first_name" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="VARCHAR"/>
            <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
            <result property="fkAreaCountryIdNationality" column="fk_area_country_id_nationality" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameNationality" column="fk_area_country_name_nationality" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdGreenCard" column="fk_area_country_id_green_card" jdbcType="BIGINT"/>
            <result property="passportNum" column="passport_num" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdPassport" column="fk_area_country_id_passport" jdbcType="BIGINT"/>
            <result property="fkAreaCountryIdBirth" column="fk_area_country_id_birth" jdbcType="BIGINT"/>
            <result property="fkAreaStateIdBirth" column="fk_area_state_id_birth" jdbcType="BIGINT"/>
            <result property="fkAreaCityIdBirth" column="fk_area_city_id_birth" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameBirth" column="fk_area_country_name_birth" jdbcType="VARCHAR"/>
            <result property="fkAreaStateNameBirth" column="fk_area_state_name_birth" jdbcType="VARCHAR"/>
            <result property="fkAreaCityNameBirth" column="fk_area_city_name_birth" jdbcType="VARCHAR"/>
            <result property="mobileAreaCode" column="mobile_area_code" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="telAreaCode" column="tel_area_code" jdbcType="VARCHAR"/>
            <result property="tel" column="tel" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkAreaStateId" column="fk_area_state_id" jdbcType="BIGINT"/>
            <result property="fkAreaCityId" column="fk_area_city_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryName" column="fk_area_country_name" jdbcType="VARCHAR"/>
            <result property="fkAreaStateName" column="fk_area_state_name" jdbcType="VARCHAR"/>
            <result property="fkAreaCityName" column="fk_area_city_name" jdbcType="VARCHAR"/>
            <result property="zipcode" column="zipcode" jdbcType="VARCHAR"/>
            <result property="contactAddress" column="contact_address" jdbcType="VARCHAR"/>
            <result property="educationLevelType" column="education_level_type" jdbcType="VARCHAR"/>
            <result property="educationMajor" column="education_major" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdEducation" column="fk_area_country_id_education" jdbcType="BIGINT"/>
            <result property="fkAreaStateIdEducation" column="fk_area_state_id_education" jdbcType="BIGINT"/>
            <result property="fkAreaCityIdEducation" column="fk_area_city_id_education" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameEducation" column="fk_area_country_name_education" jdbcType="VARCHAR"/>
            <result property="fkAreaStateNameEducation" column="fk_area_state_name_education" jdbcType="VARCHAR"/>
            <result property="fkAreaCityNameEducation" column="fk_area_city_name_education" jdbcType="VARCHAR"/>
            <result property="fkInstitutionIdEducation" column="fk_institution_id_education" jdbcType="BIGINT"/>
            <result property="fkInstitutionNameEducation" column="fk_institution_name_education" jdbcType="VARCHAR"/>
            <result property="institutionTypeEducation" column="institution_type_education" jdbcType="VARCHAR"/>
            <result property="educationLevelType2" column="education_level_type2" jdbcType="VARCHAR"/>
            <result property="educationMajor2" column="education_major2" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdEducation2" column="fk_area_country_id_education2" jdbcType="BIGINT"/>
            <result property="fkAreaStateIdEducation2" column="fk_area_state_id_education2" jdbcType="BIGINT"/>
            <result property="fkAreaCityIdEducation2" column="fk_area_city_id_education2" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameEducation2" column="fk_area_country_name_education2" jdbcType="VARCHAR"/>
            <result property="fkAreaStateNameEducation2" column="fk_area_state_name_education2" jdbcType="VARCHAR"/>
            <result property="fkAreaCityNameEducation2" column="fk_area_city_name_education2" jdbcType="VARCHAR"/>
            <result property="fkInstitutionIdEducation2" column="fk_institution_id_education2" jdbcType="BIGINT"/>
            <result property="fkInstitutionNameEducation2" column="fk_institution_name_education2" jdbcType="VARCHAR"/>
            <result property="educationProject" column="education_project" jdbcType="INTEGER"/>
            <result property="educationDegree" column="education_degree" jdbcType="INTEGER"/>
            <result property="isComplexEducation" column="is_complex_education" jdbcType="BIT"/>
            <result property="complexEducationRemark" column="complex_education_remark" jdbcType="VARCHAR"/>
            <result property="highSchoolTestType" column="high_school_test_type" jdbcType="VARCHAR"/>
            <result property="highSchoolTestScore" column="high_school_test_score" jdbcType="VARCHAR"/>
            <result property="standardTestType" column="standard_test_type" jdbcType="VARCHAR"/>
            <result property="standardTestScore" column="standard_test_score" jdbcType="VARCHAR"/>
            <result property="masterTestType" column="master_test_type" jdbcType="VARCHAR"/>
            <result property="masterTestScore" column="master_test_score" jdbcType="VARCHAR"/>
            <result property="englishTestType" column="english_test_type" jdbcType="VARCHAR"/>
            <result property="englishTestScore" column="english_test_score" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="receivedApplicationDataDate" column="received_application_data_date" jdbcType="TIMESTAMP"/>
            <result property="sharedPath" column="shared_path" jdbcType="VARCHAR"/>
            <result property="conditionType" column="condition_type" jdbcType="VARCHAR"/>
            <result property="numGea" column="num_gea" jdbcType="VARCHAR"/>
            <result property="numIae" column="num_iae" jdbcType="VARCHAR"/>
            <result property="idIssue" column="id_issue" jdbcType="VARCHAR"/>
            <result property="idIssueInfo" column="id_issue_info" jdbcType="VARCHAR"/>
            <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
            <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

        <select id="getStudentsCount" resultType="java.lang.Integer">

                SELECT COUNT(*) total FROM (
                SELECT student.id  FROM ais_sale_center.m_student_offer_item offerItem
                <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionSql"/>
                INNER JOIN ais_sale_center.m_student student ON student.id=offerItem.fk_student_id
                WHERE 1=1
                <if test="year!=null and year != 0 ">
                        AND YEAR(offerItem.gmt_create)=#{year}
                </if>
                GROUP BY student.id) a
        </select>


        <select id="getStudentsLevelCount" resultType="java.lang.Long">
                SELECT  offerItem.fk_student_id FROM ais_sale_center.m_student_offer_item offerItem
                <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionSql"/>
                WHERE
                offerItem.fk_student_offer_item_step_id  IN(1,2,3,4,5,11,12,13,14,15,17)
                <if test="year!=null and year != 0 ">
                        AND YEAR(gmt_create)=#{year}
                </if>

                GROUP BY offerItem.fk_student_id
        </select>

</mapper>
