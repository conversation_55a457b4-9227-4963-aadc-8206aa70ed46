package com.fzh.job.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.MLiveEntity;
import com.partner.vo.job.UserAppointmentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MLiveMapper extends BaseMapper<MLiveEntity>{

    List<MLiveEntity> selectDetail();


    List<UserAppointmentVo> selectDetailList(@Param("liveId")  Long liveId);


}
