package com.partner.controller;

import com.apps.api.dto.partner.UpdatePartnerLockDto;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.ResetPartnerPasswordDto;
import com.partner.dto.TeamDataSumDto;
import com.partner.dto.agent.SwitchPartnerAgent;
import com.partner.dto.student.StudentApportionAddDto;
import com.partner.service.MPartnerUserService;
import com.partner.vo.agent.PartnerUserAgentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Tag(description = "mpartneruser", name = "小程序-partner用户信息")
@RestController
@RequestMapping("/mpartneruser")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MPartnerUserController {

    private final MPartnerUserService mpartnerUserService;

    @Operation(summary = "学生管理-查询分配人员列表", description = "学生管理-查询分配人员列表")
    @GetMapping("/getApportionPartnerUser")
    public R getApportionPartnerUser() {
        return R.ok(mpartnerUserService.getApportionPartnerUser());
    }

    @Operation(summary = "学生管理-分配人员", description = "学生管理-分配人员")
    @SysLog("学生管理-分配人员")
    @PostMapping("/putStudentApportionAddDto")
    public R putStudentApportionAddDto(@RequestBody @Valid StudentApportionAddDto addDto) {
        return R.ok(mpartnerUserService.putStudentApportionAddDto(addDto));
    }

    @Operation(summary = "我的团队-启用/停用账户", description = "我的团队-启用/停用账户")
    @SysLog("我的团队-启用/停用账户")
    @PostMapping("/lockUser")
    public R lockUser(@RequestBody @Valid UpdatePartnerLockDto memberDto) {
        mpartnerUserService.lockUser(memberDto);
        return R.ok("修改成功");
    }

    @Operation(summary = "我的团队-团队数据汇总", description = "我的团队-团队数据汇总")
    @SysLog("我的团队-团队数据汇总")
    @GetMapping("/getTeamDataSum")
    public R getTeamDataSum(@ParameterObject @Valid TeamDataSumDto params) {
        return R.ok(mpartnerUserService.getTeamDataSum(params));
    }

    @Operation(summary = "重置用户密码", description = "重置用户密码")
    @Inner(false)
    @PostMapping("/resetPartnerPassword")
    public R<String> resetPartnerPassword(@RequestBody @Valid ResetPartnerPasswordDto partnerPasswordDto) {
        mpartnerUserService.resetPartnerPassword(partnerPasswordDto);
        return R.ok("重置成功");
    }

    @Operation(summary = "获取当前用户关联的代理公司列表", description = "登录-获取当前用户关联的代理公司列表")
    @GetMapping("/getPartnerUserAgentList")
    public R<List<PartnerUserAgentVo>> getPartnerUserAgentList() {
        return R.ok(mpartnerUserService.getPartnerUserAgentList());
    }

    @Operation(summary = "获取当前用户登录的代理信息", description = "获取当前用户登录的代理信息")
    @GetMapping("/getCurrentPartnerUserAgent")
    public R<PartnerUserAgentVo> getCurrentPartnerUserAgent() {
        return R.ok(mpartnerUserService.getCurrentPartnerUserAgent());
    }

    @Operation(summary = "切换代理", description = "切换代理")
    @PostMapping("/switchPartnerAgent")
    public R<String> switchPartnerAgent(@RequestBody @Valid SwitchPartnerAgent switchPartnerAgent) {
        mpartnerUserService.switchPartnerAgent(switchPartnerAgent);
        return R.ok("切换成功");
    }
}
