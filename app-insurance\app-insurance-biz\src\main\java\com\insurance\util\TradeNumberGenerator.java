package com.insurance.util;

import com.insurance.constant.RedisConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @Author:Oliver
 * @Date: 2025/8/1
 * @Version 1.0
 * @apiNote:流水号生成器
 */
@Component
public class TradeNumberGenerator {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public String generateTradeNumber() {
        String today = LocalDate.now().format(DATE_FORMATTER);
        String redisKey = RedisConstant.TRADE_NO + today;

        // Redis 原子自增（首次设置自动过期 1 天）
        Long counter = redisTemplate.opsForValue().increment(redisKey);
        if (counter != null && counter == 1L) {
            redisTemplate.expire(redisKey, java.time.Duration.ofDays(1));
        }
        // 拼接最终编号
        return today + String.format("%06d", counter);
    }
}
