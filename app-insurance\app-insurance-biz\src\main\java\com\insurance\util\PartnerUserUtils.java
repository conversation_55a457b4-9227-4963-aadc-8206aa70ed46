package com.insurance.util;

import com.alibaba.fastjson.JSONObject;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.insurance.config.RedisService;
import com.insurance.constant.RedisConstant;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.mapper.PartnerCenterMapper;
import com.insurance.vo.partner.PartnerUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  15:38
 * @Version 1.0
 */
@Slf4j
@Component
public class PartnerUserUtils {

    private static RedisService redisService;
    private static PartnerCenterMapper partnerCenterMapper;

    /**
     * 通过 Spring 自动注入 RedisService，赋值给静态字段
     */
    @Autowired
    public void setRedisService(RedisService redisService) {
        PartnerUserUtils.redisService = redisService;
    }

    /**
     * 通过 Spring 自动注入 Mapper，赋值给静态字段
     */
    @Autowired
    public void setPartnerCenterMapper(PartnerCenterMapper partnerCenterMapper) {
        PartnerUserUtils.partnerCenterMapper = partnerCenterMapper;
    }

    public static PartnerUserInfo getPartnerInfo(Long systemUserId) {
        Object cachedData = redisService.hget(RedisConstant.PARTNER_USER, systemUserId.toString());
        if (Objects.isNull(cachedData)) {
            //重新查询一遍
            PartnerUserInfo partnerUserInfo = partnerCenterMapper.selectPartnerUserInfo(SecurityUtils.getUser().getId());
            if (Objects.isNull(partnerUserInfo)) {
                log.error("获取伙伴用户信息失败,系统用户ID:{}", systemUserId);
                throw new InsuranceGlobalException("获取伙伴用户信息失败");
            }
            String json = JSONObject.toJSONString(partnerUserInfo);
            redisService.hset(RedisConstant.PARTNER_USER, systemUserId.toString(), json, Duration.ofDays(1).toMillis());
            return partnerUserInfo;
        }
        if (cachedData instanceof String) {
            return JSONObject.parseObject((String) cachedData, PartnerUserInfo.class);
        }
        return (PartnerUserInfo) cachedData;
    }

    public static Long getCurrentAgentId() {
        FzhUser user = SecurityUtils.getUser();
        PartnerUserInfo partnerInfo = getPartnerInfo(user.getId());
        return partnerInfo.getAgentId();
    }

    public static Long getCurrentPartnerId() {
        FzhUser user = SecurityUtils.getUser();
        PartnerUserInfo partnerInfo = getPartnerInfo(user.getId());
        return partnerInfo.getPartnerUserId();
    }
}
