package com.partner.event;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/27
 * @Version 1.0
 */
public class PartnerUserOfflineEvent extends ApplicationEvent {

    @Schema(description = "伙伴用户IDS")
    private List<Long> partnerUserIds;

    @Schema(description = "是否强制下线")
    private Boolean isOffline;

    public PartnerUserOfflineEvent(Object source, List<Long> partnerUserIds, Boolean isOffline) {
        super(source);
        this.partnerUserIds = partnerUserIds;
        this.isOffline = isOffline;
    }

    public List<Long> getPartnerUserIds() {
        return partnerUserIds;
    }

    public void setPartnerUserIds(List<Long> partnerUserIds) {
        this.partnerUserIds = partnerUserIds;
    }

    public Boolean getIsOffline() {
        return isOffline;
    }

    public void setIsOffline(Boolean isOffline) {
        this.isOffline = isOffline;
    }
}
