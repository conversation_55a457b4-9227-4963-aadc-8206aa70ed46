package com.pmp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  17:41
 * @Version 1.0
 */
@Data
public class CheckLoginUserDto {

    @Schema(description = "账号")
    @NotNull(message = "账号不能为空")
    private String account;

    @Schema(description = "国家code")
    @NotNull(message = "国家code不能为空")
    private String code;
}
