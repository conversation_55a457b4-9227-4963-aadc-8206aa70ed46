package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.MAppStudentOfferItemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【m_app_student_offer_item】的数据库操作Mapper
* @createDate 2025-03-28 11:53:07
* @Entity com.partner.entity.MAppStudentOfferItem
*/
@Mapper
@DS("saledb")
public interface MAppStudentOfferItemMapper extends BaseMapper<MAppStudentOfferItemEntity> {

    int updateInfoById(MAppStudentOfferItemEntity entity);


}




