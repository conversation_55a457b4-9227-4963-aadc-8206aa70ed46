package com.insurance.constant;

/**
 * @Author:<PERSON>
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:
 */
public class RocketMqConstant {

    /**
     * 消息最大重试次数
     */
    public static final Integer MAX_RECONSUME_TIMES = 3;

    /**
     * 订单请求中消息主题
     */
    public static final String ORDER_PROGRESSING_TOPIC = "insurance_order_progressing_topic";

    /**
     * 订单请求中消息消费组
     */
    public static final String ORDER_PROGRESSING_TOPIC_CONSUMER_GROUP = "insurance_order_progressing_topic_consumer_group";

    /**
     * 订单请求中消息死信消费组
     */
    public static final String DLQ_ORDER_PROGRESSING_TOPIC_CONSUMER_GROUP = "dlq_insurance_order_progressing_topic_consumer_group";

    /**
     * 订单成功消息主题
     */
    public static final String ORDER_SUCCESS_TOPIC = "insurance_order_success_topic";

    /**
     * 订单成功消息消费组
     */
    public static final String ORDER_SUCCESS_TOPIC_CONSUMER_GROUP = "insurance_order_success_topic_consumer_group";

    /**
     * 订单成功消息死信消费组
     */
    public static final String DLQ_ORDER_SUCCESS_TOPIC_CONSUMER_GROUP = "dlq_insurance_order_success_topic_consumer_group";

    /**
     * 订单失败消息主题
     */
    public static final String ORDER_FAIL_TOPIC = "insurance_order_fail_topic";

    /**
     * 订单失败消息消费组
     */
    public static final String ORDER_FAIL_TOPIC_CONSUMER_GROUP = "insurance_order_fail_topic_consumer_group";

    /**
     * 订单失败消息死信消费组
     */
    public static final String DLQ_ORDER_FAIL_TOPIC_CONSUMER_GROUP = "dlq_insurance_order_fail_topic_consumer_group";

    /**
     * 自动下单消息主题
     */
    public static final String AUTOMATIC_ORDERING_TOPIC = "insurance_automatic_ordering_topic";

    /**
     * 自动下单消息消费组
     */
    public static final String AUTOMATIC_ORDERING_TOPIC_CONSUMER_GROUP = "insurance_automatic_ordering_topic_consumer_group";

    /**
     * 自动下单消息死信消费组
     */
    public static final String DLQ_AUTOMATIC_ORDERING_TOPIC_CONSUMER_GROUP = "dlq_insurance_automatic_ordering_topic_consumer_group";

    /**
     * 创建应收应付主题
     */
    public static final String RECEIVABLE_AND_PAYABLE_TOPIC = "insurance_receivable_and_payable_topic";

    /**
     * 自动下单消息主题
     */
    public static final String CREDIT_CARD_ORDERING_TOPIC = "insurance_credit_card_ordering_topic";

    /**
     * 自动下单消息主题
     */
    public static final String ORDER_CLOSE_TOPIC = "insurance_order_close_topic";
}
