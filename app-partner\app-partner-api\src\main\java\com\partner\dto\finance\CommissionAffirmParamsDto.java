package com.partner.dto.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "代理确认-参数")
public class CommissionAffirmParamsDto {
   /* @Valid
    @NotBlank(message = "代理UUID不能为空")
    private String agentUUID;*/

   /* @Valid
    @NotNull(message = "伙伴用户（确认人）不能为空")
    private Long partnerUserId;*/


    @Schema(description = "申请项目信息")
    @Valid
    @NotNull(message = "申请项目不能为空")
    List<CommissionAffirmDto> commissionAffirmVoList;
}
