<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.SNewsMapper">

    <resultMap id="sNewsMap" type="com.partner.entity.SNewsEntity">
        <id property="id" column="id"/>
        <result property="fkTableName" column="fk_table_name"/>
        <result property="fkTableId" column="fk_table_id"/>
        <result property="fkNewsTypeId" column="fk_news_type_id"/>
        <result property="title" column="title"/>
        <result property="profile" column="profile"/>
        <result property="description" column="description"/>
        <result property="descriptionPreview" column="description_preview"/>
        <result property="descriptionM" column="description_m"/>
        <result property="descriptionMPreview" column="description_m_preview"/>
        <result property="webTitle" column="web_title"/>
        <result property="webMetaDescription" column="web_meta_description"/>
        <result property="webMetaKeywords" column="web_meta_keywords"/>
        <result property="publicLevel" column="public_level"/>
        <result property="publishTime" column="publish_time"/>
        <result property="effectiveStartTime" column="effective_start_time"/>
        <result property="effectiveEndTime" column="effective_end_time"/>
        <result property="fkNewsTypeIdRecommend" column="fk_news_type_id_recommend"/>
        <result property="gotoUrl" column="goto_url"/>
        <result property="sendEmailTime" column="send_email_time"/>
        <result property="sendEmailOfferItemSteps" column="send_email_offer_item_steps"/>
        <result property="sendEmailAgentTime" column="send_email_agent_time"/>
        <result property="sendEmailAllAgentTime" column="send_email_all_agent_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtCreateUser" column="gmt_create_user"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtModifiedUser" column="gmt_modified_user"/>
    </resultMap>
    <select id="getSNewsListPage"
            resultType="com.partner.vo.SNewsListVo">
        SELECT snews.id,
               snews.title,
               snews.profile,
               snews.description,
               snews.publish_time,
               snews.web_title,
               snews.web_meta_description,
               (SELECT institutionFile.file_key FROM ais_file_center.m_file_institution institutionFile
                         INNER JOIN ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
                WHERE sAttached.fk_table_name = 's_news' AND sAttached.fk_table_id = snews.id AND sAttached.type_key = 'institution_news_title_pic' LIMIT 1
                ) AS fileKey
        FROM ais_institution_center.s_news snews
        WHERE 1=1

        <if test="query.countryId != null or query.institutionId != null">
            AND id IN(
                SELECT fk_news_id FROM ais_institution_center.r_news_type rNewsType
                WHERE
                <if test="query.countryId != null">
                    (rNewsType.fk_table_name='u_area_country' AND rNewsType.fk_table_id=#{query.countryId} )
                </if>
               <if test="query.countryId != null and query.institutionId == null">
                OR

                   (rNewsType.fk_table_name='m_institution' AND rNewsType.fk_table_id
                        IN (
                            select id from ais_institution_center.m_institution WHERE fk_area_country_id=#{query.countryId}
                        )


                   )
                </if>


                <if test="query.countryId != null and query.institutionId != null">OR</if>
                <if test="query.institutionId != null">
                    (rNewsType.fk_table_name='m_institution' AND rNewsType.fk_table_id=#{query.institutionId} )
                </if>
            )

        </if>


        <if test="query.newsText != null and query.newsText != '' ">
            AND position(#{query.newsText,jdbcType=VARCHAR} IN snews.title)
        </if>
        AND
        FIND_IN_SET(13, snews.public_level)

        ORDER BY snews.gmt_create DESC


    </select>

    <select id="getCountryCombox"
            resultType="com.partner.vo.combox.CountryCombox">
        SELECT DISTINCT uAreaCountry.id AS areaCountryId,
                        uAreaCountry.name_chn AS areaCountryName
        FROM ais_institution_center.r_news_type rNewsType
                 INNER JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id = rNewsType.fk_table_id
        WHERE rNewsType.fk_news_id IN (select id from ais_institution_center.s_news   WHERE FIND_IN_SET(13, public_level))

         AND rNewsType.fk_table_name = 'u_area_country' AND uAreaCountry.id NOT IN(1)


        ORDER BY uAreaCountry.view_order DESC
        LIMIT 100

    </select>

    <select id="getInstitutionCombox"
            resultType="com.partner.vo.combox.InstitutionCombox">
        SELECT DISTINCT institution.id AS institutionId,
                        institution.name_chn AS institutionName
        FROM ais_institution_center.r_news_type rNewsType
                 INNER JOIN ais_institution_center.m_institution institution ON institution.id = rNewsType.fk_table_id
        WHERE (rNewsType.fk_news_id IN (select id from ais_institution_center.s_news   WHERE FIND_IN_SET(13, public_level))

            AND rNewsType.fk_table_name = 'm_institution')

        LIMIT 100

    </select>
    <select id="getByDetail" resultType="com.partner.entity.SNewsEntity">
        SELECT * FROM ais_institution_center.s_news WHERE id=#{id}
    </select>


</mapper>