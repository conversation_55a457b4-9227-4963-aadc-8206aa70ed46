package com.insurance.dto.file;

import com.insurance.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppFileCenter extends BaseEntity {

    @Schema(description = "文件guid")
    private String fileGuid;

    @Schema(description = "源文件类型")
    private String fileTypeOrc;

    @Schema(description = "源文件名")
    private String fileNameOrc;

    @Schema(description = "目标文件名")
    private String fileName;

    @Schema(description = "目标文件路径")
    private String filePath;

    @Schema(description = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;
}
