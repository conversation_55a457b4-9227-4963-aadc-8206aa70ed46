package com.coupon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.common.core.util.R;
import com.coupon.dto.AddCouponTypeDto;
import com.coupon.dto.CouponQuotaDto;
import com.coupon.dto.CouponTakenRecDto;
import com.coupon.dto.CouponTypeDto;
import com.coupon.entity.MCouponTypeEntity;
import com.coupon.vo.GetAllCouponQuotaVo;
import com.coupon.vo.GetAllCouponTypeVo;
import com.coupon.vo.UploadAttached;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ICouponManageService {
    Long addNewCouponType(AddCouponTypeDto addCouponTypeDto) throws Exception;

    IPage<GetAllCouponTypeVo> getAllCouponType(CouponTypeDto couponTypeDto) throws Exception;

    List<UploadAttached> uploadAttached(MultipartFile[] files)throws Exception;

    void deleteCouponType(Long id) throws Exception;

    void updateCouponType(CouponTypeDto couponTypeDto) throws Exception;

    void addCouponQuota(CouponQuotaDto couponQuotaDto) throws Exception;

    IPage<GetAllCouponQuotaVo> getAllCouponQuoTa(CouponQuotaDto couponQuotaDto) throws Exception;

    void deleteCouponQuota(Long id) throws Exception;

    void updateCouponQuota(CouponQuotaDto couponQuotaDto) throws Exception;

    R couponTakenRec(CouponTakenRecDto couponTakenRecDto)throws Exception;

    /**
     * 获取优惠券详情
     * @param id
     * @return
     */
    MCouponTypeEntity getCouponTypeById(String id);
}
