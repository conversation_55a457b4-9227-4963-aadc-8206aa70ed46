package com.coupon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CouponTakenRecVo {
    private Long couponTypeId;
    private String title;
    private String subTitle;
    private String description;
    private int discountMethod;
    private String price;
    private String isActive;
    private String imagePath;
    private String codeImageGuid;
    private String codeImagePath;

    private String studentName;
    private String studentEmail;
    private String studentNeeaId;
    private String examDate;
    // 是否失效
    private boolean isLapse = false;
    // 推荐类型
    private int recommendedType;
    // 是否已经兑换
    private boolean isUsed = false;
    // 卷码
    private String code;
    private LocalDateTime validPeriodStart;
    private LocalDateTime validPeriodEnd;
    // 兑换时间
    private LocalDateTime redeemPeriodStart;
    private LocalDateTime redeemPeriodEnd;
}
