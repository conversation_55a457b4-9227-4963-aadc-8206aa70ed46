package com.partner.mapper;

import com.apps.api.entity.SystemMenuEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface AppSystemCenterMapper {

    /**
     * 查询菜单-PARTNER
     * @return
     */
    List<SystemMenuEntity> selectByPlatformCode();


    /**
     * 查询菜单-PARTNER
     * @param menuIds
     * @return
     */
    List<SystemMenuEntity> selectSystemMenuByIds(List< Long> menuIds);

    /**
     * 查询默认系统角色ID-PARTNER
     * @return
     */
    Long selectSystemPartnerDefaultRoleId();




}
