package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.MInstitutionRankingParamsDto;
import com.partner.service.MInstitutionRankingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(description = "MInstitutionRanking" , name = "小程序-院校大数据" )
@RestController
@RequestMapping("/MInstitutionRanking")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
@Inner(value = false)
public class MInstitutionRankingController {

    private final MInstitutionRankingService rankingService;

    @Operation(summary = "国家下拉框" , description = "国家下拉框" )
    @GetMapping("/getCountryCombox" )
    public R getCountryCombox(@ParameterObject MInstitutionRankingParamsDto dto){
        return R.ok(rankingService.getCountryCombox(dto));
    }

    @Operation(summary = "学校类型下拉框" , description = "学校类型下拉框" )
    @GetMapping("/getInstitutionTypeCombox" )
    public R getInstitutionTypeCombox(@ParameterObject MInstitutionRankingParamsDto dto){
        return R.ok(rankingService.getInstitutionTypeCombox(dto));
    }

    @Operation(summary = "专业下拉框" , description = "专业下拉框" )
    @GetMapping("/getCourseTypeGroupCombox" )
    public R getCourseTypeGroupCombox(@ParameterObject MInstitutionRankingParamsDto dto){
        return R.ok(rankingService.getCourseTypeGroupCombox(dto));
    }




    @Operation(summary = "排名分类" , description = "排名分类" )
    @GetMapping("/getRankingCombox" )
    public R getRankingCombox(@ParameterObject MInstitutionRankingParamsDto dto){
        return R.ok(rankingService.getRankingCombox(dto));
    }


    @Operation(summary = "院校排名分页列表" , description = "院校排名分页列表" )
    @SysLog("院校排名分页列表" )
    @GetMapping("/getPageInstitutionRanking" )
    public R getPageInstitutionRanking(Page page, @ParameterObject @Valid MInstitutionRankingParamsDto dto) {

        return R.ok(rankingService.getPageInstitutionRanking(page,dto));
    }
}
