package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.insurance.entity.CreditCard;
import com.insurance.entity.InsuranceOrder;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
public interface CreditCardService extends IService<CreditCard> {

    /**
     * 获取可用的信用卡
     *
     * @param price
     * @return
     */
    CreditCard getAvailableCreditCard(String orderNo, String secret);


    /**
     * 信用卡下单
     *
     * @param orderNo
     */
    void sendCreditCardOrderRequest(String orderNo);

    /**
     * 信用卡支付成功
     *
     * @param order
     */
    void CreditCardPaySuccess(InsuranceOrder order);


    /**
     * 信用卡支付失败
     *
     * @param order
     */
    void CreditCardPayFail(InsuranceOrder order);

    /**
     * 信用卡还款出账通知
     *
     * @param type
     */
    void sendRepaymentNotify(Integer type);

    /**
     * 信用卡额度提醒
     */
    void sendQuotaRemindNotify();

}
