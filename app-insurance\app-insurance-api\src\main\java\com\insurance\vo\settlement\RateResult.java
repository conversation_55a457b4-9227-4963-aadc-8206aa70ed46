package com.insurance.vo.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/6/18
 * @Version 1.0
 * @apiNote:汇率兑换结果
 */
@Data
public class RateResult {

    @Schema(description = "结果-不等于0都是失败")
    private Integer status;

    @Schema(description = "请求结果描述")
    private String msg;

    @Schema(description = "请求结果")
    private RateDetail result;

}
