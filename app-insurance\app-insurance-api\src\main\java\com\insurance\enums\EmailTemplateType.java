package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;


/**
 * 邮件模板类型枚举
 */
@Getter
@AllArgsConstructor
public enum EmailTemplateType {
    QUOTA_REMIND("QUOTA_REMIND", "额度不足提醒", Arrays.asList("name", "lastDigits", "limitAmount", "date")),
    FAIL_REMIND("FAIL_REMIND", "支付失败提醒", Arrays.asList("name", "orderNo", "payDate", "lastDigits", "date")),
    REPAYMENT_REMIND("REPAYMENT_REMIND", "还款提醒", Arrays.asList("name", "lastDigits", "date")),
    DISBURSEMENT_REMIND("DISBURSEMENT_REMIND", "出账提醒", Arrays.asList("name", "lastDigits", "date")),
    TRANSACTION_REMIND("TRANSACTION_REMIND", "交易提醒", Arrays.asList("name", "businessTypeName","lastDigits", "payDate", "tradeAmount", "date"));

    private final String code;
    private final String desc;
    private final List<String> requiredPlaceholders;

    public static EmailTemplateType getEnumByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
