package com.partner.dto.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class AmountConvetParamsDto {
    @Schema(description = "人民币金额")
    @NotNull(message = "人民币金额不能为空")
    private BigDecimal amountRbm;

    @Schema(description = "转换币种")
    @NotBlank(message = "转换币种不能为空")
    private String toCurrencyNum;


}
