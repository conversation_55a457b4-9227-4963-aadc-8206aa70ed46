package com.partner.dto.base;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
@Schema(description = "用户权限基础信息-传参入mapper")
public class UserInfoParams implements Serializable {

    @Schema(description = "用户Id")
    private Long userId;

    @Schema(description = "租户Id")
    private Long tenantId;

    @Schema(description = "平台ID")
    private Long platformId;

    @Schema(description = "partner用户Id")
    private Long partnerUserId;

    @Schema(description = "系统角色编码")
    private String roleCode;

    @Schema(description = "系统角色名称")
    private String roleName;

    @Schema(description = "代理ID")
    private Long agentId;

    @Schema(description = "公司ID")
    private Long companyId;
    @Schema(description = "角色权限")
    private Boolean roleFlag=false;
    @Schema(description = "个人角色权限")
    private Boolean roleTypeFlag=false;

    @Schema(description = "国别权限")
    private Boolean countryFlag=true;

    @Schema(description = "业务国别Ids")
    private List<Long> areaCountryIds;

    @Schema(description = "下级partner用户Id(包含自己)")
    private List<Long> levelPartnerUserIds;

    @Schema(description = "伙伴角色IDS")
    private List<Long> roleIds;

    @Schema(description = "伙伴角色名称")
    private List<String> roleNames;

    @Schema(description = "伙伴菜单权限标识")
    private List<String> permissionKeys;

    @Schema(description = "是否超级管理员0否1是")
    private Integer isAdmin = 0;

    public List<Long> getLevelPartnerUserIds() {
        if(ObjectUtil.isEmpty(levelPartnerUserIds) && ObjectUtil.isNotEmpty(partnerUserId)){
            levelPartnerUserIds=new ArrayList<>();
            levelPartnerUserIds.add(partnerUserId);
        }
        return levelPartnerUserIds;
    }



}
