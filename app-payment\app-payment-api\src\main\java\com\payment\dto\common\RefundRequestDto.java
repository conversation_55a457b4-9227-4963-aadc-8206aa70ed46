package com.payment.dto.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/17
 * @Version 1.0
 * @apiNote: 退款请求参数
 */
@Data
public class RefundRequestDto {

    @Schema(description = "小程序 AppId")
    private String appId;

    @Schema(description = "微信支付订单号")
    private String transactionId;

    @Schema(description = "商户退款单号")
    private String outRefundNo;

    @Schema(description = "订单总金额，单位：分")
    private Long totalFee;

    @Schema(description = "退款金额，单位：分")
    private Long refundFee;

    @Schema(description = "退款原因")
    private String reason;
}
