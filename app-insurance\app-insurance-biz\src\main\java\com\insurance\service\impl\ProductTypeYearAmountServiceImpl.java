package com.insurance.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.insurance.dto.order.InsurancePlanDTO;
import com.insurance.entity.ProductTypeYearAmount;
import com.insurance.enums.InsuranceTypeEnum;
import com.insurance.enums.ProductTypeEnum;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.mapper.ProductTypeYearAmountMapper;
import com.insurance.service.ProductTypeYearAmountService;
import com.insurance.utils.DateCalcUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * @apiNote: 保险金额配置表 服务实现类 (MyBatis-Plus QueryWrapper 版本)
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductTypeYearAmountServiceImpl extends ServiceImpl<ProductTypeYearAmountMapper, ProductTypeYearAmount> implements ProductTypeYearAmountService {

    private final ProductTypeYearAmountMapper productTypeYearAmountMapper;

    @Override
    public BigDecimal getDailyRate(InsurancePlanDTO insurancePlanDTO) {
        return null;
    }

    /**
     * 根据开始结束日期计算保单预估金额
     *
     * @param insurancePlanDTO
     * @return
     */
    @Override
    public BigDecimal calculateInsuranceEstimatedAmount(InsurancePlanDTO insurancePlanDTO) {
        InsuranceTypeEnum insuranceTypeEnum = InsuranceTypeEnum.getEnumByCode(insurancePlanDTO.getInsuranceType());
        if (insuranceTypeEnum == null) {
            log.error("保险单类型不存在");
            throw new InsuranceGlobalException("保险单类型不存在");
        }
        ProductTypeEnum productTypeEnum = ProductTypeEnum.getEnumByCode(insurancePlanDTO.getProductType());
        if (productTypeEnum == null) {
            log.error("产品类型不存在");
            throw new InsuranceGlobalException("产品类型不存在");
        }

        // 最终金额
        BigDecimal amount = null;
        switch (productTypeEnum) {
            case NIB:
                amount = this.calNIBEstimatedAmount(insurancePlanDTO);
                break;
            case NIB_LOGIN:
            case ALLIAN:
                break;
            default:
                return null;
        }

        return amount;
    }

    /**
     * 计算NIB预估金额
     *
     * @param insurancePlanDTO
     * @return
     */
    private BigDecimal calNIBEstimatedAmount(InsurancePlanDTO insurancePlanDTO) {
        // 获取保险计划的开始时间和结束时间
        Date startStr = insurancePlanDTO.getStartStr();
        Date endStr = insurancePlanDTO.getEndStr();

        // 查询天数
        long days = DateCalcUtils.daysBetween(startStr, endStr);
        long yearBetween = (long) Math.ceil(days / 365.0);

        Integer maxYear = this.productTypeYearAmountMapper.getMaxYear(insurancePlanDTO);
        // 每天的费用
        if (maxYear >= yearBetween) {
            BigDecimal dayPrice = getDayPrice(insurancePlanDTO, yearBetween);
            return dayPrice.multiply(BigDecimal.valueOf(days));
        } else {
            BigDecimal dayPrice = getDayPrice(insurancePlanDTO, maxYear);
            return dayPrice.multiply(BigDecimal.valueOf(days));
        }

    }

    private BigDecimal getDayPrice(InsurancePlanDTO insurancePlanDTO, long year) {
        LambdaQueryWrapper<ProductTypeYearAmount> productTypeYearAmountLambdaQueryWrapper = new LambdaQueryWrapper<ProductTypeYearAmount>()
                .eq(ProductTypeYearAmount::getInsuranceType, insurancePlanDTO.getInsuranceType())
                .eq(ProductTypeYearAmount::getProductType, insurancePlanDTO.getProductType())
                .eq(ProductTypeYearAmount::getPolicyYear, year);
        ProductTypeYearAmount productTypeYearAmount = this.getOne(productTypeYearAmountLambdaQueryWrapper);
        if (ObjectUtil.isNull(productTypeYearAmount)) {
            log.error("保险金额配置不存在");
            throw new InsuranceGlobalException("保险金额配置不存在");
        }
        BigDecimal insuranceAmount = productTypeYearAmount.getInsuranceAmount();
        BigDecimal maxDays = BigDecimal.valueOf(year).multiply(BigDecimal.valueOf(365));
        BigDecimal dayPrice = insuranceAmount.divide(maxDays, 2, RoundingMode.HALF_UP);
        return dayPrice;
    }


}