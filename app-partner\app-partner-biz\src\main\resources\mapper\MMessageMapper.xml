<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MMessageMapper">

    <sql id="Base_Column_List">
        message.id,
        message.fk_company_id,
        message.fk_platform_id,
        message.fk_platform_code,
        message.title,
        message.weight,
        message.jump_mode,
        message.jump_url,
        message.web_title,
        message.web_meta_description,
        message.start_time,
        message.end_time,
        message.remark,
        message.status,
        message.gmt_create,
        message.gmt_create_user,
        message.gmt_modified,
        message.gmt_modified_user
    </sql>

    <select id="searchMessage" resultType="com.partner.vo.MMessageVo">
        SELECT  <include refid="Base_Column_List" />
        FROM ais_platform_center.m_message message
        WHERE message.status=2
          AND message.fk_platform_id=#{fkPlatformId}
          AND message.end_time > now()
        ORDER BY weight DESC
        LIMIT 100
    </select>
</mapper>
