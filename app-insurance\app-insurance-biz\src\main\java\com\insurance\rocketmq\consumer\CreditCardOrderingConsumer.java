package com.insurance.rocketmq.consumer;

import com.alibaba.fastjson.JSON;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.service.CreditCardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:信用卡下单消费者
 */
@Component
@Slf4j
@RocketMQMessageListener(
        topic = "insurance_credit_card_ordering_topic",
        consumerGroup = "insurance_credit_card_ordering_topic_consumer_group",
        maxReconsumeTimes = 3,
        consumeMode = ConsumeMode.CONCURRENTLY
)
public class CreditCardOrderingConsumer implements RocketMQListener<OrderMsg> {

    @Autowired
    private CreditCardService creditCardService;

    @Override
    public void onMessage(OrderMsg orderMsg) {
        log.info("信用卡下单队列消费者收到消息，消息内容:{}", JSON.toJSONString(orderMsg));
        creditCardService.sendCreditCardOrderRequest(orderMsg.getOrderNo());
    }
}
