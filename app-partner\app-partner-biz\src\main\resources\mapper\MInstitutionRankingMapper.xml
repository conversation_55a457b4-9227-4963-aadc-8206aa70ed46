<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MInstitutionRankingMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MInstitutionRankingEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="rankingType" column="ranking_type" jdbcType="INTEGER"/>
            <result property="rankingMin" column="ranking_min" jdbcType="INTEGER"/>
            <result property="rankingMax" column="ranking_max" jdbcType="INTEGER"/>
            <result property="rankingNote" column="ranking_note" jdbcType="VARCHAR"/>
            <result property="fkInstitutionId" column="fk_institution_id" jdbcType="BIGINT"/>
            <result property="institutionName" column="institution_name" jdbcType="VARCHAR"/>
            <result property="institutionNameChn" column="institution_name_chn" jdbcType="VARCHAR"/>
            <result property="institutionNature" column="institution_nature" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="areaCountryName" column="area_country_name" jdbcType="VARCHAR"/>
            <result property="fkCourseTypeGroupId" column="fk_course_type_group_id" jdbcType="BIGINT"/>
            <result property="courseTypeGroupName" column="course_type_group_name" jdbcType="VARCHAR"/>
            <result property="courseTypeGroupNameChn" column="course_type_group_name_chn" jdbcType="VARCHAR"/>
            <result property="fkCourseTypeId" column="fk_course_type_id" jdbcType="BIGINT"/>
            <result property="courseTypeName" column="course_type_name" jdbcType="VARCHAR"/>
            <result property="courseTypeNameChn" column="course_type_name_chn" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>



    <select id="getCountryCombox" resultType="com.partner.vo.combox.CountryCombox">
        SELECT DISTINCT uAreaCountry.id AS areaCountryId,
                        uAreaCountry.name_chn AS areaCountryName
        FROM ais_institution_center.m_institution_ranking institutionRanking
                 INNER JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id = institutionRanking.fk_area_country_id
        ORDER BY uAreaCountry.view_order DESC
        LIMIT 100
    </select>
    <select id="getRankingCombox" resultType="com.partner.vo.combox.RankingTypeCombox">
        SELECT DISTINCT institutionRanking.ranking_type
            FROM ais_institution_center.m_institution_ranking institutionRanking order by ranking_type
        LIMIT 30
    </select>


    <select id="getPageInstitutionRanking" resultType="com.partner.vo.MInstitutionRankingVo">
        SELECT institutionRanking.year,
               institutionRanking.ranking_min,
               institutionRanking.institution_name,
               institutionRanking.institution_name_chn,
               institutionRanking.area_country_name,
                uAreaCountry.name_chn AS areaCountryNameChn,
                (SELECT CONCAT(#{query.mMageAddress}, institutionFile.file_key)
                FROM ais_file_center.m_file_institution institutionFile
                INNER JOIN
                ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
                WHERE sAttached.fk_table_name = 'm_institution'
                AND sAttached.type_key = 'institution_logo'
                AND sAttached.fk_table_id = institutionRanking.fk_institution_id
                LIMIT 1) AS fileKey,
                (SELECT CONCAT(#{query.mMageAddress}, institutionFile.file_key)
                FROM ais_file_center.m_file_institution institutionFile
                INNER JOIN
                ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
                WHERE sAttached.fk_table_name = 'm_institution'
                AND sAttached.type_key = 'institution_pic'
                AND sAttached.fk_table_id = institutionRanking.fk_institution_id
                LIMIT 1) AS institutionPic,
                (SELECT CONCAT(#{query.mMageAddress}, institutionFile.file_key)
                FROM ais_file_center.m_file_institution institutionFile
                INNER JOIN
                ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
                WHERE sAttached.fk_table_name = 'm_institution'
                AND sAttached.type_key = 'institution_cover'
                AND sAttached.fk_table_id = institutionRanking.fk_institution_id
                LIMIT 1) AS fileKeyCover
        FROM ais_institution_center.m_institution_ranking institutionRanking
             INNER JOIN ais_institution_center.m_institution institution ON institution.id=institutionRanking.fk_institution_id
             LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id =institutionRanking.fk_area_country_id
        WHERE 1=1
          <if test="query.year!=null">
              AND
              institutionRanking.year=#{query.year}
          </if>
          <if test="query.rankingType!=null">
              AND
              institutionRanking.ranking_type=#{query.rankingType}
          </if>
          <if test="query.institutionName != null and query.institutionName != '' ">
            AND
            (
              position(#{query.institutionName,jdbcType=VARCHAR} IN institutionRanking.institution_name)
                  OR
              position(#{query.institutionName,jdbcType=VARCHAR} IN institutionRanking.institution_name_chn)
              )

          </if>


        <if test="query.areaCountryId!=null">
            AND
            institutionRanking.fk_area_country_id=#{query.areaCountryId}
        </if>


        <if test="query.institutionTypeId!=null">
            AND
            institution.fk_institution_type_id=#{query.institutionTypeId}
        </if>



        <if test="query.courseCypeGroupId!=null">
            AND
            institutionRanking.fk_course_type_group_id=#{query.courseCypeGroupId}
        </if>
        ORDER BY  ranking_min
    </select>
    <select id="getInstitutionTypeCombox" resultType="com.partner.vo.combox.InstitutionTypeCombox">
        SELECT DISTINCT uInstitutionType.id AS institutionTypeId,uInstitutionType.type_name_chn AS  institutionTypeName
        FROM ais_institution_center.m_institution_ranking institutionRanking
                 INNER JOIN ais_institution_center.m_institution institution  ON institution.id=institutionRanking.fk_institution_id
                 INNER JOIN ais_institution_center.u_institution_type uInstitutionType  ON   uInstitutionType.id=institution.fk_institution_type_id

        ORDER BY  view_order DESC
    </select>
    <select id="getCourseTypeGroupCombox" resultType="com.partner.vo.combox.CourseTypeGroupCombox">

        select DISTINCT courseTypeGroup.id AS courseCypeGroupId,
                        courseTypeGroup.type_group_name AS typeGroupName,
                        courseTypeGroup.type_group_name_chn AS typeGroupNameChn
        FROM ais_institution_center.m_institution_ranking institutionRanking
        INNER JOIN ais_institution_center.u_course_type_group courseTypeGroup  ON courseTypeGroup.id=institutionRanking.fk_course_type_group_id
        ORDER BY courseTypeGroup.view_order


    </select>


</mapper>
