package com.partner.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置
 *
 * <AUTHOR>
 * @date 2023/03/07
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file")
public class FileProperties {

    /**
     * 公共桶（默认值）
     */
    private String publicBucketName = "hti-ais-images-dev-1301376564";

    /**
     * 私有桶（默认值）
     */
    private String privateBucketName = "hti-ais-files-dev-1301376564";

    /**
     * 文件保存目录-公有桶
     */
    private String catalogue = "partner";

    /**
     * 私密桶保存目录-私有桶
     */
    private String privateCatalogue = "appendix";

    /**
     * OSS配置
     */
    private Oss oss = new Oss();

    @Data
    public static class Oss {
        /**
         * 启用 OSS（默认 true）
         */
        private boolean enable = true;

        /**
         * 使用路径风格访问（默认 false）
         */
        private boolean pathStyleAccess = false;

        /**
         * 访问域名
         */
        private String endpoint = "cos.ap-shanghai.myqcloud.com";

        /**
         * 访问密钥 AccessKey
         */
        private String accessKey = "AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza";

        /**
         * 访问密钥 SecretKey
         */
        private String secretKey = "lQ8oRyOk12uETcyCGILG0b8sgchpttiT";
    }
}
